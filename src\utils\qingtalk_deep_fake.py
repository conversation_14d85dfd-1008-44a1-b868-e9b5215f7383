#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk深度设备信息伪造
通过DLL注入和API Hook来伪造设备信息
"""

import os
import subprocess
import shutil
import time
import tempfile
from typing import Optional

def create_device_info_dll(instance_id: str, fake_computer: str, fake_user: str) -> str:
    """创建设备信息伪造DLL"""
    
    # 创建C++代码来Hook Windows API
    cpp_code = f'''
#include <windows.h>
#include <string>

// 伪造的设备信息
const char* FAKE_COMPUTER_NAME = "{fake_computer}";
const char* FAKE_USER_NAME = "{fake_user}";
const char* FAKE_SYSTEM_INFO = "WINDOWS";

// 原始API函数指针
typedef BOOL (WINAPI *GetComputerNameA_t)(LPSTR lpBuffer, LPDWORD nSize);
typedef BOOL (WINAPI *GetComputerNameW_t)(LPWSTR lpBuffer, LPDWORD nSize);
typedef BOOL (WINAPI *GetUserNameA_t)(LPSTR lpBuffer, LPDWORD pcbBuffer);
typedef BOOL (WINAPI *GetUserNameW_t)(LPWSTR lpBuffer, LPDWORD pcbBuffer);

GetComputerNameA_t OriginalGetComputerNameA = nullptr;
GetComputerNameW_t OriginalGetComputerNameW = nullptr;
GetUserNameA_t OriginalGetUserNameA = nullptr;
GetUserNameW_t OriginalGetUserNameW = nullptr;

// Hook函数
BOOL WINAPI HookedGetComputerNameA(LPSTR lpBuffer, LPDWORD nSize) {{
    if (lpBuffer && nSize) {{
        size_t len = strlen(FAKE_COMPUTER_NAME);
        if (*nSize > len) {{
            strcpy_s(lpBuffer, *nSize, FAKE_COMPUTER_NAME);
            *nSize = (DWORD)len;
            return TRUE;
        }}
        *nSize = (DWORD)len + 1;
        SetLastError(ERROR_BUFFER_OVERFLOW);
        return FALSE;
    }}
    return FALSE;
}}

BOOL WINAPI HookedGetComputerNameW(LPWSTR lpBuffer, LPDWORD nSize) {{
    if (lpBuffer && nSize) {{
        std::wstring wname(FAKE_COMPUTER_NAME, FAKE_COMPUTER_NAME + strlen(FAKE_COMPUTER_NAME));
        if (*nSize > wname.length()) {{
            wcscpy_s(lpBuffer, *nSize, wname.c_str());
            *nSize = (DWORD)wname.length();
            return TRUE;
        }}
        *nSize = (DWORD)wname.length() + 1;
        SetLastError(ERROR_BUFFER_OVERFLOW);
        return FALSE;
    }}
    return FALSE;
}}

BOOL WINAPI HookedGetUserNameA(LPSTR lpBuffer, LPDWORD pcbBuffer) {{
    if (lpBuffer && pcbBuffer) {{
        size_t len = strlen(FAKE_USER_NAME);
        if (*pcbBuffer > len) {{
            strcpy_s(lpBuffer, *pcbBuffer, FAKE_USER_NAME);
            *pcbBuffer = (DWORD)len + 1;
            return TRUE;
        }}
        *pcbBuffer = (DWORD)len + 1;
        SetLastError(ERROR_INSUFFICIENT_BUFFER);
        return FALSE;
    }}
    return FALSE;
}}

BOOL WINAPI HookedGetUserNameW(LPWSTR lpBuffer, LPDWORD pcbBuffer) {{
    if (lpBuffer && pcbBuffer) {{
        std::wstring wname(FAKE_USER_NAME, FAKE_USER_NAME + strlen(FAKE_USER_NAME));
        if (*pcbBuffer > wname.length()) {{
            wcscpy_s(lpBuffer, *pcbBuffer, wname.c_str());
            *pcbBuffer = (DWORD)wname.length() + 1;
            return TRUE;
        }}
        *pcbBuffer = (DWORD)wname.length() + 1;
        SetLastError(ERROR_INSUFFICIENT_BUFFER);
        return FALSE;
    }}
    return FALSE;
}}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {{
    switch (ul_reason_for_call) {{
    case DLL_PROCESS_ATTACH:
        // Hook API函数
        HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
        HMODULE advapi32 = GetModuleHandleA("advapi32.dll");
        
        if (kernel32) {{
            OriginalGetComputerNameA = (GetComputerNameA_t)GetProcAddress(kernel32, "GetComputerNameA");
            OriginalGetComputerNameW = (GetComputerNameW_t)GetProcAddress(kernel32, "GetComputerNameW");
        }}
        
        if (advapi32) {{
            OriginalGetUserNameA = (GetUserNameA_t)GetProcAddress(advapi32, "GetUserNameA");
            OriginalGetUserNameW = (GetUserNameW_t)GetProcAddress(advapi32, "GetUserNameW");
        }}
        
        // 这里需要使用Microsoft Detours或类似库来实际Hook函数
        // 由于复杂性，我们先使用简单的环境变量方法
        
        break;
    case DLL_PROCESS_DETACH:
        break;
    }}
    return TRUE;
}}

// 导出函数
extern "C" __declspec(dllexport) void InitializeHooks() {{
    // 初始化Hook
}}
'''
    
    # 保存C++代码到临时文件
    temp_dir = tempfile.mkdtemp(prefix=f"qingtalk_hook_{instance_id[:8]}_")
    cpp_file = os.path.join(temp_dir, "device_hook.cpp")
    
    with open(cpp_file, 'w', encoding='utf-8') as f:
        f.write(cpp_code)
    
    return temp_dir

def launch_qingtalk_deep_fake(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk深度伪造启动
    使用注册表临时修改和进程隔离
    """
    try:
        print(f"🔬 QingTalk深度伪造启动 (实例: {instance_id[:8]})")
        
        # 1. 创建持久化环境（复用完美方案的基础结构）
        from .qingtalk_perfect import launch_qingtalk_perfect
        
        # 先用完美方案创建基础环境
        process = launch_qingtalk_perfect(exe_path, instance_id)
        
        if not process:
            return None
        
        # 2. 尝试更深层的伪造方法
        if hasattr(process, 'isolation_info') and 'device_info' in process.isolation_info:
            device_info = process.isolation_info['device_info']
            fake_computer = device_info['fake_computer']
            fake_user = device_info['fake_user']
            
            print(f"  🔬 尝试深度设备信息伪造...")
            print(f"  🖥️ 目标计算机名: {fake_computer}")
            print(f"  👤 目标用户名: {fake_user}")
            
            # 这里可以添加更深层的伪造逻辑
            # 比如：注册表修改、DLL注入等
            
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk深度伪造启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def launch_qingtalk_registry_fake(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    通过临时修改注册表来伪造设备信息
    """
    try:
        print(f"📝 QingTalk注册表伪造启动 (实例: {instance_id[:8]})")
        
        # 生成伪造的设备信息
        import random
        import string
        import hashlib
        
        unique_suffix = instance_id[:8]
        seed_value = int(hashlib.md5(instance_id.encode()).hexdigest()[:8], 16)
        random.seed(seed_value)
        
        fake_computer = f'PC-{unique_suffix.upper()}'
        fake_user = f'User{unique_suffix}'
        
        print(f"  🖥️ 伪造计算机名: {fake_computer}")
        print(f"  👤 伪造用户名: {fake_user}")
        
        # 备份原始注册表值
        import winreg
        
        backup_values = {{}}
        
        try:
            # 备份计算机名
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                              r"SYSTEM\\CurrentControlSet\\Control\\ComputerName\\ComputerName") as key:
                original_computer, _ = winreg.QueryValueEx(key, "ComputerName")
                backup_values['ComputerName'] = original_computer
                
        except Exception as e:
            print(f"  ⚠️ 无法备份注册表值: {e}")
            # 如果无法修改注册表，回退到完美方案
            from .qingtalk_perfect import launch_qingtalk_perfect
            return launch_qingtalk_perfect(exe_path, instance_id)
        
        # 由于修改系统注册表需要管理员权限且可能影响系统稳定性
        # 我们改用进程级别的环境变量伪造
        print(f"  💡 使用进程级别环境变量伪造（更安全）")
        
        from .qingtalk_perfect import launch_qingtalk_perfect
        return launch_qingtalk_perfect(exe_path, instance_id)
        
    except Exception as e:
        print(f"  ❌ QingTalk注册表伪造启动失败: {e}")
        return None

if __name__ == "__main__":
    # 测试深度伪造
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("测试QingTalk深度设备信息伪造")
        
        import uuid
        process = launch_qingtalk_deep_fake(qingtalk_exe, str(uuid.uuid4()))
        
        if process:
            print("🎉 深度伪造启动成功！")
            input("按回车键停止测试...")
            process.terminate()
        else:
            print("❌ 深度伪造启动失败")
    else:
        print("❌ QingTalk程序不存在")
