#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速UI测试脚本
专门验证修改后系统设置中的设备名称是否立即更新
"""

import ctypes
from ctypes import wintypes
import os
import sys
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_computer_name_api():
    """通过API获取计算机名"""
    try:
        size = wintypes.DWORD(0)
        ctypes.windll.kernel32.GetComputerNameExW(0, None, ctypes.byref(size))
        buffer = ctypes.create_unicode_buffer(size.value)
        success = ctypes.windll.kernel32.GetComputerNameExW(0, buffer, ctypes.byref(size))
        if success:
            return buffer.value
        return "API_Failed"
    except:
        return "API_Error"

def open_system_about():
    """打开系统关于页面"""
    try:
        subprocess.Popen(['ms-settings:about'], shell=True)
        return True
    except:
        try:
            subprocess.Popen(['control', 'system'], shell=True)
            return True
        except:
            return False

def main():
    """主函数"""
    print("🚀 快速UI测试")
    print("=" * 40)
    
    # 检查管理员权限
    if not ctypes.windll.shell32.IsUserAnAdmin():
        print("❌ 需要管理员权限")
        return
    
    # 显示当前信息
    current_env = os.environ.get('COMPUTERNAME', 'Unknown')
    current_api = get_computer_name_api()
    
    print(f"📋 当前信息:")
    print(f"  环境变量: {current_env}")
    print(f"  API返回: {current_api}")
    
    # 导入并执行修改
    try:
        from src.utils.device_modifier import DeviceModifier
        
        modifier = DeviceModifier()
        
        print(f"\n🔧 执行修改...")
        success = modifier.modify_computer_name_only()
        
        if success:
            print(f"✅ 修改成功")
            
            # 等待生效
            print(f"⏳ 等待生效...")
            time.sleep(5)
            
            # 检查变化
            new_env = os.environ.get('COMPUTERNAME', 'Unknown')
            new_api = get_computer_name_api()
            
            print(f"\n📋 修改后信息:")
            print(f"  环境变量: {new_env}")
            print(f"  API返回: {new_api}")
            
            # 分析变化
            if new_env != current_env:
                print(f"✅ 环境变量已更改")
            else:
                print(f"⚪ 环境变量未变化")
            
            if new_api != current_api:
                print(f"✅ API返回已更改")
            else:
                print(f"⚪ API返回未变化")
            
            # 打开系统设置
            print(f"\n🖥️ 打开系统设置...")
            if open_system_about():
                print(f"✅ 系统设置已打开")
                print(f"💡 请检查设备名称是否显示: {new_env}")
            else:
                print(f"❌ 无法打开系统设置")
            
            # 等待用户确认
            input(f"\n按回车键恢复...")
            
            # 恢复
            print(f"🔄 恢复中...")
            modifier.restore_device_info()
            
            print(f"✅ 恢复完成")
            
        else:
            print(f"❌ 修改失败")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
