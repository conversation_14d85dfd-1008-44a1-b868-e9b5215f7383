import ctypes
from ctypes import wintypes
import winreg
import wmi
import os
import subprocess

# GetComputerNameEx 支持的名称类型
class ComputerNameFormat:
    ComputerNameNetBIOS = 0
    ComputerNameDnsHostname = 1
    ComputerNameDnsDomain = 2
    ComputerNameDnsFullyQualified = 3
    ComputerNamePhysicalNetBIOS = 4
    ComputerNamePhysicalDnsHostname = 5
    ComputerNamePhysicalDnsDomain = 6
    ComputerNamePhysicalDnsFullyQualified = 7

def get_computer_name_ex(name_type):
    size = wintypes.DWORD(0)
    ctypes.windll.kernel32.GetComputerNameExW(name_type, None, ctypes.byref(size))
    buffer = ctypes.create_unicode_buffer(size.value)
    success = ctypes.windll.kernel32.GetComputerNameExW(name_type, buffer, ctypes.byref(size))
    if success:
        return buffer.value
    return None

def get_registry_names():
    keys = {
        "ActiveComputerName": r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName",
        "ComputerName": r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName",
        "Tcpip_Parameters": r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters",
        "WindowsNT_CurrentVersion": r"SOFTWARE\Microsoft\Windows NT\CurrentVersion"
    }
    names = {}
    for key_name, reg_path in keys.items():
        try:
            reg = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
            for val_name in ["ComputerName", "Hostname", "RegisteredOwner", "RegisteredOrganization", "ProductName"]:
                try:
                    val, _ = winreg.QueryValueEx(reg, val_name)
                    names[f"{key_name}_{val_name}"] = val
                except FileNotFoundError:
                    pass
            winreg.CloseKey(reg)
        except FileNotFoundError:
            pass
    return names

def get_wmi_info():
    c = wmi.WMI()
    info = {}
    try:
        for sys in c.Win32_ComputerSystem():
            info["WMI_ComputerSystem_Name"] = sys.Name
            info["WMI_ComputerSystem_Domain"] = sys.Domain
            info["WMI_ComputerSystem_Workgroup"] = sys.Workgroup
            info["WMI_ComputerSystem_Manufacturer"] = sys.Manufacturer
            info["WMI_ComputerSystem_Model"] = sys.Model
        for os in c.Win32_OperatingSystem():
            info["WMI_OS_Caption"] = os.Caption
            info["WMI_OS_CSDVersion"] = os.CSDVersion
            info["WMI_OS_InstallDate"] = os.InstallDate
        for bios in c.Win32_BIOS():
            info["WMI_BIOS_SerialNumber"] = bios.SerialNumber.strip()
            info["WMI_BIOS_Manufacturer"] = bios.Manufacturer
            info["WMI_BIOS_Version"] = bios.Version
        for csprod in c.Win32_ComputerSystemProduct():
            info["WMI_ComputerSystemProduct_UUID"] = csprod.UUID
    except Exception as e:
        info["WMI_Error"] = str(e)
    # 网络适配器 DNS 主机名 和 DHCP 主机名
    try:
        for nic in c.Win32_NetworkAdapterConfiguration(IPEnabled=True):
            key = f"WMI_NIC_{nic.Description}"
            info[key] = {
                "DNSHostName": nic.DNSHostName,
                "DHCPHostName": getattr(nic, "DHCPServer", None)
            }
    except:
        pass
    return info

def get_env_vars():
    keys = ["COMPUTERNAME", "USERNAME", "USERDOMAIN"]
    return {k: os.environ.get(k) for k in keys}

def get_netbios_name():
    try:
        output = subprocess.check_output("nbtstat -n", shell=True, text=True, encoding="utf-8")
        # 解析 NetBIOS 名称行，一般包含 <00> UNIQUE  PC-NAME
        lines = output.splitlines()
        names = []
        for line in lines:
            if "<00>" in line and "UNIQUE" in line:
                parts = line.split()
                if len(parts) >= 1:
                    names.append(parts[0])
        return names
    except Exception as e:
        return [f"Error: {e}"]

if __name__ == "__main__":
    print("=== GetComputerNameEx 各类型名称 ===")
    for t in range(8):
        name = get_computer_name_ex(t)
        print(f"{t}: {name}")

    print("\n=== 注册表相关信息 ===")
    reg_names = get_registry_names()
    for k, v in reg_names.items():
        print(f"{k}: {v}")

    print("\n=== WMI 信息 ===")
    wmi_info = get_wmi_info()
    for k, v in wmi_info.items():
        print(f"{k}: {v}")

    print("\n=== 环境变量 ===")
    env_vars = get_env_vars()
    for k, v in env_vars.items():
        print(f"{k}: {v}")

    print("\n=== NetBIOS 名称 (nbtstat -n) ===")
    netbios_names = get_netbios_name()
    for name in netbios_names:
        print(f"- {name}")
