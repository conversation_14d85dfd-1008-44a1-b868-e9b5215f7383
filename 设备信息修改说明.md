# 设备信息修改功能说明

## 功能概述

本项目已将设备信息修改功能从自动执行改为手动触发，用户可以选择是否修改设备信息。

## 主要改动

### 1. 新增独立的设备修改模块
- **文件位置**: `src/utils/device_modifier.py`
- **功能**: 提供设备信息修改和恢复功能
- **特点**: 单例模式，确保全局只有一个修改器实例
- **新增功能**:
  - 持久化备份文件 (`device_backup.json`)
  - 环境变量备份和恢复
  - 详细的恢复统计信息
  - 增强的错误处理

### 2. 界面改进
- **新增按钮**: 在工具栏添加了"修改信息"按钮
- **动态按钮**: 修改后会显示"恢复信息"按钮
- **状态显示**: 按钮状态会根据设备信息修改状态动态更新

### 3. 启动流程优化
- **QingTalk终极模式**: 不再自动修改设备信息，而是检查是否已修改
- **用户选择**: 用户可以选择是否修改设备信息
- **正常多开**: 不修改设备信息时仍可正常使用多开功能

## 使用方法

### 1. 正常多开（不修改设备信息）
1. 启动程序
2. 直接创建和启动实例
3. 使用原始设备信息进行多开

### 2. 修改设备信息后多开
1. 启动程序（需要管理员权限）
2. 点击"修改信息"按钮
3. 确认修改操作
4. 等待修改完成
5. 创建和启动实例（使用新的设备信息）

### 3. 恢复原始设备信息
1. 点击"恢复信息"按钮
2. 确认恢复操作
3. 等待恢复完成

## 权限要求

- **正常多开**: 不需要管理员权限
- **修改设备信息**: 需要管理员权限
- **恢复设备信息**: 需要管理员权限

## 修改内容

设备信息修改包括以下内容：

### 1. 注册表修改
- 计算机名相关注册表项
- 硬件信息注册表项
- 系统信息注册表项

### 2. 环境变量修改
- COMPUTERNAME
- USERNAME
- USERDOMAIN
- LOGONSERVER
- 其他网络相关环境变量

### 3. 临时文件创建
- 伪造的系统文件
- 网络配置文件

## 安全特性

### 1. 自动备份
- 修改前自动备份原始值
- 支持完整恢复
- **持久化备份**: 备份数据保存到 `device_backup.json` 文件
- **跨会话恢复**: 即使程序重启也能恢复之前的修改

### 2. 自动恢复
- 程序关闭时自动恢复
- 手动恢复功能
- **详细统计**: 显示恢复的注册表项、环境变量、临时文件数量

### 3. 错误处理
- 修改失败时自动回滚
- 详细的错误信息提示
- **分类错误**: 区分权限错误、文件不存在等不同类型的错误

## 测试方法

### 1. 使用测试脚本
```bash
# 以管理员身份运行（完整功能测试）
python test_device_modifier.py

# 普通权限运行（备份恢复功能测试）
python test_backup_restore.py
```

### 2. 手动测试
1. 以管理员身份启动程序
2. 点击"修改信息"按钮
3. 启动QingTalk实例
4. 检查QingTalk显示的设备信息
5. 点击"恢复信息"按钮
6. 验证设备信息是否恢复

## 注意事项

1. **管理员权限**: 修改设备信息需要管理员权限
2. **系统影响**: 修改会影响整个系统，不仅仅是QingTalk
3. **自动恢复**: 程序会在关闭时自动恢复，但建议手动恢复
4. **备份重要**: 修改前会自动备份，请不要手动删除备份数据
5. **重启影响**: 某些修改可能需要重启才能完全生效

## 故障排除

### 1. 修改失败
- 检查是否有管理员权限
- 检查注册表是否被其他程序锁定
- 查看控制台错误信息

### 2. 恢复失败
- 重新以管理员身份运行程序
- 手动点击"恢复信息"按钮
- 如果仍然失败，可能需要手动恢复注册表

### 3. 按钮状态异常
- 重启程序
- 检查设备修改模块是否正常加载

### 4. 备份文件问题
- **备份文件位置**: `device_backup.json`（在程序根目录）
- **手动清理**: 如果程序异常退出，可以手动删除备份文件
- **恢复失败**: 检查备份文件是否存在和格式是否正确

### 5. 恢复显示"0项"问题
- **已修复**: 新版本已修复恢复计数问题
- **详细统计**: 现在会显示注册表、环境变量、临时文件的详细恢复情况
- **持久化备份**: 即使程序重启也能正确恢复

## 技术细节

### 1. 单例模式
设备修改器使用单例模式，确保全局只有一个实例，避免冲突。

### 2. 线程安全
UI操作在主线程，设备修改在后台线程，通过`root.after()`确保线程安全。

### 3. 状态管理
通过`_is_modified`标志跟踪修改状态，确保状态一致性。

### 4. 资源管理
自动管理临时文件和注册表备份，确保资源正确释放。
