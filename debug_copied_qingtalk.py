#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试复制的QingTalk目录启动
"""

import os
import subprocess
import time

def test_copied_qingtalk():
    """测试复制的QingTalk目录启动"""
    copied_exe = r"D:\text\py\duokai\persistent_data\instance_e0b6d2d7\QingTalk\QingTalk.exe"
    
    if not os.path.exists(copied_exe):
        print(f"❌ 复制的QingTalk不存在: {copied_exe}")
        return False
    
    print("🧪 测试复制的QingTalk目录启动")
    print("-" * 40)
    print(f"程序路径: {copied_exe}")
    print(f"工作目录: {os.path.dirname(copied_exe)}")
    
    try:
        # 使用复制的QingTalk，不修改环境变量
        process = subprocess.Popen(
            copied_exe,
            cwd=os.path.dirname(copied_exe),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            poll_result = process.poll()
            if poll_result is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {poll_result})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_copied_qingtalk_with_original_env():
    """测试复制的QingTalk，使用原始环境变量"""
    copied_exe = r"D:\text\py\duokai\persistent_data\instance_e0b6d2d7\QingTalk\QingTalk.exe"
    
    if not os.path.exists(copied_exe):
        print(f"❌ 复制的QingTalk不存在: {copied_exe}")
        return False
    
    print("\n🧪 测试复制的QingTalk（原始环境变量）")
    print("-" * 40)
    
    try:
        # 使用原始环境变量
        env = os.environ.copy()
        
        process = subprocess.Popen(
            copied_exe,
            env=env,
            cwd=os.path.dirname(copied_exe),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            poll_result = process.poll()
            if poll_result is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {poll_result})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_original_qingtalk_from_copied_dir():
    """从复制的目录启动原始QingTalk"""
    original_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    copied_dir = r"D:\text\py\duokai\persistent_data\instance_e0b6d2d7\QingTalk"
    
    if not os.path.exists(original_exe):
        print(f"❌ 原始QingTalk不存在: {original_exe}")
        return False
    
    if not os.path.exists(copied_dir):
        print(f"❌ 复制目录不存在: {copied_dir}")
        return False
    
    print("\n🧪 测试从复制目录启动原始QingTalk")
    print("-" * 40)
    
    try:
        # 从复制的目录启动原始exe
        process = subprocess.Popen(
            original_exe,
            cwd=copied_dir,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            poll_result = process.poll()
            if poll_result is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {poll_result})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("复制QingTalk目录启动测试")
    print("=" * 60)
    
    tests = [
        ("复制QingTalk启动", test_copied_qingtalk),
        ("复制QingTalk（原始环境）", test_copied_qingtalk_with_original_env),
        ("原始exe从复制目录启动", test_original_qingtalk_from_copied_dir),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count > 0:
        print("\n💡 分析:")
        if results.get("复制QingTalk启动", False):
            print("- 复制的QingTalk可以正常启动")
        if results.get("复制QingTalk（原始环境）", False):
            print("- 环境变量不是问题")
        if results.get("原始exe从复制目录启动", False):
            print("- 工作目录不是问题，可以从复制目录启动原始exe")
    else:
        print("\n💡 分析:")
        print("- 所有测试都失败，可能是复制的目录有问题")
        print("- 建议检查复制的文件是否完整")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
