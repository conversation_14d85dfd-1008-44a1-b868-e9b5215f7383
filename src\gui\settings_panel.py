#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置面板
程序配置和设置
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os

class SettingsPanel:
    def __init__(self, parent, config_manager):
        self.parent = parent
        self.config_manager = config_manager
        
        self.create_widgets()
        self.load_settings()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个设置页面
        self.create_general_settings()
        self.create_sandbox_settings()
        self.create_proxy_settings()
        self.create_advanced_settings()
        
        # 创建按钮框架
        self.create_buttons()
    
    def create_general_settings(self):
        """创建常规设置页面"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="常规设置")
        
        # 滚动框架
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 窗口设置
        window_group = ttk.LabelFrame(scrollable_frame, text="窗口设置", padding=10)
        window_group.pack(fill=tk.X, pady=5)
        
        ttk.Label(window_group, text="窗口宽度:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.window_width = ttk.Entry(window_group, width=10)
        self.window_width.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Label(window_group, text="窗口高度:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.window_height = ttk.Entry(window_group, width=10)
        self.window_height.grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # 实例设置
        instance_group = ttk.LabelFrame(scrollable_frame, text="实例设置", padding=10)
        instance_group.pack(fill=tk.X, pady=5)
        
        ttk.Label(instance_group, text="最大实例数:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.max_instances = ttk.Entry(instance_group, width=10)
        self.max_instances.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        self.auto_save = tk.BooleanVar()
        ttk.Checkbutton(
            instance_group, 
            text="自动保存配置", 
            variable=self.auto_save
        ).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        # 主题设置
        theme_group = ttk.LabelFrame(scrollable_frame, text="主题设置", padding=10)
        theme_group.pack(fill=tk.X, pady=5)
        
        ttk.Label(theme_group, text="主题:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.theme = ttk.Combobox(theme_group, values=['default', 'dark', 'light'])
        self.theme.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_sandbox_settings(self):
        """创建沙盒设置页面"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="沙盒设置")
        
        main_frame = ttk.Frame(frame, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 沙盒功能
        sandbox_group = ttk.LabelFrame(main_frame, text="沙盒功能", padding=10)
        sandbox_group.pack(fill=tk.X, pady=5)
        
        self.enable_isolation = tk.BooleanVar()
        ttk.Checkbutton(
            sandbox_group, 
            text="启用进程隔离", 
            variable=self.enable_isolation
        ).pack(anchor=tk.W, pady=2)
        
        self.enable_registry_isolation = tk.BooleanVar()
        ttk.Checkbutton(
            sandbox_group, 
            text="启用注册表隔离", 
            variable=self.enable_registry_isolation
        ).pack(anchor=tk.W, pady=2)
        
        self.enable_filesystem_isolation = tk.BooleanVar()
        ttk.Checkbutton(
            sandbox_group, 
            text="启用文件系统隔离", 
            variable=self.enable_filesystem_isolation
        ).pack(anchor=tk.W, pady=2)
        
        # 临时目录设置
        temp_group = ttk.LabelFrame(main_frame, text="临时目录", padding=10)
        temp_group.pack(fill=tk.X, pady=5)
        
        ttk.Label(temp_group, text="临时目录:").pack(anchor=tk.W, pady=2)
        temp_frame = ttk.Frame(temp_group)
        temp_frame.pack(fill=tk.X, pady=2)
        
        self.temp_dir = ttk.Entry(temp_frame)
        self.temp_dir.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(
            temp_frame, 
            text="浏览", 
            command=self.browse_temp_dir
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 日志设置
        log_group = ttk.LabelFrame(main_frame, text="日志设置", padding=10)
        log_group.pack(fill=tk.X, pady=5)
        
        ttk.Label(log_group, text="日志级别:").pack(anchor=tk.W, pady=2)
        self.log_level = ttk.Combobox(
            log_group, 
            values=['DEBUG', 'INFO', 'WARNING', 'ERROR']
        )
        self.log_level.pack(anchor=tk.W, pady=2)
    
    def create_proxy_settings(self):
        """创建代理设置页面"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="代理设置")
        
        main_frame = ttk.Frame(frame, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 代理功能
        proxy_group = ttk.LabelFrame(main_frame, text="代理功能", padding=10)
        proxy_group.pack(fill=tk.X, pady=5)
        
        self.enable_proxy = tk.BooleanVar()
        ttk.Checkbutton(
            proxy_group, 
            text="启用代理功能", 
            variable=self.enable_proxy
        ).pack(anchor=tk.W, pady=2)
        
        # 代理设置
        proxy_config_group = ttk.LabelFrame(main_frame, text="代理配置", padding=10)
        proxy_config_group.pack(fill=tk.X, pady=5)
        
        ttk.Label(proxy_config_group, text="默认代理类型:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.default_proxy_type = ttk.Combobox(
            proxy_config_group, 
            values=['http', 'https', 'socks4', 'socks5']
        )
        self.default_proxy_type.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Label(proxy_config_group, text="连接超时(秒):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.proxy_timeout = ttk.Entry(proxy_config_group, width=10)
        self.proxy_timeout.grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        self.auto_test_proxy = tk.BooleanVar()
        ttk.Checkbutton(
            proxy_config_group, 
            text="自动测试代理", 
            variable=self.auto_test_proxy
        ).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)
    
    def create_advanced_settings(self):
        """创建高级设置页面"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="高级设置")
        
        main_frame = ttk.Frame(frame, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 系统修改
        system_group = ttk.LabelFrame(main_frame, text="系统标识修改", padding=10)
        system_group.pack(fill=tk.X, pady=5)
        
        self.modify_mac = tk.BooleanVar()
        ttk.Checkbutton(
            system_group, 
            text="修改MAC地址", 
            variable=self.modify_mac
        ).pack(anchor=tk.W, pady=2)
        
        self.modify_computer_name = tk.BooleanVar()
        ttk.Checkbutton(
            system_group, 
            text="修改计算机名", 
            variable=self.modify_computer_name
        ).pack(anchor=tk.W, pady=2)
        
        self.modify_machine_guid = tk.BooleanVar()
        ttk.Checkbutton(
            system_group, 
            text="修改机器GUID", 
            variable=self.modify_machine_guid
        ).pack(anchor=tk.W, pady=2)
        
        # 性能设置
        performance_group = ttk.LabelFrame(main_frame, text="性能设置", padding=10)
        performance_group.pack(fill=tk.X, pady=5)
        
        ttk.Label(performance_group, text="监控间隔(秒):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.monitor_interval = ttk.Entry(performance_group, width=10)
        self.monitor_interval.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Label(performance_group, text="清理间隔(分钟):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.cleanup_interval = ttk.Entry(performance_group, width=10)
        self.cleanup_interval.grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # 安全设置
        security_group = ttk.LabelFrame(main_frame, text="安全设置", padding=10)
        security_group.pack(fill=tk.X, pady=5)
        
        self.require_admin = tk.BooleanVar()
        ttk.Checkbutton(
            security_group, 
            text="需要管理员权限", 
            variable=self.require_admin
        ).pack(anchor=tk.W, pady=2)
        
        self.enable_logging = tk.BooleanVar()
        ttk.Checkbutton(
            security_group, 
            text="启用操作日志", 
            variable=self.enable_logging
        ).pack(anchor=tk.W, pady=2)
    
    def create_buttons(self):
        """创建按钮"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        
        ttk.Button(
            button_frame, 
            text="保存设置", 
            command=self.save_settings
        ).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            button_frame, 
            text="重置默认", 
            command=self.reset_settings
        ).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            button_frame, 
            text="应用", 
            command=self.apply_settings
        ).pack(side=tk.RIGHT, padx=5)
    
    def load_settings(self):
        """加载设置"""
        try:
            # 加载常规设置
            app_config = self.config_manager.get_config('app') or {}
            self.window_width.insert(0, str(app_config.get('window_width', 1200)))
            self.window_height.insert(0, str(app_config.get('window_height', 800)))
            self.max_instances.insert(0, str(app_config.get('max_instances', 10)))
            self.auto_save.set(app_config.get('auto_save', True))
            self.theme.set(app_config.get('theme', 'default'))
            
            # 加载沙盒设置
            sandbox_config = self.config_manager.get_config('sandbox') or {}
            self.enable_isolation.set(sandbox_config.get('enable_isolation', True))
            self.enable_registry_isolation.set(sandbox_config.get('enable_registry_isolation', False))
            self.enable_filesystem_isolation.set(sandbox_config.get('enable_filesystem_isolation', False))
            self.temp_dir.insert(0, sandbox_config.get('temp_dir', 'temp'))
            self.log_level.set(sandbox_config.get('log_level', 'INFO'))
            
            # 加载代理设置
            proxy_config = self.config_manager.get_config('proxy') or {}
            self.enable_proxy.set(proxy_config.get('enable_proxy', True))
            self.default_proxy_type.set(proxy_config.get('proxy_type', 'http'))
            self.proxy_timeout.insert(0, str(proxy_config.get('timeout', 30)))
            self.auto_test_proxy.set(proxy_config.get('auto_test', False))
            
            # 加载高级设置
            advanced_config = self.config_manager.get_config('advanced') or {}
            self.modify_mac.set(advanced_config.get('modify_mac', False))
            self.modify_computer_name.set(advanced_config.get('modify_computer_name', False))
            self.modify_machine_guid.set(advanced_config.get('modify_machine_guid', False))
            self.monitor_interval.insert(0, str(advanced_config.get('monitor_interval', 2)))
            self.cleanup_interval.insert(0, str(advanced_config.get('cleanup_interval', 30)))
            self.require_admin.set(advanced_config.get('require_admin', False))
            self.enable_logging.set(advanced_config.get('enable_logging', True))
            
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存常规设置
            self.config_manager.set_config('app', 'window_width', int(self.window_width.get()))
            self.config_manager.set_config('app', 'window_height', int(self.window_height.get()))
            self.config_manager.set_config('app', 'max_instances', int(self.max_instances.get()))
            self.config_manager.set_config('app', 'auto_save', self.auto_save.get())
            self.config_manager.set_config('app', 'theme', self.theme.get())
            
            # 保存沙盒设置
            self.config_manager.set_config('sandbox', 'enable_isolation', self.enable_isolation.get())
            self.config_manager.set_config('sandbox', 'enable_registry_isolation', self.enable_registry_isolation.get())
            self.config_manager.set_config('sandbox', 'enable_filesystem_isolation', self.enable_filesystem_isolation.get())
            self.config_manager.set_config('sandbox', 'temp_dir', self.temp_dir.get())
            self.config_manager.set_config('sandbox', 'log_level', self.log_level.get())
            
            # 保存代理设置
            self.config_manager.set_config('proxy', 'enable_proxy', self.enable_proxy.get())
            self.config_manager.set_config('proxy', 'proxy_type', self.default_proxy_type.get())
            self.config_manager.set_config('proxy', 'timeout', int(self.proxy_timeout.get()))
            self.config_manager.set_config('proxy', 'auto_test', self.auto_test_proxy.get())
            
            # 保存高级设置
            self.config_manager.set_config('advanced', 'modify_mac', self.modify_mac.get())
            self.config_manager.set_config('advanced', 'modify_computer_name', self.modify_computer_name.get())
            self.config_manager.set_config('advanced', 'modify_machine_guid', self.modify_machine_guid.get())
            self.config_manager.set_config('advanced', 'monitor_interval', int(self.monitor_interval.get()))
            self.config_manager.set_config('advanced', 'cleanup_interval', int(self.cleanup_interval.get()))
            self.config_manager.set_config('advanced', 'require_admin', self.require_admin.get())
            self.config_manager.set_config('advanced', 'enable_logging', self.enable_logging.get())
            
            # 保存配置文件
            self.config_manager.save_config()
            
            messagebox.showinfo("成功", "设置保存成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {str(e)}")
    
    def apply_settings(self):
        """应用设置"""
        # TODO: 实现设置应用逻辑
        messagebox.showinfo("提示", "设置应用功能待实现")
    
    def reset_settings(self):
        """重置设置"""
        if messagebox.askyesno("确认", "确定要重置所有设置为默认值吗？"):
            # TODO: 实现设置重置逻辑
            messagebox.showinfo("提示", "设置重置功能待实现")
    
    def browse_temp_dir(self):
        """浏览临时目录"""
        directory = filedialog.askdirectory(
            title="选择临时目录",
            initialdir=self.temp_dir.get() or os.getcwd()
        )
        
        if directory:
            self.temp_dir.delete(0, tk.END)
            self.temp_dir.insert(0, directory)
