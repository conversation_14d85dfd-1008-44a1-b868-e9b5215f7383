#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk优化启动方案
基于成功验证的"用户数据重定向"方法
"""

import os
import subprocess
import tempfile
import shutil
from typing import Optional
from .persistent_data import launch_qingtalk_persistent, cleanup_qingtalk_persistent

def launch_qingtalk_optimized(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk优化启动方案 - 完美方案优先
    """
    try:
        # 检查是否有管理员权限
        import ctypes
        if ctypes.windll.shell32.IsUserAnAdmin():
            print(f"🌟 QingTalk启动 (实例: {instance_id[:8]}) - 终极方案")
            print(f"  🎯 检测到管理员权限，使用设备信息伪装")
            from .qingtalk_ultimate import launch_qingtalk_ultimate
            return launch_qingtalk_ultimate(exe_path, instance_id)
        else:
            print(f"🌟 QingTalk启动 (实例: {instance_id[:8]}) - 完美方案")
            print(f"  🎯 使用数据隔离 + 持久化登录")
            from .qingtalk_perfect import launch_qingtalk_perfect
            return launch_qingtalk_perfect(exe_path, instance_id)

    except Exception as e:
        print(f"  ❌ 完美方案失败，尝试持久化登录: {e}")

        try:
            # 备用方案：持久化登录（已验证成功）
            from .qingtalk_persistent_login import launch_qingtalk_persistent_login
            print(f"💾 QingTalk启动 (实例: {instance_id[:8]}) - 持久化登录方案")
            return launch_qingtalk_persistent_login(exe_path, instance_id)

        except Exception as e2:
            print(f"  ❌ 持久化登录失败，回退到临时模式: {e2}")
            # 最后回退到临时模式
            return launch_qingtalk_temporary(exe_path, instance_id)

def launch_qingtalk_data_only_isolation(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk数据隔离启动 - 只隔离数据，不复制程序
    """
    try:
        print(f"🔄 QingTalk数据隔离启动 (实例: {instance_id[:8]})")

        # 1. 创建持久化数据目录（不复制程序）
        persistent_base = os.path.join(os.getcwd(), "persistent_data")
        os.makedirs(persistent_base, exist_ok=True)

        instance_dir = os.path.join(persistent_base, f"instance_{instance_id[:8]}")
        user_data_dir = os.path.join(instance_dir, 'UserData')
        appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
        temp_dir = os.path.join(user_data_dir, 'Temp')

        # 创建目录结构
        for path in [appdata_roaming, appdata_local, temp_dir]:
            os.makedirs(path, exist_ok=True)

        # 创建QingTalk专用数据目录
        qingtalk_roaming = os.path.join(appdata_roaming, 'QingTalk')
        qingtalk_local = os.path.join(appdata_local, 'QingTalk')
        os.makedirs(qingtalk_roaming, exist_ok=True)
        os.makedirs(qingtalk_local, exist_ok=True)

        print(f"  📂 数据目录: {instance_dir}")
        print(f"  💾 QingTalk数据: {qingtalk_roaming}")
        print(f"  🚀 使用原始程序: {exe_path}")

        # 2. 设置强化隔离环境变量
        env = os.environ.copy()

        # 用户数据目录隔离
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': user_data_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
        })

        # 强化系统标识隔离
        unique_suffix = instance_id[:8]
        env.update({
            'COMPUTERNAME': f'QINGTALK-{unique_suffix.upper()}',
            'USERNAME': f'QTUser_{unique_suffix}',
            'USERDOMAIN': f'QTDOMAIN_{unique_suffix.upper()}',
            'SESSIONNAME': f'QTSession_{unique_suffix}',
            'LOGONSERVER': f'\\\\QINGTALK-{unique_suffix.upper()}',
        })

        # 网络隔离（使用不同端口避免冲突）
        port_base = 40000 + (sum(ord(c) for c in instance_id) % 10000)
        env.update({
            'QINGTALK_PORT_BASE': str(port_base),
            'QINGTALK_IPC_PORT': str(port_base + 1),
            'QINGTALK_SYNC_PORT': str(port_base + 2),
            'QINGTALK_UPDATE_PORT': str(port_base + 3),
        })

        # 进程间通信隔离
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_ISOLATION_MODE': '1',
            'QINGTALK_MUTEX_PREFIX': f'QT_{unique_suffix}_',
            'QINGTALK_PIPE_PREFIX': f'QTPipe_{unique_suffix}_',
            'QINGTALK_SHARED_MEM_PREFIX': f'QTMem_{unique_suffix}_',
        })

        # 系统标识隔离
        env.update({
            'PROCESSOR_IDENTIFIER': f'Intel64 Family 6 Model {sum(ord(c) for c in instance_id) % 100} Stepping 1',
            'NUMBER_OF_PROCESSORS': '4',
            'PROCESSOR_LEVEL': '6',
            'PROCESSOR_REVISION': f'{sum(ord(c) for c in instance_id) % 9999:04x}',
        })

        # Windows特定隔离
        env.update({
            'ALLUSERSPROFILE': os.path.join(user_data_dir, 'ProgramData'),
            'PROGRAMDATA': os.path.join(user_data_dir, 'ProgramData'),
            'PUBLIC': os.path.join(user_data_dir, 'Public'),
        })

        # 创建这些目录
        for env_path in [env['ALLUSERSPROFILE'], env['PUBLIC']]:
            os.makedirs(env_path, exist_ok=True)

        print(f"  🔧 端口基数: {port_base}")
        print(f"  🏷️ 计算机名: QINGTALK-{unique_suffix.upper()}")
        print(f"  👤 用户名: QTUser_{unique_suffix}")

        # 3. 启动原始QingTalk程序（使用强隔离）
        process = subprocess.Popen(
            exe_path,
            env=env,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NEW_CONSOLE
        )

        # 保存隔离信息
        process.isolation_info = {
            'user_data_dir': instance_dir,
            'method': 'qingtalk_data_only',
            'instance_id': instance_id,
            'port_base': port_base,
            'persistent': True
        }

        print(f"  ✅ QingTalk数据隔离启动成功 (PID: {process.pid})")
        return process

    except Exception as e:
        print(f"  ❌ QingTalk数据隔离启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def launch_qingtalk_simple_persistent(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk智能持久化启动 - 首次复制程序，后续重用 = 保持登录 + 支持多开
    """
    try:
        print(f"🔄 QingTalk智能持久化启动 (实例: {instance_id[:8]})")

        # 1. 检查实例目录是否存在
        persistent_base = os.path.join(os.getcwd(), "persistent_data")
        os.makedirs(persistent_base, exist_ok=True)

        instance_dir = os.path.join(persistent_base, f"instance_{instance_id[:8]}")
        qingtalk_program_dir = os.path.join(instance_dir, 'QingTalk')
        user_data_dir = os.path.join(instance_dir, 'UserData')

        # 检查是否是首次启动
        is_first_time = not os.path.exists(instance_dir)

        if is_first_time:
            print(f"  🆕 首次启动，创建完整实例环境...")

            # 创建实例目录
            os.makedirs(instance_dir, exist_ok=True)

            # 复制QingTalk程序（更安全的复制方式）
            qingtalk_source_dir = os.path.dirname(exe_path)
            print(f"  📂 复制QingTalk程序从: {qingtalk_source_dir}")
            print(f"  📂 复制QingTalk程序到: {qingtalk_program_dir}")

            try:
                # 使用更详细的复制过程
                def copy_with_retry(src, dst):
                    """带重试的复制函数"""
                    max_retries = 3
                    for attempt in range(max_retries):
                        try:
                            if os.path.exists(dst):
                                shutil.rmtree(dst, ignore_errors=True)

                            shutil.copytree(
                                src,
                                dst,
                                ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData', '__pycache__'),
                                dirs_exist_ok=True
                            )
                            return True
                        except Exception as e:
                            print(f"    复制尝试 {attempt + 1} 失败: {e}")
                            if attempt < max_retries - 1:
                                import time
                                time.sleep(1)
                            else:
                                raise e
                    return False

                copy_with_retry(qingtalk_source_dir, qingtalk_program_dir)
                print(f"  ✅ QingTalk程序复制完成")

            except Exception as e:
                print(f"  ❌ 复制QingTalk程序失败: {e}")
                raise e
        else:
            print(f"  🔄 重用现有实例环境...")

            # 验证现有程序文件是否完整
            if not os.path.exists(os.path.join(qingtalk_program_dir, "QingTalk.exe")):
                print(f"  ⚠️ 检测到程序文件不完整，重新复制...")
                qingtalk_source_dir = os.path.dirname(exe_path)
                if os.path.exists(qingtalk_program_dir):
                    shutil.rmtree(qingtalk_program_dir, ignore_errors=True)
                shutil.copytree(qingtalk_source_dir, qingtalk_program_dir,
                               ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))
                print(f"  ✅ 程序文件修复完成")

        # 创建用户数据目录结构
        appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
        temp_dir = os.path.join(user_data_dir, 'Temp')

        # 确保目录存在
        for path in [appdata_roaming, appdata_local, temp_dir]:
            os.makedirs(path, exist_ok=True)

        # 创建QingTalk专用数据目录
        qingtalk_roaming = os.path.join(appdata_roaming, 'QingTalk')
        qingtalk_local = os.path.join(appdata_local, 'QingTalk')
        os.makedirs(qingtalk_roaming, exist_ok=True)
        os.makedirs(qingtalk_local, exist_ok=True)

        # 使用实例专用的QingTalk程序
        instance_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")

        print(f"  📂 实例目录: {instance_dir}")
        print(f"  💾 QingTalk数据: {qingtalk_roaming}")
        print(f"  🚀 程序路径: {instance_exe}")
        print(f"  {'🆕 首次创建' if is_first_time else '🔄 重用现有'}")

        # 详细检查程序文件
        if not os.path.exists(instance_exe):
            raise FileNotFoundError(f"实例程序文件不存在: {instance_exe}")

        # 检查关键资源文件
        resources_dir = os.path.join(qingtalk_program_dir, "resources")
        if os.path.exists(resources_dir):
            print(f"  ✅ 资源目录存在: {resources_dir}")
        else:
            print(f"  ⚠️ 资源目录不存在: {resources_dir}")

        # 检查app.asar文件
        app_asar = os.path.join(resources_dir, "app.asar")
        if os.path.exists(app_asar):
            print(f"  ✅ app.asar存在: {app_asar}")
        else:
            print(f"  ⚠️ app.asar不存在: {app_asar}")

        print(f"  🔍 程序目录内容: {os.listdir(qingtalk_program_dir) if os.path.exists(qingtalk_program_dir) else '目录不存在'}")

        # 2. 设置智能隔离环境变量
        env = os.environ.copy()

        # 核心：隔离用户数据和进程通信
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': user_data_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
        })

        # 进程隔离标识
        unique_suffix = instance_id[:8]
        env.update({
            'COMPUTERNAME': f'QINGTALK-{unique_suffix.upper()}',
            'USERNAME': f'QTUser_{unique_suffix}',
            'USERDOMAIN': f'QTDOMAIN_{unique_suffix.upper()}',
            'SESSIONNAME': f'QTSession_{unique_suffix}',
        })

        # 网络隔离（使用不同端口避免冲突）
        port_base = 40000 + (sum(ord(c) for c in instance_id) % 10000)
        env.update({
            'QINGTALK_PORT_BASE': str(port_base),
            'QINGTALK_IPC_PORT': str(port_base + 1),
            'QINGTALK_SYNC_PORT': str(port_base + 2),
            'QINGTALK_UPDATE_PORT': str(port_base + 3),
        })

        # 进程间通信隔离
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_ISOLATION_MODE': '1',
            'QINGTALK_MUTEX_PREFIX': f'QT_{unique_suffix}_',
            'QINGTALK_PIPE_PREFIX': f'QTPipe_{unique_suffix}_',
            'QINGTALK_SHARED_MEM_PREFIX': f'QTMem_{unique_suffix}_',
        })

        print(f"  🔧 端口基数: {port_base}")
        print(f"  🏷️ 计算机名: QINGTALK-{unique_suffix.upper()}")

        # 3. 启动实例专用的QingTalk
        process = subprocess.Popen(
            instance_exe,
            env=env,
            cwd=qingtalk_program_dir,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NEW_CONSOLE
        )

        # 保存持久化信息
        process.isolation_info = {
            'user_data_dir': instance_dir,
            'program_dir': qingtalk_program_dir,
            'method': 'qingtalk_smart_persistent',
            'instance_id': instance_id,
            'port_base': port_base,
            'persistent': True,
            'is_first_time': is_first_time
        }

        print(f"  ✅ QingTalk智能持久化启动成功 (PID: {process.pid})")
        return process

    except Exception as e:
        print(f"  ❌ QingTalk智能持久化启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def launch_qingtalk_temporary(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk临时启动方案 - 不保持登录状态
    """
    try:
        print(f"🛡️ QingTalk临时隔离启动 (实例: {instance_id[:8]})")

        # 1. 创建完全隔离的QingTalk副本
        base_dir = tempfile.mkdtemp(prefix=f"qingtalk_temp_{instance_id[:8]}_")
        qingtalk_source_dir = os.path.dirname(exe_path)
        qingtalk_isolated_dir = os.path.join(base_dir, "QingTalk")

        print(f"  📂 复制QingTalk程序...")
        shutil.copytree(qingtalk_source_dir, qingtalk_isolated_dir,
                       ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))

        isolated_exe = os.path.join(qingtalk_isolated_dir, "QingTalk.exe")

        # 2. 创建完全独立的用户数据目录
        user_data_dir = os.path.join(base_dir, 'UserData')
        appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
        temp_dir = os.path.join(user_data_dir, 'Temp')

        # 创建目录结构
        for path in [appdata_roaming, appdata_local, temp_dir]:
            os.makedirs(path, exist_ok=True)

        # 3. 设置强隔离环境变量
        env = os.environ.copy()

        # 基础用户数据重定向
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': user_data_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
        })

        # 进程隔离标识
        unique_suffix = instance_id[:8]
        env.update({
            'COMPUTERNAME': f'QINGTALK-{unique_suffix.upper()}',
            'USERNAME': f'QTUser_{unique_suffix}',
            'USERDOMAIN': f'QTDOMAIN_{unique_suffix.upper()}',
            'SESSIONNAME': f'QTSession_{unique_suffix}',
        })

        # 网络隔离 - 重定向可能的通信端口
        port_base = 40000 + (sum(ord(c) for c in instance_id) % 10000)
        env.update({
            'QINGTALK_PORT_BASE': str(port_base),
            'QINGTALK_IPC_PORT': str(port_base + 1),
            'QINGTALK_SYNC_PORT': str(port_base + 2),
            'QINGTALK_UPDATE_PORT': str(port_base + 3),
        })

        # 进程间通信隔离
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_ISOLATION_MODE': '1',
            'QINGTALK_MUTEX_PREFIX': f'QT_{unique_suffix}_',
            'QINGTALK_PIPE_PREFIX': f'QTPipe_{unique_suffix}_',
            'QINGTALK_SHARED_MEM_PREFIX': f'QTMem_{unique_suffix}_',
        })

        print(f"  📂 临时目录: {base_dir}")
        print(f"  🔧 端口基数: {port_base}")
        print(f"  🏷️ 计算机名: QINGTALK-{unique_suffix.upper()}")

        # 4. 启动隔离的QingTalk
        process = subprocess.Popen(
            isolated_exe,
            env=env,
            cwd=qingtalk_isolated_dir,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NEW_CONSOLE
        )

        # 保存隔离信息
        process.isolation_info = {
            'user_data_dir': base_dir,
            'method': 'qingtalk_temporary',
            'instance_id': instance_id,
            'port_base': port_base,
            'persistent': False  # 标记为临时存储
        }

        print(f"  ✅ QingTalk临时隔离启动成功 (PID: {process.pid})")
        return process

    except Exception as e:
        print(f"  ❌ QingTalk临时隔离启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_qingtalk_optimized(process: subprocess.Popen):
    """
    清理QingTalk优化启动的资源
    """
    try:
        if hasattr(process, 'isolation_info'):
            isolation_info = process.isolation_info
            method = isolation_info.get('method', 'unknown')

            if method == 'qingtalk_perfect':
                # 完美模式：保持程序和登录状态
                from .qingtalk_perfect import cleanup_qingtalk_perfect
                cleanup_qingtalk_perfect(process)
            elif method == 'qingtalk_persistent_login':
                # 持久化登录模式：保持所有数据
                from .qingtalk_persistent_login import cleanup_qingtalk_persistent_login
                cleanup_qingtalk_persistent_login(process)
            elif method == 'qingtalk_final':
                # 最终模式：保持程序和数据
                from .qingtalk_final import cleanup_qingtalk_final
                cleanup_qingtalk_final(process)
            elif method == 'qingtalk_ultimate':
                # 终极模式：保持程序和数据
                from .qingtalk_ultimate import cleanup_qingtalk_ultimate
                cleanup_qingtalk_ultimate(process)
            elif method == 'qingtalk_data_only':
                # 数据隔离模式：不删除用户数据，保持登录状态
                instance_id = isolation_info.get('instance_id', 'unknown')
                print(f"💾 保持实例 {instance_id[:8]} 的登录状态")
            elif method == 'qingtalk_smart_persistent':
                # 智能持久化模式：不删除用户数据和程序，保持登录状态
                instance_id = isolation_info.get('instance_id', 'unknown')
                print(f"💾 保持实例 {instance_id[:8]} 的登录状态和程序文件")
            elif method == 'qingtalk_simple_persistent':
                # 简化持久化模式：不删除用户数据，保持登录状态
                instance_id = isolation_info.get('instance_id', 'unknown')
                print(f"💾 保持实例 {instance_id[:8]} 的登录状态")
            elif method == 'qingtalk_persistent':
                # 复杂持久化模式：清理临时程序，保持用户数据
                cleanup_qingtalk_persistent(process)
            else:
                # 临时模式：删除临时数据
                user_data_dir = isolation_info.get('user_data_dir')
                if user_data_dir and os.path.exists(user_data_dir):
                    print(f"🧹 清理QingTalk临时隔离环境: {user_data_dir}")
                    shutil.rmtree(user_data_dir, ignore_errors=True)

    except Exception as e:
        print(f"⚠️ 清理QingTalk隔离环境失败: {e}")

def is_qingtalk_app(exe_path: str) -> bool:
    """
    检查是否是QingTalk应用
    """
    return 'qingtalk' in exe_path.lower()

def test_qingtalk_optimized():
    """
    测试QingTalk优化方案
    """
    print("=" * 50)
    print("QingTalk优化方案测试")
    print("=" * 50)
    
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk不存在: {qingtalk_exe}")
        return False
    
    print("🧪 测试QingTalk多开...")
    
    processes = []
    
    # 启动3个实例
    for i in range(3):
        print(f"\n🚀 启动实例 {i+1}...")
        process = launch_qingtalk_optimized(qingtalk_exe, f"test_instance_{i}")
        
        if process:
            processes.append(process)
            print(f"✅ 实例 {i+1} 启动成功 (PID: {process.pid})")
        else:
            print(f"❌ 实例 {i+1} 启动失败")
        
        # 延迟启动避免冲突
        import time
        time.sleep(3)
    
    if not processes:
        print("\n❌ 没有实例启动成功")
        return False
    
    # 等待10秒检查状态
    print(f"\n⏳ 等待10秒检查运行状态...")
    import time
    time.sleep(10)
    
    running_count = 0
    for i, process in enumerate(processes):
        if process.poll() is None:
            print(f"✅ 实例 {i+1} 正在运行 (PID: {process.pid})")
            running_count += 1
        else:
            print(f"❌ 实例 {i+1} 已停止")
    
    print(f"\n📊 运行统计: {running_count}/{len(processes)} 个实例成功运行")
    
    # 清理所有实例
    print(f"\n🛑 清理所有实例...")
    for i, process in enumerate(processes):
        try:
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=10)
                print(f"✅ 实例 {i+1} 已停止")
            
            cleanup_qingtalk_optimized(process)
            print(f"✅ 实例 {i+1} 已清理")
        except Exception as e:
            print(f"⚠️ 实例 {i+1} 清理失败: {e}")
    
    success = running_count > 1
    
    print(f"\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    
    if success:
        print(f"🎉 QingTalk多开测试成功！")
        print(f"✅ 成功运行 {running_count} 个实例")
        print(f"💡 可以集成到主程序中使用")
    else:
        print(f"❌ QingTalk多开测试失败")
        if running_count == 1:
            print(f"⚠️ 只有1个实例运行，可能仍有检测机制")
        else:
            print(f"⚠️ 没有实例成功运行")
    
    return success

if __name__ == "__main__":
    test_qingtalk_optimized()
