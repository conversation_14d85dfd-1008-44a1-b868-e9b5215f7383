#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于分析结果的绕过策略
针对QingTalk的具体检测机制提供解决方案
"""

import os
import subprocess
import tempfile
import shutil
from typing import Dict, List

class BypassStrategies:
    def __init__(self):
        self.qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    def strategy_1_complete_isolation(self, instance_id: str):
        """策略1: 完全隔离 - 基于分析结果的最强隔离"""
        print(f"🛡️ 策略1: 完全隔离启动 (实例: {instance_id})")
        
        try:
            # 创建完全隔离的环境
            base_dir = tempfile.mkdtemp(prefix=f"qingtalk_isolated_{instance_id}_")
            
            # 1. 复制QingTalk到隔离目录
            qingtalk_dir = os.path.dirname(self.qingtalk_exe)
            isolated_qingtalk_dir = os.path.join(base_dir, "QingTalk")
            
            print(f"  复制QingTalk到: {isolated_qingtalk_dir}")
            shutil.copytree(qingtalk_dir, isolated_qingtalk_dir)
            
            isolated_exe = os.path.join(isolated_qingtalk_dir, "QingTalk.exe")
            
            # 2. 创建完全隔离的环境变量
            env = os.environ.copy()
            
            # 重定向所有用户相关路径
            user_data_dir = os.path.join(base_dir, "UserData")
            os.makedirs(user_data_dir, exist_ok=True)
            
            env.update({
                'APPDATA': os.path.join(user_data_dir, 'AppData', 'Roaming'),
                'LOCALAPPDATA': os.path.join(user_data_dir, 'AppData', 'Local'),
                'USERPROFILE': user_data_dir,
                'HOMEPATH': user_data_dir,
                'TEMP': os.path.join(user_data_dir, 'Temp'),
                'TMP': os.path.join(user_data_dir, 'Temp'),
                'USERNAME': f'QingTalkUser_{instance_id}',
                'COMPUTERNAME': f'QINGTALK-{instance_id.upper()}',
                'USERDOMAIN': f'QINGTALK-{instance_id.upper()}'
            })
            
            # 创建必要的目录
            for path in [env['APPDATA'], env['LOCALAPPDATA'], env['TEMP']]:
                os.makedirs(path, exist_ok=True)
            
            # 3. 启动隔离的QingTalk
            process = subprocess.Popen(
                isolated_exe,
                env=env,
                cwd=isolated_qingtalk_dir,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 完全隔离启动成功 (PID: {process.pid})")
            
            # 保存清理信息
            process.cleanup_info = {
                'base_dir': base_dir,
                'instance_id': instance_id
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 完全隔离启动失败: {e}")
            return None
    
    def strategy_2_registry_virtualization(self, instance_id: str):
        """策略2: 注册表虚拟化"""
        print(f"🔧 策略2: 注册表虚拟化启动 (实例: {instance_id})")
        
        try:
            # 创建虚拟注册表环境
            base_dir = tempfile.mkdtemp(prefix=f"qingtalk_regvirt_{instance_id}_")
            
            # 设置注册表重定向环境变量
            env = os.environ.copy()
            env.update({
                'HKCU_OVERRIDE': os.path.join(base_dir, 'HKCU'),
                'HKLM_OVERRIDE': os.path.join(base_dir, 'HKLM'),
                'REGISTRY_ISOLATION': '1',
                'INSTANCE_ID': instance_id,
                'QINGTALK_INSTANCE_ID': instance_id
            })
            
            # 创建虚拟注册表目录
            os.makedirs(env['HKCU_OVERRIDE'], exist_ok=True)
            os.makedirs(env['HKLM_OVERRIDE'], exist_ok=True)
            
            # 启动QingTalk
            process = subprocess.Popen(
                self.qingtalk_exe,
                env=env,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 注册表虚拟化启动成功 (PID: {process.pid})")
            
            process.cleanup_info = {
                'base_dir': base_dir,
                'instance_id': instance_id
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 注册表虚拟化启动失败: {e}")
            return None
    
    def strategy_3_process_masquerading(self, instance_id: str):
        """策略3: 进程伪装"""
        print(f"🎭 策略3: 进程伪装启动 (实例: {instance_id})")
        
        try:
            # 创建伪装环境
            base_dir = tempfile.mkdtemp(prefix=f"qingtalk_masq_{instance_id}_")
            
            # 复制并重命名QingTalk
            fake_name = f"BusinessApp_{instance_id}.exe"
            fake_exe = os.path.join(base_dir, fake_name)
            shutil.copy2(self.qingtalk_exe, fake_exe)
            
            # 设置伪装环境变量
            env = os.environ.copy()
            env.update({
                'PROCESS_NAME_OVERRIDE': fake_name,
                'APPLICATION_NAME': f'Business Application {instance_id}',
                'INSTANCE_ID': instance_id,
                'MASQUERADE_MODE': '1'
            })
            
            # 启动伪装的进程
            process = subprocess.Popen(
                fake_exe,
                env=env,
                cwd=base_dir,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 进程伪装启动成功 (PID: {process.pid}, 名称: {fake_name})")
            
            process.cleanup_info = {
                'base_dir': base_dir,
                'instance_id': instance_id
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 进程伪装启动失败: {e}")
            return None
    
    def strategy_4_network_isolation(self, instance_id: str):
        """策略4: 网络隔离"""
        print(f"🌐 策略4: 网络隔离启动 (实例: {instance_id})")
        
        try:
            # 设置网络隔离环境变量
            env = os.environ.copy()
            env.update({
                'HTTP_PROXY': f'127.0.0.1:808{instance_id[-1]}',  # 使用不同的代理端口
                'HTTPS_PROXY': f'127.0.0.1:808{instance_id[-1]}',
                'NO_PROXY': '',
                'NETWORK_ISOLATION': '1',
                'INSTANCE_ID': instance_id,
                'NETWORK_NAMESPACE': f'qingtalk_{instance_id}'
            })
            
            # 启动QingTalk
            process = subprocess.Popen(
                self.qingtalk_exe,
                env=env,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 网络隔离启动成功 (PID: {process.pid})")
            
            process.cleanup_info = {
                'instance_id': instance_id
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 网络隔离启动失败: {e}")
            return None
    
    def test_all_strategies(self):
        """测试所有绕过策略"""
        print("=" * 60)
        print("QingTalk绕过策略测试")
        print("=" * 60)
        
        strategies = [
            ("完全隔离", self.strategy_1_complete_isolation),
            ("注册表虚拟化", self.strategy_2_registry_virtualization),
            ("进程伪装", self.strategy_3_process_masquerading),
            ("网络隔离", self.strategy_4_network_isolation)
        ]
        
        results = {}
        
        for strategy_name, strategy_func in strategies:
            print(f"\n🧪 测试策略: {strategy_name}")
            print("-" * 40)
            
            try:
                # 启动进程
                process = strategy_func(f"test{len(results)+1}")
                
                if process:
                    # 等待5秒检查状态
                    import time
                    time.sleep(5)
                    
                    if process.poll() is None:
                        print(f"  ✅ 策略 '{strategy_name}' 成功 - 进程运行中")
                        results[strategy_name] = {
                            'success': True,
                            'pid': process.pid,
                            'duration': 5
                        }
                        
                        # 停止进程
                        process.terminate()
                        process.wait(timeout=5)
                        print(f"  🛑 进程已停止")
                    else:
                        print(f"  ❌ 策略 '{strategy_name}' 失败 - 进程已退出")
                        results[strategy_name] = {
                            'success': False,
                            'reason': 'Process exited early'
                        }
                    
                    # 清理
                    self.cleanup_strategy(process)
                else:
                    print(f"  ❌ 策略 '{strategy_name}' 失败 - 无法启动")
                    results[strategy_name] = {
                        'success': False,
                        'reason': 'Failed to start'
                    }
                    
            except Exception as e:
                print(f"  ❌ 策略 '{strategy_name}' 异常: {e}")
                results[strategy_name] = {
                    'success': False,
                    'reason': str(e)
                }
        
        # 生成测试报告
        self.generate_strategy_report(results)
        
        return results
    
    def cleanup_strategy(self, process):
        """清理策略资源"""
        try:
            if hasattr(process, 'cleanup_info'):
                cleanup_info = process.cleanup_info
                base_dir = cleanup_info.get('base_dir')
                
                if base_dir and os.path.exists(base_dir):
                    shutil.rmtree(base_dir, ignore_errors=True)
                    print(f"  🧹 清理完成: {base_dir}")
        except Exception as e:
            print(f"  ⚠️ 清理失败: {e}")
    
    def generate_strategy_report(self, results):
        """生成策略测试报告"""
        print(f"\n" + "=" * 60)
        print("绕过策略测试报告")
        print("=" * 60)
        
        successful_strategies = [name for name, result in results.items() if result.get('success')]
        failed_strategies = [name for name, result in results.items() if not result.get('success')]
        
        print(f"\n📊 测试结果:")
        print(f"  成功策略: {len(successful_strategies)}/{len(results)}")
        print(f"  失败策略: {len(failed_strategies)}/{len(results)}")
        
        if successful_strategies:
            print(f"\n✅ 成功的策略:")
            for strategy in successful_strategies:
                result = results[strategy]
                print(f"  - {strategy} (PID: {result.get('pid')}, 运行: {result.get('duration')}秒)")
        
        if failed_strategies:
            print(f"\n❌ 失败的策略:")
            for strategy in failed_strategies:
                result = results[strategy]
                print(f"  - {strategy}: {result.get('reason', 'Unknown')}")
        
        # 推荐
        if successful_strategies:
            print(f"\n💡 推荐使用:")
            print(f"  最佳策略: {successful_strategies[0]}")
            print(f"  备选策略: {', '.join(successful_strategies[1:]) if len(successful_strategies) > 1 else '无'}")
        else:
            print(f"\n⚠️ 所有策略都失败了，QingTalk的检测机制可能很强")
            print(f"  建议考虑:")
            print(f"  1. 使用虚拟机完全隔离")
            print(f"  2. 使用不同的Windows用户账户")
            print(f"  3. 使用容器技术")

def test_bypass_strategies():
    """测试绕过策略"""
    bypass = BypassStrategies()
    
    if not os.path.exists(bypass.qingtalk_exe):
        print(f"❌ QingTalk不存在: {bypass.qingtalk_exe}")
        return
    
    print("QingTalk绕过策略测试工具")
    print("基于分析结果测试不同的绕过方法")
    
    results = bypass.test_all_strategies()
    
    return results

if __name__ == "__main__":
    test_bypass_strategies()
