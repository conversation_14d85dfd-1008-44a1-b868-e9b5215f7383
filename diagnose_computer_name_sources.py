#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断计算机名的所有获取来源
找出QingTalk可能使用的获取方式
"""

import ctypes
from ctypes import wintypes
import os
import sys
import platform
import subprocess
import winreg
import socket

def get_all_computer_name_sources():
    """获取所有可能的计算机名来源"""
    sources = {}
    
    print("🔍 检查所有计算机名获取来源...")
    
    # 1. 环境变量
    print("  📋 环境变量...")
    env_vars = ['COMPUTERNAME', 'USERDOMAIN', 'LOGONSERVER', 'CLIENTNAME']
    for var in env_vars:
        sources[f'ENV_{var}'] = os.environ.get(var, 'Not Found')
    
    # 2. GetComputerNameEx API (所有类型)
    print("  🔧 GetComputerNameEx API...")
    name_types = [
        ('NetBIOS', 0),
        ('DnsHostname', 1),
        ('DnsDomain', 2),
        ('DnsFullyQualified', 3),
        ('PhysicalNetBIOS', 4),
        ('PhysicalDnsHostname', 5),
        ('PhysicalDnsDomain', 6),
        ('PhysicalDnsFullyQualified', 7),
    ]
    
    for name, type_id in name_types:
        try:
            size = wintypes.DWORD(0)
            ctypes.windll.kernel32.GetComputerNameExW(type_id, None, ctypes.byref(size))
            buffer = ctypes.create_unicode_buffer(size.value)
            success = ctypes.windll.kernel32.GetComputerNameExW(type_id, buffer, ctypes.byref(size))
            if success:
                sources[f'API_GetComputerNameEx_{name}'] = buffer.value
            else:
                sources[f'API_GetComputerNameEx_{name}'] = 'Failed'
        except:
            sources[f'API_GetComputerNameEx_{name}'] = 'Error'
    
    # 3. GetComputerName API
    print("  🔧 GetComputerName API...")
    try:
        size = wintypes.DWORD(0)
        ctypes.windll.kernel32.GetComputerNameW(None, ctypes.byref(size))
        buffer = ctypes.create_unicode_buffer(size.value)
        success = ctypes.windll.kernel32.GetComputerNameW(buffer, ctypes.byref(size))
        if success:
            sources['API_GetComputerName'] = buffer.value
        else:
            sources['API_GetComputerName'] = 'Failed'
    except:
        sources['API_GetComputerName'] = 'Error'
    
    # 4. 注册表直接读取
    print("  📝 注册表直接读取...")
    registry_keys = [
        (r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
        (r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", "ComputerName"),
        (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Hostname"),
        (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Hostname"),
        (r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOwner"),
        (r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOrganization"),
        (r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment", "COMPUTERNAME"),
    ]
    
    for subkey, value_name in registry_keys:
        key_name = subkey.split("\\")[-1]
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, subkey) as key:
                value, _ = winreg.QueryValueEx(key, value_name)
                sources[f'REG_{key_name}_{value_name}'] = value
        except:
            sources[f'REG_{key_name}_{value_name}'] = 'Not Found'
    
    # 5. WMI查询
    print("  🖥️ WMI查询...")
    try:
        import wmi
        c = wmi.WMI()
        
        # Win32_ComputerSystem
        for computer in c.Win32_ComputerSystem():
            sources['WMI_ComputerSystem_Name'] = computer.Name
            sources['WMI_ComputerSystem_DNSHostName'] = getattr(computer, 'DNSHostName', 'Not Available')
            sources['WMI_ComputerSystem_Domain'] = getattr(computer, 'Domain', 'Not Available')
            break
        
        # Win32_OperatingSystem
        for os_info in c.Win32_OperatingSystem():
            sources['WMI_OperatingSystem_CSName'] = getattr(os_info, 'CSName', 'Not Available')
            break
        
        # Win32_NetworkAdapterConfiguration
        for adapter in c.Win32_NetworkAdapterConfiguration():
            if hasattr(adapter, 'DNSHostName') and adapter.DNSHostName:
                sources['WMI_NetworkAdapter_DNSHostName'] = adapter.DNSHostName
                break
        
    except ImportError:
        sources['WMI_Status'] = 'WMI module not available'
    except Exception as e:
        sources['WMI_Status'] = f'WMI Error: {e}'
    
    # 6. Python标准库
    print("  🐍 Python标准库...")
    try:
        sources['Python_platform_node'] = platform.node()
        sources['Python_platform_uname'] = platform.uname().node
        sources['Python_socket_gethostname'] = socket.gethostname()
        sources['Python_socket_getfqdn'] = socket.getfqdn()
    except Exception as e:
        sources['Python_Error'] = str(e)
    
    # 7. 命令行工具
    print("  💻 命令行工具...")
    commands = [
        ('hostname', ['hostname']),
        ('whoami', ['whoami']),
        ('systeminfo', ['systeminfo', '/fo', 'csv']),
        ('wmic_computersystem', ['wmic', 'computersystem', 'get', 'name', '/value']),
        ('wmic_os', ['wmic', 'os', 'get', 'csname', '/value']),
    ]
    
    for name, cmd in commands:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                output = result.stdout.strip()
                if name == 'systeminfo':
                    # 解析systeminfo输出中的计算机名
                    lines = output.split('\n')
                    if len(lines) > 1:
                        fields = lines[1].split(',')
                        if len(fields) > 1:
                            sources[f'CMD_{name}'] = fields[1].strip('"')
                        else:
                            sources[f'CMD_{name}'] = 'Parse Error'
                    else:
                        sources[f'CMD_{name}'] = 'No Data'
                elif name.startswith('wmic'):
                    # 解析WMIC输出
                    for line in output.split('\n'):
                        if '=' in line and 'Name' in line:
                            sources[f'CMD_{name}'] = line.split('=')[1].strip()
                            break
                    else:
                        sources[f'CMD_{name}'] = output[:50] if output else 'No Output'
                else:
                    sources[f'CMD_{name}'] = output
            else:
                sources[f'CMD_{name}'] = f'Error: {result.returncode}'
        except Exception as e:
            sources[f'CMD_{name}'] = f'Exception: {e}'
    
    return sources

def print_sources_analysis(sources, title):
    """打印来源分析"""
    print(f"\n{'='*80}")
    print(f"{title}")
    print(f"{'='*80}")
    
    categories = {
        'ENV_': '🌍 环境变量',
        'API_': '🔧 Windows API',
        'REG_': '📝 注册表',
        'WMI_': '🖥️ WMI查询',
        'Python_': '🐍 Python标准库',
        'CMD_': '💻 命令行工具'
    }
    
    for prefix, category_name in categories.items():
        print(f"\n{category_name}:")
        found_items = False
        for key, value in sources.items():
            if key.startswith(prefix):
                print(f"  {key[len(prefix):]:35}: {value}")
                found_items = True
        if not found_items:
            print(f"  无相关项目")

def find_unchanged_sources(original_sources, modified_sources):
    """找出未改变的来源"""
    unchanged = []
    
    for key in original_sources:
        if key in modified_sources:
            if original_sources[key] == modified_sources[key]:
                if 'PC-20250807RRZC' in str(original_sources[key]):
                    unchanged.append(key)
    
    return unchanged

def main():
    """主函数"""
    print("🎯 计算机名获取来源诊断工具")
    print("=" * 80)
    print("找出QingTalk可能使用的所有计算机名获取方式")
    print("=" * 80)
    
    # 获取当前所有来源
    print("📋 获取修改前的所有来源...")
    current_sources = get_all_computer_name_sources()
    
    print_sources_analysis(current_sources, "当前所有计算机名来源")
    
    # 分析哪些来源还显示原始名称
    print(f"\n🔍 分析结果:")
    original_name = "PC-20250807RRZC"
    unchanged_sources = []
    
    for key, value in current_sources.items():
        if original_name in str(value):
            unchanged_sources.append(key)
    
    if unchanged_sources:
        print(f"\n⚠️ 以下来源仍显示原始名称 '{original_name}':")
        for source in unchanged_sources:
            print(f"  🔴 {source}: {current_sources[source]}")
        
        print(f"\n💡 QingTalk可能使用以上任一来源获取设备信息")
        print(f"需要修改这些来源才能完全伪装设备名称")
    else:
        print(f"\n✅ 所有来源都已更改，不再显示原始名称")
    
    print(f"\n📋 建议:")
    print(f"1. 重点关注WMI查询结果")
    print(f"2. 检查是否有遗漏的注册表项")
    print(f"3. 确认所有API调用都返回新名称")

if __name__ == "__main__":
    main()
