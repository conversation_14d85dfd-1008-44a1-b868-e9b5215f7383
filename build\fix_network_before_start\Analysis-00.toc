(['D:\\text\\py\\duokai\\fix_network_before_start.py'],
 ['D:\\text\\py\\duokai'],
 [],
 [('D:\\Software\\develop\\Python311\\Lib\\site-packages\\mitmproxy\\utils\\pyinstaller',
   0),
  ('D:\\Software\\develop\\Python311\\Lib\\site-packages\\mitmproxy_rs\\_pyinstaller',
   0),
  ('D:\\Software\\develop\\Python311\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\Software\\develop\\Python311\\Lib\\site-packages\\pygame\\__pyinstaller',
   0),
  ('D:\\Software\\develop\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Software\\develop\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('icon.ico', 'D:\\text\\py\\duokai\\icon.ico', 'DATA')],
 '3.11.3 (tags/v3.11.3:f3909b8, Apr  4 2023, 23:49:59) [MSC v.1934 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('fix_network_before_start',
   'D:\\text\\py\\duokai\\fix_network_before_start.py',
   'PYSOURCE')],
 [('zipfile', 'D:\\Software\\develop\\Python311\\Lib\\zipfile.py', 'PYMODULE'),
  ('argparse',
   'D:\\Software\\develop\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Software\\develop\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy', 'D:\\Software\\develop\\Python311\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Software\\develop\\Python311\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile',
   'D:\\Software\\develop\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\Software\\develop\\Python311\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Software\\develop\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Software\\develop\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Software\\develop\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Software\\develop\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\Software\\develop\\Python311\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Software\\develop\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Software\\develop\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Software\\develop\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('random', 'D:\\Software\\develop\\Python311\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\Software\\develop\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal', 'D:\\Software\\develop\\Python311\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Software\\develop\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Software\\develop\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'D:\\Software\\develop\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers', 'D:\\Software\\develop\\Python311\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Software\\develop\\Python311\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\Software\\develop\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Software\\develop\\Python311\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Software\\develop\\Python311\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\Software\\develop\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Software\\develop\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('bisect', 'D:\\Software\\develop\\Python311\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Software\\develop\\Python311\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Software\\develop\\Python311\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'D:\\Software\\develop\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Software\\develop\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Software\\develop\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Software\\develop\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Software\\develop\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Software\\develop\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'D:\\Software\\develop\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Software\\develop\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Software\\develop\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('socket', 'D:\\Software\\develop\\Python311\\Lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'D:\\Software\\develop\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri', 'D:\\Software\\develop\\Python311\\Lib\\quopri.py', 'PYMODULE'),
  ('email',
   'D:\\Software\\develop\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\Software\\develop\\Python311\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'D:\\Software\\develop\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token', 'D:\\Software\\develop\\Python311\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\Software\\develop\\Python311\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\Software\\develop\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\Software\\develop\\Python311\\Lib\\bz2.py', 'PYMODULE'),
  ('pathlib', 'D:\\Software\\develop\\Python311\\Lib\\pathlib.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Software\\develop\\Python311\\Lib\\fnmatch.py', 'PYMODULE'),
  ('contextlib',
   'D:\\Software\\develop\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('threading',
   'D:\\Software\\develop\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Software\\develop\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'D:\\Software\\develop\\Python311\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\Software\\develop\\Python311\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Software\\develop\\Python311\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Software\\develop\\Python311\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Software\\develop\\Python311\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Software\\develop\\Python311\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Software\\develop\\Python311\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Software\\develop\\Python311\\Lib\\ast.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Software\\develop\\Python311\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Software\\develop\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\Software\\develop\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Software\\develop\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Software\\develop\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('json',
   'D:\\Software\\develop\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Software\\develop\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Software\\develop\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Software\\develop\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\Software\\develop\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Software\\develop\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'D:\\Software\\develop\\Python311\\Lib\\signal.py', 'PYMODULE')],
 [('python311.dll',
   'D:\\Software\\develop\\Python311\\python311.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Software\\develop\\Python311\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Software\\develop\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Software\\develop\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Software\\develop\\Python311\\DLLs\\libffi-8.dll',
   'BINARY')],
 [],
 [],
 [('icon.ico', 'D:\\text\\py\\duokai\\icon.ico', 'DATA'),
  ('base_library.zip',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\base_library.zip',
   'DATA')],
 [('linecache',
   'D:\\Software\\develop\\Python311\\Lib\\linecache.py',
   'PYMODULE'),
  ('keyword', 'D:\\Software\\develop\\Python311\\Lib\\keyword.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Software\\develop\\Python311\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('sre_compile',
   'D:\\Software\\develop\\Python311\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('reprlib', 'D:\\Software\\develop\\Python311\\Lib\\reprlib.py', 'PYMODULE'),
  ('enum', 'D:\\Software\\develop\\Python311\\Lib\\enum.py', 'PYMODULE'),
  ('heapq', 'D:\\Software\\develop\\Python311\\Lib\\heapq.py', 'PYMODULE'),
  ('copyreg', 'D:\\Software\\develop\\Python311\\Lib\\copyreg.py', 'PYMODULE'),
  ('ntpath', 'D:\\Software\\develop\\Python311\\Lib\\ntpath.py', 'PYMODULE'),
  ('abc', 'D:\\Software\\develop\\Python311\\Lib\\abc.py', 'PYMODULE'),
  ('types', 'D:\\Software\\develop\\Python311\\Lib\\types.py', 'PYMODULE'),
  ('posixpath',
   'D:\\Software\\develop\\Python311\\Lib\\posixpath.py',
   'PYMODULE'),
  ('warnings',
   'D:\\Software\\develop\\Python311\\Lib\\warnings.py',
   'PYMODULE'),
  ('genericpath',
   'D:\\Software\\develop\\Python311\\Lib\\genericpath.py',
   'PYMODULE'),
  ('io', 'D:\\Software\\develop\\Python311\\Lib\\io.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\Software\\develop\\Python311\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\Software\\develop\\Python311\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Software\\develop\\Python311\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('weakref', 'D:\\Software\\develop\\Python311\\Lib\\weakref.py', 'PYMODULE'),
  ('traceback',
   'D:\\Software\\develop\\Python311\\Lib\\traceback.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Software\\develop\\Python311\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('stat', 'D:\\Software\\develop\\Python311\\Lib\\stat.py', 'PYMODULE'),
  ('operator',
   'D:\\Software\\develop\\Python311\\Lib\\operator.py',
   'PYMODULE'),
  ('functools',
   'D:\\Software\\develop\\Python311\\Lib\\functools.py',
   'PYMODULE'),
  ('codecs', 'D:\\Software\\develop\\Python311\\Lib\\codecs.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\Software\\develop\\Python311\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('locale', 'D:\\Software\\develop\\Python311\\Lib\\locale.py', 'PYMODULE'),
  ('re._parser',
   'D:\\Software\\develop\\Python311\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'D:\\Software\\develop\\Python311\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\Software\\develop\\Python311\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\Software\\develop\\Python311\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re', 'D:\\Software\\develop\\Python311\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('sre_parse',
   'D:\\Software\\develop\\Python311\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('os', 'D:\\Software\\develop\\Python311\\Lib\\os.py', 'PYMODULE')])
