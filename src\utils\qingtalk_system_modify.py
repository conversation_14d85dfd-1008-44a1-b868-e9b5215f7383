#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk系统信息修改方案
临时修改系统设备信息来测试QingTalk检测
"""

import os
import subprocess
import winreg
import tempfile
import shutil
from typing import Optional, Dict

class SystemInfoModifier:
    """系统信息修改器"""
    
    def __init__(self):
        self.backup_values = {}
        self.modified_keys = []
    
    def backup_registry_value(self, hkey, subkey: str, value_name: str) -> bool:
        """备份注册表值"""
        try:
            with winreg.OpenKey(hkey, subkey) as key:
                original_value, value_type = winreg.QueryValueEx(key, value_name)
                
                backup_key = f"{hkey}\\{subkey}\\{value_name}"
                self.backup_values[backup_key] = (original_value, value_type)
                
                print(f"  📋 备份: {subkey}\\{value_name} = {original_value}")
                return True
                
        except Exception as e:
            print(f"  ❌ 备份失败: {subkey}\\{value_name} - {e}")
            return False
    
    def modify_registry_value(self, hkey, subkey: str, value_name: str, new_value: str) -> bool:
        """修改注册表值"""
        try:
            # 先备份
            if not self.backup_registry_value(hkey, subkey, value_name):
                return False
            
            # 修改值
            with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, new_value)
                
            self.modified_keys.append((hkey, subkey, value_name))
            print(f"  ✅ 修改: {subkey}\\{value_name} = {new_value}")
            return True
            
        except Exception as e:
            print(f"  ❌ 修改失败: {subkey}\\{value_name} - {e}")
            return False
    
    def restore_all_values(self):
        """恢复所有修改的值"""
        print("🔄 恢复原始系统信息...")
        
        for backup_key, (original_value, value_type) in self.backup_values.items():
            try:
                # 解析备份键
                parts = backup_key.split('\\')
                hkey_str = parts[0]
                subkey = '\\'.join(parts[1:-1])
                value_name = parts[-1]
                
                # 转换hkey
                if 'HKEY_LOCAL_MACHINE' in hkey_str:
                    hkey = winreg.HKEY_LOCAL_MACHINE
                elif 'HKEY_CURRENT_USER' in hkey_str:
                    hkey = winreg.HKEY_CURRENT_USER
                else:
                    continue
                
                # 恢复值
                with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                    winreg.SetValueEx(key, value_name, 0, value_type, original_value)
                    
                print(f"  ✅ 恢复: {subkey}\\{value_name} = {original_value}")
                
            except Exception as e:
                print(f"  ❌ 恢复失败: {backup_key} - {e}")

def modify_system_device_info(fake_computer: str, fake_user: str = None) -> SystemInfoModifier:
    """修改系统设备信息"""
    
    print(f"🔧 修改系统设备信息")
    print(f"  🖥️ 目标计算机名: {fake_computer}")
    if fake_user:
        print(f"  👤 目标用户名: {fake_user}")
    
    modifier = SystemInfoModifier()
    
    try:
        # 检查管理员权限
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print(f"  ❌ 需要管理员权限来修改注册表")
            return modifier
        
        # 修改计算机名相关注册表项
        computer_keys = [
            (winreg.HKEY_LOCAL_MACHINE, 
             r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", 
             "ComputerName"),
            (winreg.HKEY_LOCAL_MACHINE, 
             r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", 
             "ComputerName"),
            (winreg.HKEY_LOCAL_MACHINE, 
             r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", 
             "Hostname"),
            (winreg.HKEY_LOCAL_MACHINE, 
             r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", 
             "NV Hostname"),
        ]
        
        success_count = 0
        for hkey, subkey, value_name in computer_keys:
            if modifier.modify_registry_value(hkey, subkey, value_name, fake_computer):
                success_count += 1
        
        print(f"  📊 成功修改 {success_count}/{len(computer_keys)} 个注册表项")
        
        if success_count > 0:
            print(f"  ⚠️ 注意: 某些更改可能需要重启才能生效")
            print(f"  💡 建议: 测试完成后立即恢复原始值")
        
        return modifier
        
    except Exception as e:
        print(f"  ❌ 修改系统信息失败: {e}")
        return modifier

def launch_qingtalk_with_modified_system(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """在修改系统信息后启动QingTalk"""
    
    print(f"🔧 QingTalk系统信息修改启动 (实例: {instance_id[:8]})")
    
    # 生成伪造的设备信息
    import random
    import hashlib
    
    unique_suffix = instance_id[:8]
    seed_value = int(hashlib.md5(instance_id.encode()).hexdigest()[:8], 16)
    random.seed(seed_value)
    
    fake_computer = f'QTTEST-{unique_suffix.upper()}'
    
    print(f"  🎯 准备修改系统信息...")
    
    # 修改系统信息
    modifier = modify_system_device_info(fake_computer)
    
    try:
        # 等待注册表更改生效
        print(f"  ⏳ 等待系统信息更新...")
        import time
        time.sleep(3)
        
        # 启动QingTalk
        print(f"  🚀 启动QingTalk...")
        
        # 使用完美方案作为基础
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from src.utils.qingtalk_perfect import launch_qingtalk_perfect
        
        process = launch_qingtalk_perfect(exe_path, instance_id)
        
        if process and hasattr(process, 'isolation_info'):
            process.isolation_info['method'] = 'qingtalk_system_modify'
            process.isolation_info['system_modifier'] = modifier
            process.isolation_info['fake_computer'] = fake_computer
        
        return process
        
    except Exception as e:
        print(f"  ❌ 启动失败: {e}")
        # 如果启动失败，立即恢复系统信息
        modifier.restore_all_values()
        return None

def cleanup_system_modify(process):
    """清理系统修改"""
    try:
        if hasattr(process, 'isolation_info') and 'system_modifier' in process.isolation_info:
            modifier = process.isolation_info['system_modifier']
            modifier.restore_all_values()
            print(f"🔄 系统信息已恢复")
    except Exception as e:
        print(f"清理系统修改失败: {e}")

def test_current_device_info():
    """测试当前设备信息获取方法"""
    print("🔍 测试当前设备信息获取方法")
    
    methods = [
        ("环境变量 COMPUTERNAME", lambda: os.environ.get('COMPUTERNAME', 'Unknown')),
        ("环境变量 USERNAME", lambda: os.environ.get('USERNAME', 'Unknown')),
        ("Python platform.node()", lambda: __import__('platform').node()),
        ("命令行 hostname", lambda: subprocess.run(['hostname'], capture_output=True, text=True).stdout.strip()),
        ("命令行 whoami", lambda: subprocess.run(['whoami'], capture_output=True, text=True).stdout.strip()),
    ]
    
    print("📋 当前设备信息:")
    for name, method in methods:
        try:
            result = method()
            print(f"  {name}: {result}")
        except Exception as e:
            print(f"  {name}: 获取失败 - {e}")

def create_test_script():
    """创建测试脚本"""
    
    script_content = '''
@echo off
echo ========================================
echo QingTalk设备信息修改测试
echo ========================================

echo 1. 修改前的设备信息:
hostname
whoami

echo.
echo 2. 启动QingTalk测试...
python src/utils/qingtalk_system_modify.py

echo.
echo 3. 修改后的设备信息:
hostname
whoami

echo.
echo 测试完成，按任意键退出...
pause
'''
    
    with open('test_device_modify.bat', 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print("📝 已创建测试脚本: test_device_modify.bat")
    print("💡 以管理员身份运行该脚本进行测试")

if __name__ == "__main__":
    print("QingTalk系统信息修改测试")
    
    # 测试当前设备信息
    test_current_device_info()
    
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("\n🧪 测试系统信息修改方案...")
        
        import uuid
        process = launch_qingtalk_with_modified_system(qingtalk_exe, str(uuid.uuid4()))
        
        if process:
            print("🎉 启动成功！")
            print("💡 请检查QingTalk显示的设备信息是否有变化")
            print("⚠️ 测试完成后会自动恢复系统信息")
            
            input("按回车键停止测试并恢复系统信息...")
            
            try:
                process.terminate()
                cleanup_system_modify(process)
            except:
                pass
        else:
            print("❌ 启动失败")
    else:
        print("❌ QingTalk程序不存在")
        
    # 创建测试脚本
    create_test_script()
