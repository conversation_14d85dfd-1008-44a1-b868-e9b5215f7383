#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试
只测试核心启动功能
"""

import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_subprocess():
    """直接测试subprocess启动"""
    print("=" * 50)
    print("直接subprocess测试")
    print("=" * 50)
    
    test_exe = r"C:\Windows\System32\notepad.exe"
    
    if not os.path.exists(test_exe):
        print(f"❌ 测试程序不存在: {test_exe}")
        return False
    
    try:
        print(f"📝 启动程序: {test_exe}")
        
        # 直接启动
        process = subprocess.Popen(
            test_exe,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ 进程启动成功 (PID: {process.pid})")
        
        # 检查进程状态
        time.sleep(2)
        if process.poll() is None:
            print("✅ 进程正在运行")
            
            input("按回车键关闭进程...")
            
            process.terminate()
            process.wait(timeout=5)
            print("✅ 进程已关闭")
            return True
        else:
            print("❌ 进程启动后立即退出")
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def test_instance_start_method():
    """测试实例的start方法"""
    print("\n" + "=" * 50)
    print("实例start方法测试")
    print("=" * 50)
    
    try:
        from src.core.instance_manager import Instance
        
        test_exe = r"C:\Windows\System32\notepad.exe"
        
        if not os.path.exists(test_exe):
            print(f"❌ 测试程序不存在: {test_exe}")
            return False
        
        print(f"📝 创建实例对象...")
        instance = Instance("test-instance", test_exe)
        print(f"✅ 实例对象创建成功")
        
        print(f"⏳ 调用start方法...")
        success = instance.start()
        
        if success:
            print(f"✅ start方法返回成功")
            print(f"   PID: {instance.pid}")
            print(f"   状态: {instance.status}")
            
            # 检查进程状态
            time.sleep(2)
            is_running = instance.is_running()
            print(f"   运行状态: {'运行中' if is_running else '已停止'}")
            
            if is_running:
                print("🎉 实例确实在运行！")
                
                input("按回车键停止实例...")
                
                stop_success = instance.stop()
                if stop_success:
                    print("✅ 实例停止成功")
                else:
                    print("❌ 实例停止失败")
                
                return True
            else:
                print("❌ 实例启动后立即停止")
                return False
        else:
            print("❌ start方法返回失败")
            return False
            
    except Exception as e:
        print(f"❌ 实例测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sandbox_creation():
    """测试沙盒创建"""
    print("\n" + "=" * 50)
    print("沙盒创建测试")
    print("=" * 50)
    
    try:
        from src.core.sandbox_manager import SandboxManager
        
        print("📝 创建沙盒管理器...")
        sandbox_manager = SandboxManager("test-sandbox")
        print("✅ 沙盒管理器创建成功")
        
        print("⏳ 创建沙盒环境...")
        sandbox_env = sandbox_manager.create_sandbox()
        
        if sandbox_env:
            print("✅ 沙盒环境创建成功")
            print(f"   工作目录: {sandbox_env.get('work_dir')}")
            print(f"   临时目录: {sandbox_env.get('temp_dir')}")
            
            # 检查目录是否存在
            work_dir = sandbox_env.get('work_dir')
            if work_dir and os.path.exists(work_dir):
                print("✅ 沙盒目录确实存在")
                
                # 清理沙盒
                sandbox_manager.cleanup_sandbox()
                print("✅ 沙盒清理完成")
                return True
            else:
                print("❌ 沙盒目录不存在")
                return False
        else:
            print("❌ 沙盒环境创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 沙盒测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("最小化功能测试")
    
    # 运行各项测试
    subprocess_ok = test_direct_subprocess()
    instance_ok = test_instance_start_method()
    sandbox_ok = test_sandbox_creation()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"直接subprocess: {'✅ 通过' if subprocess_ok else '❌ 失败'}")
    print(f"实例start方法: {'✅ 通过' if instance_ok else '❌ 失败'}")
    print(f"沙盒创建: {'✅ 通过' if sandbox_ok else '❌ 失败'}")
    
    if subprocess_ok and instance_ok and sandbox_ok:
        print("\n🎉 所有基础功能正常！")
    else:
        print("\n❌ 存在基础功能问题，需要修复")
        
        if not subprocess_ok:
            print("   - subprocess启动有问题")
        if not instance_ok:
            print("   - 实例启动方法有问题")
        if not sandbox_ok:
            print("   - 沙盒创建有问题")
    
    input("\n按回车键退出...")
