#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试多开功能
详细查看启动过程
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config_manager import ConfigManager
from src.core.instance_manager import InstanceManager

def debug_instance_creation():
    """调试实例创建过程"""
    print("=" * 50)
    print("调试实例创建过程")
    print("=" * 50)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        config_manager.load_config()
        print("✅ 配置管理器创建成功")
        
        # 创建实例管理器
        instance_manager = InstanceManager(config_manager)
        print("✅ 实例管理器创建成功")
        
        # 测试程序路径
        test_exe = r"C:\Windows\System32\notepad.exe"
        
        if not os.path.exists(test_exe):
            print(f"❌ 测试程序不存在: {test_exe}")
            return False
        
        print(f"📝 使用测试程序: {test_exe}")
        
        # 创建一个实例
        print("\n🚀 创建实例...")
        instance_id = instance_manager.create_instance(test_exe)
        print(f"✅ 实例创建成功: {instance_id}")
        
        # 获取实例对象
        instance = instance_manager.get_instance(instance_id)
        if not instance:
            print("❌ 无法获取实例对象")
            return False
        
        print(f"✅ 实例对象获取成功")
        print(f"   实例ID: {instance.id}")
        print(f"   EXE路径: {instance.exe_path}")
        print(f"   状态: {instance.status}")
        print(f"   代理配置: {instance.proxy_config}")
        
        # 尝试启动实例
        print("\n⏳ 启动实例...")
        success = instance_manager.start_instance(instance_id)
        
        if success:
            print("✅ 实例启动成功")
            print(f"   PID: {instance.pid}")
            print(f"   状态: {instance.status}")
            
            # 检查进程是否真的在运行
            time.sleep(2)
            is_running = instance.is_running()
            print(f"   运行状态: {'运行中' if is_running else '已停止'}")
            
            if is_running:
                print("🎉 实例确实在运行！")
                
                # 等待用户确认
                input("\n按回车键停止实例...")
                
                # 停止实例
                print("\n🛑 停止实例...")
                stop_success = instance_manager.stop_instance(instance_id)
                if stop_success:
                    print("✅ 实例停止成功")
                else:
                    print("❌ 实例停止失败")
            else:
                print("❌ 实例启动后立即停止了")
        else:
            print("❌ 实例启动失败")
        
        # 清理实例
        print("\n🧹 清理实例...")
        instance_manager.remove_instance(instance_id)
        print("✅ 实例清理完成")
        
        return success
        
    except Exception as e:
        print(f"❌ 调试过程失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def debug_multiple_instances():
    """调试多个实例"""
    print("\n" + "=" * 50)
    print("调试多个实例")
    print("=" * 50)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        config_manager.load_config()
        
        # 创建实例管理器
        instance_manager = InstanceManager(config_manager)
        
        # 测试程序路径
        test_exe = r"C:\Windows\System32\notepad.exe"
        
        if not os.path.exists(test_exe):
            print(f"❌ 测试程序不存在: {test_exe}")
            return False
        
        print(f"📝 使用测试程序: {test_exe}")
        
        # 创建多个实例
        instance_count = 2
        instance_ids = []
        
        print(f"\n🚀 创建 {instance_count} 个实例...")
        for i in range(instance_count):
            instance_id = instance_manager.create_instance(test_exe)
            instance_ids.append(instance_id)
            print(f"✅ 实例 {i+1} 创建成功: {instance_id[:8]}...")
        
        # 逐个启动实例
        print(f"\n⏳ 启动实例...")
        running_instances = []
        
        for i, instance_id in enumerate(instance_ids):
            print(f"\n启动实例 {i+1}...")
            success = instance_manager.start_instance(instance_id)
            
            if success:
                instance = instance_manager.get_instance(instance_id)
                print(f"✅ 实例 {i+1} 启动成功 (PID: {instance.pid})")
                
                # 检查是否真的在运行
                time.sleep(1)
                if instance.is_running():
                    print(f"✅ 实例 {i+1} 确认运行中")
                    running_instances.append(instance_id)
                else:
                    print(f"❌ 实例 {i+1} 启动后停止了")
            else:
                print(f"❌ 实例 {i+1} 启动失败")
        
        print(f"\n📊 运行统计: {len(running_instances)}/{instance_count} 个实例成功运行")
        
        if len(running_instances) > 1:
            print("🎉 多开功能正常工作！")
            success = True
        elif len(running_instances) == 1:
            print("⚠️ 只有一个实例运行，可能存在单实例限制")
            success = False
        else:
            print("❌ 没有实例成功运行")
            success = False
        
        # 显示运行中的实例信息
        if running_instances:
            print("\n📋 运行中的实例:")
            for instance_id in running_instances:
                instance = instance_manager.get_instance(instance_id)
                print(f"   - {instance_id[:8]}... (PID: {instance.pid})")
        
        # 等待用户确认
        input("\n按回车键停止所有实例...")
        
        # 停止所有实例
        print("\n🛑 停止所有实例...")
        for i, instance_id in enumerate(instance_ids):
            instance_manager.stop_instance(instance_id)
            print(f"✅ 实例 {i+1} 已停止")
        
        # 清理实例
        print("\n🧹 清理实例...")
        for instance_id in instance_ids:
            instance_manager.remove_instance(instance_id)
        print("✅ 清理完成")
        
        return success
        
    except Exception as e:
        print(f"❌ 多实例调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("多开功能调试工具")
    
    # 运行调试
    single_ok = debug_instance_creation()
    multiple_ok = debug_multiple_instances()
    
    print("\n" + "=" * 50)
    print("调试结果汇总")
    print("=" * 50)
    print(f"单实例测试: {'✅ 通过' if single_ok else '❌ 失败'}")
    print(f"多实例测试: {'✅ 通过' if multiple_ok else '❌ 失败'}")
    
    if single_ok and multiple_ok:
        print("\n🎉 多开功能完全正常！")
    elif single_ok:
        print("\n⚠️ 单实例正常，但多开可能受限")
        print("这可能是目标程序的单实例限制造成的")
    else:
        print("\n❌ 实例启动存在问题")
    
    input("\n按回车键退出...")
