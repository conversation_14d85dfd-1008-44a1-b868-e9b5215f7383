#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员权限工具
检查和提升管理员权限
"""

import ctypes
import sys
import os
from tkinter import messagebox

def is_admin():
    """检查是否有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行程序"""
    try:
        if is_admin():
            return True
        else:
            # 以管理员身份重新运行
            ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                sys.executable, 
                " ".join(sys.argv), 
                None, 
                1
            )
            return False
    except Exception as e:
        print(f"提升权限失败: {e}")
        return False

def check_admin_and_prompt():
    """检查管理员权限并提示用户"""
    if not is_admin():
        result = messagebox.askyesno(
            "权限提示",
            "检测到程序没有管理员权限。\n\n"
            "某些功能（如修改系统标识）需要管理员权限才能正常工作。\n\n"
            "是否要以管理员身份重新启动程序？\n\n"
            "选择'否'将继续运行，但部分功能可能受限。"
        )
        
        if result:
            # 用户选择重新启动
            if run_as_admin():
                return True
            else:
                # 重新启动失败或用户取消
                sys.exit(0)
        else:
            # 用户选择继续运行
            messagebox.showinfo(
                "提示",
                "程序将在受限模式下运行。\n\n"
                "如需完整功能，请右键点击程序图标，选择'以管理员身份运行'。"
            )
            return False
    
    return True

def get_admin_status_text():
    """获取管理员状态文本"""
    if is_admin():
        return "✅ 管理员权限"
    else:
        return "⚠️ 受限权限"
