('D:\\text\\py\\duokai\\build\\main\\PYZ-00.pyz',
 [('OpenSSL',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Software\\develop\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Software\\develop\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Software\\develop\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Software\\develop\\Python311\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Software\\develop\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Software\\develop\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Software\\develop\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'D:\\Software\\develop\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\Software\\develop\\Python311\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\Software\\develop\\Python311\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Software\\develop\\Python311\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bz2', 'D:\\Software\\develop\\Python311\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\Software\\develop\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Software\\develop\\Python311\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Software\\develop\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Software\\develop\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Software\\develop\\Python311\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\Software\\develop\\Python311\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Software\\develop\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Software\\develop\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Software\\develop\\Python311\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Software\\develop\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Software\\develop\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal', 'D:\\Software\\develop\\Python311\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\Software\\develop\\Python311\\Lib\\dis.py', 'PYMODULE'),
  ('email',
   'D:\\Software\\develop\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Software\\develop\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Software\\develop\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Software\\develop\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Software\\develop\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Software\\develop\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Software\\develop\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Software\\develop\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Software\\develop\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Software\\develop\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Software\\develop\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Software\\develop\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Software\\develop\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Software\\develop\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Software\\develop\\Python311\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\Software\\develop\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Software\\develop\\Python311\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Software\\develop\\Python311\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Software\\develop\\Python311\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Software\\develop\\Python311\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\Software\\develop\\Python311\\Lib\\gzip.py', 'PYMODULE'),
  ('h2',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\__init__.py',
   'PYMODULE'),
  ('h2.config',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\config.py',
   'PYMODULE'),
  ('h2.connection',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\connection.py',
   'PYMODULE'),
  ('h2.errors',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\errors.py',
   'PYMODULE'),
  ('h2.events',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\events.py',
   'PYMODULE'),
  ('h2.exceptions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\exceptions.py',
   'PYMODULE'),
  ('h2.frame_buffer',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\frame_buffer.py',
   'PYMODULE'),
  ('h2.settings',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\settings.py',
   'PYMODULE'),
  ('h2.stream',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\stream.py',
   'PYMODULE'),
  ('h2.utilities',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\utilities.py',
   'PYMODULE'),
  ('h2.windows',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\h2\\windows.py',
   'PYMODULE'),
  ('hashlib', 'D:\\Software\\develop\\Python311\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Software\\develop\\Python311\\Lib\\hmac.py', 'PYMODULE'),
  ('hpack',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\__init__.py',
   'PYMODULE'),
  ('hpack.exceptions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\exceptions.py',
   'PYMODULE'),
  ('hpack.hpack',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\hpack.py',
   'PYMODULE'),
  ('hpack.huffman',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\huffman.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\huffman_table.py',
   'PYMODULE'),
  ('hpack.struct',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\struct.py',
   'PYMODULE'),
  ('hpack.table',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hpack\\table.py',
   'PYMODULE'),
  ('http',
   'D:\\Software\\develop\\Python311\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Software\\develop\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Software\\develop\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Software\\develop\\Python311\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('hyperframe',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hyperframe\\__init__.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hyperframe\\exceptions.py',
   'PYMODULE'),
  ('hyperframe.flags',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hyperframe\\flags.py',
   'PYMODULE'),
  ('hyperframe.frame',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\hyperframe\\frame.py',
   'PYMODULE'),
  ('idna',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Software\\develop\\Python311\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress',
   'D:\\Software\\develop\\Python311\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'D:\\Software\\develop\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Software\\develop\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Software\\develop\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Software\\develop\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Software\\develop\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Software\\develop\\Python311\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\Software\\develop\\Python311\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('netrc', 'D:\\Software\\develop\\Python311\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Software\\develop\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\Software\\develop\\Python311\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Software\\develop\\Python311\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\Software\\develop\\Python311\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Software\\develop\\Python311\\Lib\\pickle.py', 'PYMODULE'),
  ('platform',
   'D:\\Software\\develop\\Python311\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint', 'D:\\Software\\develop\\Python311\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Software\\develop\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('queue', 'D:\\Software\\develop\\Python311\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Software\\develop\\Python311\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Software\\develop\\Python311\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors',
   'D:\\Software\\develop\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('shlex', 'D:\\Software\\develop\\Python311\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Software\\develop\\Python311\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Software\\develop\\Python311\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Software\\develop\\Python311\\Lib\\socket.py', 'PYMODULE'),
  ('socks',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('src', 'D:\\text\\py\\duokai\\src\\__init__.py', 'PYMODULE'),
  ('src.core', 'D:\\text\\py\\duokai\\src\\core\\__init__.py', 'PYMODULE'),
  ('src.core.config_manager',
   'D:\\text\\py\\duokai\\src\\core\\config_manager.py',
   'PYMODULE'),
  ('src.core.instance_manager',
   'D:\\text\\py\\duokai\\src\\core\\instance_manager.py',
   'PYMODULE'),
  ('src.core.proxy_manager',
   'D:\\text\\py\\duokai\\src\\core\\proxy_manager.py',
   'PYMODULE'),
  ('src.core.sandbox_manager',
   'D:\\text\\py\\duokai\\src\\core\\sandbox_manager.py',
   'PYMODULE'),
  ('src.core.system_modifier',
   'D:\\text\\py\\duokai\\src\\core\\system_modifier.py',
   'PYMODULE'),
  ('src.gui', 'D:\\text\\py\\duokai\\src\\gui\\__init__.py', 'PYMODULE'),
  ('src.gui.simple_main_window',
   'D:\\text\\py\\duokai\\src\\gui\\simple_main_window.py',
   'PYMODULE'),
  ('src.utils', 'D:\\text\\py\\duokai\\src\\utils\\__init__.py', 'PYMODULE'),
  ('src.utils.admin_utils',
   'D:\\text\\py\\duokai\\src\\utils\\admin_utils.py',
   'PYMODULE'),
  ('src.utils.app_specific',
   'D:\\text\\py\\duokai\\src\\utils\\app_specific.py',
   'PYMODULE'),
  ('src.utils.device_modifier',
   'D:\\text\\py\\duokai\\src\\utils\\device_modifier.py',
   'PYMODULE'),
  ('src.utils.mutex_bypass',
   'D:\\text\\py\\duokai\\src\\utils\\mutex_bypass.py',
   'PYMODULE'),
  ('src.utils.network_fix',
   'D:\\text\\py\\duokai\\src\\utils\\network_fix.py',
   'PYMODULE'),
  ('src.utils.persistent_data',
   'D:\\text\\py\\duokai\\src\\utils\\persistent_data.py',
   'PYMODULE'),
  ('src.utils.process_utils',
   'D:\\text\\py\\duokai\\src\\utils\\process_utils.py',
   'PYMODULE'),
  ('src.utils.qingtalk_final',
   'D:\\text\\py\\duokai\\src\\utils\\qingtalk_final.py',
   'PYMODULE'),
  ('src.utils.qingtalk_launcher',
   'D:\\text\\py\\duokai\\src\\utils\\qingtalk_launcher.py',
   'PYMODULE'),
  ('src.utils.qingtalk_optimized',
   'D:\\text\\py\\duokai\\src\\utils\\qingtalk_optimized.py',
   'PYMODULE'),
  ('src.utils.qingtalk_perfect',
   'D:\\text\\py\\duokai\\src\\utils\\qingtalk_perfect.py',
   'PYMODULE'),
  ('src.utils.qingtalk_persistent_login',
   'D:\\text\\py\\duokai\\src\\utils\\qingtalk_persistent_login.py',
   'PYMODULE'),
  ('src.utils.qingtalk_ultimate',
   'D:\\text\\py\\duokai\\src\\utils\\qingtalk_ultimate.py',
   'PYMODULE'),
  ('ssl', 'D:\\Software\\develop\\Python311\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\Software\\develop\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Software\\develop\\Python311\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Software\\develop\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Software\\develop\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Software\\develop\\Python311\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile',
   'D:\\Software\\develop\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Software\\develop\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\Software\\develop\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Software\\develop\\Python311\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\Software\\develop\\Python311\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\Software\\develop\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Software\\develop\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'D:\\Software\\develop\\Python311\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\Software\\develop\\Python311\\Lib\\uuid.py', 'PYMODULE'),
  ('win32con',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Software\\develop\\Python311\\Lib\\zipfile.py', 'PYMODULE'),
  ('zstandard',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
