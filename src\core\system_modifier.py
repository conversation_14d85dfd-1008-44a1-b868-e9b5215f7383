#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统标识修改模块
负责修改MAC地址、计算机名、注册表等系统标识
"""

import os
import random
import string
import subprocess
import winreg
import uuid
from typing import Dict, List, Optional

class SystemModifier:
    def __init__(self, instance_id: str):
        self.instance_id = instance_id
        self.original_values = {}
        self.modified_values = {}
        
    def generate_random_mac(self) -> str:
        """生成随机MAC地址"""
        # 生成本地管理的MAC地址（第二位设为2、6、A、E）
        mac = [0x02, 0x00, 0x00,
               random.randint(0x00, 0x7f),
               random.randint(0x00, 0xff),
               random.randint(0x00, 0xff)]
        return ':'.join(map(lambda x: "%02x" % x, mac))
    
    def generate_random_computer_name(self) -> str:
        """生成随机计算机名"""
        prefixes = ['WIN', 'PC', 'DESKTOP', 'LAPTOP', 'WORK']
        prefix = random.choice(prefixes)
        suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        return f"{prefix}-{suffix}"
    
    def generate_random_guid(self) -> str:
        """生成随机GUID"""
        return str(uuid.uuid4()).upper()
    
    def modify_mac_address(self):
        """修改MAC地址"""
        try:
            # 获取网络适配器列表
            result = subprocess.run(
                ['wmic', 'path', 'win32_networkadapter', 'get', 'name,netconnectionid'],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                # 这里需要管理员权限来修改MAC地址
                # 实际实现中可能需要使用第三方工具或驱动
                new_mac = self.generate_random_mac()
                self.modified_values['mac_address'] = new_mac
                print(f"实例 {self.instance_id} 设置MAC地址: {new_mac}")
                
        except Exception as e:
            print(f"修改MAC地址失败: {e}")
    
    def modify_computer_name(self):
        """修改计算机名"""
        try:
            new_name = self.generate_random_computer_name()
            
            # 修改注册表中的计算机名
            key_path = r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_SET_VALUE) as key:
                # 备份原值
                try:
                    original_name, _ = winreg.QueryValueEx(key, "ComputerName")
                    self.original_values['computer_name'] = original_name
                except:
                    pass
                
                # 设置新值（注意：这需要管理员权限）
                # winreg.SetValueEx(key, "ComputerName", 0, winreg.REG_SZ, new_name)
                
            self.modified_values['computer_name'] = new_name
            print(f"实例 {self.instance_id} 设置计算机名: {new_name}")
            
        except Exception as e:
            print(f"修改计算机名失败: {e}")
    
    def modify_machine_guid(self):
        """修改机器GUID"""
        try:
            new_guid = self.generate_random_guid()
            
            # 修改注册表中的机器GUID
            key_path = r"SOFTWARE\Microsoft\Cryptography"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_SET_VALUE) as key:
                # 备份原值
                try:
                    original_guid, _ = winreg.QueryValueEx(key, "MachineGuid")
                    self.original_values['machine_guid'] = original_guid
                except:
                    pass
                
                # 设置新值（注意：这需要管理员权限）
                # winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
                
            self.modified_values['machine_guid'] = new_guid
            print(f"实例 {self.instance_id} 设置机器GUID: {new_guid}")
            
        except Exception as e:
            print(f"修改机器GUID失败: {e}")
    
    def modify_hardware_profile(self):
        """修改硬件配置文件"""
        try:
            # 修改硬件配置文件ID
            new_profile_guid = self.generate_random_guid()
            
            key_path = r"SYSTEM\CurrentControlSet\Hardware Profiles\0001"
            # 这里需要修改硬件配置文件的相关注册表项
            
            self.modified_values['hardware_profile'] = new_profile_guid
            print(f"实例 {self.instance_id} 设置硬件配置文件: {new_profile_guid}")
            
        except Exception as e:
            print(f"修改硬件配置文件失败: {e}")
    
    def modify_system_info(self):
        """修改系统信息"""
        try:
            # 修改系统相关的注册表项
            modifications = {
                'ProductId': f"00000-{random.randint(10000, 99999)}-{random.randint(10000, 99999)}-{random.randint(10000, 99999)}",
                'InstallDate': str(random.randint(1000000000, 1700000000)),
                'RegisteredOwner': f"User{random.randint(1000, 9999)}",
                'RegisteredOrganization': f"Org{random.randint(100, 999)}"
            }
            
            for key_name, value in modifications.items():
                self.modified_values[key_name.lower()] = value
                print(f"实例 {self.instance_id} 设置{key_name}: {value}")
                
        except Exception as e:
            print(f"修改系统信息失败: {e}")
    
    def apply_modifications(self):
        """应用所有修改"""
        try:
            print(f"开始为实例 {self.instance_id} 应用系统标识修改...")

            # 检查是否有管理员权限
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()

            if not is_admin:
                print(f"警告：当前没有管理员权限，部分系统标识修改可能失败")

            # 应用修改（即使没有管理员权限也尝试其他修改）
            self.modify_mac_address()

            if is_admin:
                self.modify_computer_name()
                self.modify_machine_guid()
                self.modify_hardware_profile()
            else:
                print(f"跳过需要管理员权限的修改（计算机名、机器GUID、硬件配置文件）")

            self.modify_system_info()

            print(f"实例 {self.instance_id} 系统标识修改完成")

        except Exception as e:
            print(f"应用系统标识修改失败: {e}")
    
    def restore_modifications(self):
        """恢复原始值"""
        try:
            print(f"开始恢复实例 {self.instance_id} 的系统标识...")
            
            # 恢复注册表修改
            for key, original_value in self.original_values.items():
                # 这里实现恢复逻辑
                print(f"恢复 {key}: {original_value}")
            
            print(f"实例 {self.instance_id} 系统标识恢复完成")
            
        except Exception as e:
            print(f"恢复系统标识失败: {e}")
    
    def get_current_modifications(self) -> Dict:
        """获取当前修改的值"""
        return self.modified_values.copy()
    
    def create_environment_variables(self) -> Dict[str, str]:
        """创建环境变量"""
        env_vars = {}
        
        # 设置自定义环境变量
        if 'computer_name' in self.modified_values:
            env_vars['COMPUTERNAME'] = self.modified_values['computer_name']
            env_vars['USERDOMAIN'] = self.modified_values['computer_name']
        
        if 'machine_guid' in self.modified_values:
            env_vars['MACHINE_GUID'] = self.modified_values['machine_guid']
        
        # 添加实例特定的环境变量
        env_vars['INSTANCE_ID'] = self.instance_id
        env_vars['SANDBOX_MODE'] = '1'
        
        return env_vars
