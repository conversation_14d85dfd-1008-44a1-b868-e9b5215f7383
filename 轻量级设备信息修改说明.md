# 安全轻量级设备信息修改说明

## 概述

本项目新增了安全轻量级设备信息修改功能，专门针对需要修改 `GetComputerNameEx` 函数并让系统UI立即显示变化的场景。基于深入研究，我们发现只需要修改 `ActiveComputerName` 就能让系统UI立即生效，因此这个版本只修改最核心的2个注册表项，确保系统最大稳定性。

## 功能对比

### 完整修改模式（原有功能）
- ✅ 修改所有计算机名相关注册表项
- ✅ 修改硬件信息（BIOS、处理器等）
- ✅ 修改所有环境变量
- ✅ 创建完整的伪造系统文件
- ⚠️ 对系统影响较大，可能导致系统不稳定
- ⚠️ 修改项目多，恢复时间长

### 安全轻量级修改模式（推荐使用）
- ✅ 只修改2个核心注册表项
- ✅ 系统UI立即显示新计算机名
- ✅ 不修改网络相关设置
- ✅ 不影响系统服务和网络连接
- ✅ 对系统稳定性影响最小
- ✅ 足以应对大部分软件检测
- ✅ 基于ActiveComputerName的立即生效机制

## GetComputerNameEx 函数说明

`GetComputerNameEx` 是 Windows API 中用于获取计算机名的函数，支持多种名称格式：

### 支持的名称类型
```
0 - ComputerNameNetBIOS          : NetBIOS 名称
1 - ComputerNameDnsHostname      : DNS 主机名
2 - ComputerNameDnsDomain        : DNS 域名
3 - ComputerNameDnsFullyQualified: DNS 完全限定名
4 - ComputerNamePhysicalNetBIOS  : 物理 NetBIOS 名称
5 - ComputerNamePhysicalDnsHostname: 物理 DNS 主机名
6 - ComputerNamePhysicalDnsDomain: 物理 DNS 域名
7 - ComputerNamePhysicalDnsFullyQualified: 物理 DNS 完全限定名
```

### 数据来源
`GetComputerNameEx` 主要从以下注册表位置读取数据：
- `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName`
- `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName`

## 安全轻量级修改的具体实现

### 修改的注册表项（仅2个核心项）
```
1. HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName
   - 值名: ComputerName
   - 作用: GetComputerNameEx 的主要数据源

2. HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName
   - 值名: ComputerName
   - 作用: 活动计算机名，修改后UI立即生效（关键！）
```

### 为什么只修改这2个项目？
基于深入研究发现：
- **ComputerName**: GetComputerNameEx函数的主要数据源
- **ActiveComputerName**: 系统UI立即显示的关键，修改后无需重启即可在设置中看到变化
- **不修改网络相关项**: 避免影响TCP/IP、工作站服务、NetBIOS等，确保网络连接稳定
- **不修改系统服务项**: 避免影响文件共享、网络邻居等功能

### 修改的环境变量（仅1个）
```
COMPUTERNAME: 设置为新的计算机名
```

### 安全的立即生效机制
```
1. 发送WM_SETTINGCHANGE消息通知系统环境变量更改
2. 更新当前进程环境变量确保子进程继承新值
3. ActiveComputerName的修改会让系统UI立即显示新名称
```

### 不执行的操作（确保安全）
```
❌ 不刷新DNS缓存 - 避免影响网络解析
❌ 不重启系统服务 - 避免影响网络连接
❌ 不修改TCP/IP参数 - 避免影响网络配置
❌ 不修改工作站服务 - 避免影响文件共享
❌ 不修改NetBIOS设置 - 避免影响网络邻居
```

### 创建的临时文件
```
临时目录: %TEMP%\qingtalk_light_XXXXXX\
文件: computername (包含新的计算机名)
环境变量: QINGTALK_COMPUTER_NAME, TEMP_COMPUTER_FILE
```

## 使用方法

### 通过GUI界面
1. 以管理员身份运行程序
2. 点击工具栏中的"轻量修改"按钮
3. 确认修改对话框
4. 等待修改完成
5. 启动需要的应用程序
6. 使用完毕后点击"恢复信息"按钮

### 通过代码调用
```python
from src.utils.device_modifier import DeviceModifier

# 创建修改器
modifier = DeviceModifier()

# 执行轻量级修改
success = modifier.modify_computer_name_only()

if success:
    print("轻量级修改成功")
    # 使用应用程序...
    
    # 恢复原始信息
    modifier.restore_device_info()
```

### 通过测试脚本
```bash
# 以管理员身份运行
python test_computer_name_only.py
```

## 安全性分析

### 轻量级修改的优势
1. **系统稳定性**: 只修改计算机名相关项，不涉及硬件信息
2. **影响范围小**: 不修改BIOS、处理器等敏感信息
3. **恢复简单**: 修改项目少，恢复过程快速可靠
4. **兼容性好**: 不会影响系统核心功能

### 风险评估
- **低风险**: 计算机名修改是Windows支持的标准操作
- **可逆性**: 所有修改都有完整备份，可以完全恢复
- **隔离性**: 修改只影响当前进程和子进程

## 检测效果

### 能够绕过的检测
- 基于 `GetComputerNameEx` 的设备指纹检测
- 基于环境变量 `COMPUTERNAME` 的检测
- 基于 `platform.node()` 的Python检测
- 基于 `hostname` 命令的检测

### 无法绕过的检测
- 基于硬件序列号的检测（需要完整修改模式）
- 基于MAC地址的检测
- 基于CPU ID的检测（需要完整修改模式）

## 使用建议

### 推荐场景
1. **日常使用**: 对系统稳定性要求高的场景
2. **测试环境**: 需要快速切换设备身份的测试
3. **轻度伪装**: 只需要改变计算机名的应用

### 不推荐场景
1. **深度检测**: 需要完全伪装硬件信息的场景
2. **高安全要求**: 需要绕过硬件级检测的场景

### 最佳实践
1. 优先使用轻量级修改模式
2. 只有在轻量级模式无效时才使用完整修改模式
3. 使用完毕后及时恢复原始信息
4. 定期备份重要数据

## 故障排除

### 常见问题
1. **权限不足**: 确保以管理员身份运行
2. **修改失败**: 检查注册表权限和防病毒软件
3. **恢复失败**: 重启系统通常可以解决

### 紧急恢复
如果自动恢复失败，可以手动恢复：
1. 打开注册表编辑器 (regedit)
2. 导航到计算机名注册表项
3. 恢复备份的原始值
4. 重启系统

## 技术细节

### 实现原理
轻量级修改通过最小化的注册表修改来影响 `GetComputerNameEx` 函数的返回值，同时保持系统其他部分不变。

### 备份机制
所有修改都有完整的备份：
- 注册表原始值备份到内存和文件
- 环境变量原始值备份
- 临时文件自动清理

### 生效机制
- 注册表修改立即生效
- 环境变量修改影响当前进程
- 新启动的进程会读取修改后的值

## 总结

轻量级设备信息修改是一个安全、可靠的解决方案，专门针对只需要修改计算机名的场景。它在保证功能性的同时，最大程度地降低了对系统稳定性的影响，是日常使用的推荐选择。
