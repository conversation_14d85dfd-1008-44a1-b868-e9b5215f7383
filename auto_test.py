#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动测试
不需要用户交互的测试
"""

import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_subprocess():
    """直接测试subprocess启动"""
    print("=" * 50)
    print("直接subprocess测试")
    print("=" * 50)
    
    test_exe = r"C:\Windows\System32\notepad.exe"
    
    if not os.path.exists(test_exe):
        print(f"❌ 测试程序不存在: {test_exe}")
        return False
    
    try:
        print(f"📝 启动程序: {test_exe}")
        
        # 直接启动
        process = subprocess.Popen(
            test_exe,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ 进程启动成功 (PID: {process.pid})")
        
        # 检查进程状态
        time.sleep(3)
        if process.poll() is None:
            print("✅ 进程正在运行")
            
            # 自动关闭进程
            process.terminate()
            process.wait(timeout=5)
            print("✅ 进程已关闭")
            return True
        else:
            print("❌ 进程启动后立即退出")
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_instance_start_method():
    """测试实例的start方法"""
    print("\n" + "=" * 50)
    print("实例start方法测试")
    print("=" * 50)
    
    try:
        from src.core.instance_manager import Instance
        
        test_exe = r"C:\Windows\System32\notepad.exe"
        
        if not os.path.exists(test_exe):
            print(f"❌ 测试程序不存在: {test_exe}")
            return False
        
        print(f"📝 创建实例对象...")
        instance = Instance("test-instance", test_exe)
        print(f"✅ 实例对象创建成功")
        
        print(f"⏳ 调用start方法...")
        success = instance.start()
        
        if success:
            print(f"✅ start方法返回成功")
            print(f"   PID: {instance.pid}")
            print(f"   状态: {instance.status}")
            
            # 检查进程状态
            time.sleep(3)
            is_running = instance.is_running()
            print(f"   运行状态: {'运行中' if is_running else '已停止'}")
            
            if is_running:
                print("🎉 实例确实在运行！")
                
                # 自动停止实例
                stop_success = instance.stop()
                if stop_success:
                    print("✅ 实例停止成功")
                else:
                    print("❌ 实例停止失败")
                
                return True
            else:
                print("❌ 实例启动后立即停止")
                return False
        else:
            print("❌ start方法返回失败")
            return False
            
    except Exception as e:
        print(f"❌ 实例测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_direct():
    """测试直接多开"""
    print("\n" + "=" * 50)
    print("直接多开测试")
    print("=" * 50)
    
    test_exe = r"C:\Windows\System32\notepad.exe"
    
    if not os.path.exists(test_exe):
        print(f"❌ 测试程序不存在: {test_exe}")
        return False
    
    try:
        processes = []
        
        # 启动多个进程
        for i in range(3):
            print(f"📝 启动进程 {i+1}...")
            
            # 创建临时目录
            temp_dir = f"temp_{i}"
            os.makedirs(temp_dir, exist_ok=True)
            
            # 设置环境变量
            env = os.environ.copy()
            env['INSTANCE_ID'] = f"instance_{i}"
            env['TEMP'] = os.path.abspath(temp_dir)
            
            process = subprocess.Popen(
                test_exe,
                env=env,
                cwd=temp_dir,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            processes.append(process)
            print(f"✅ 进程 {i+1} 启动成功 (PID: {process.pid})")
            time.sleep(1)
        
        # 检查所有进程状态
        time.sleep(3)
        running_count = 0
        for i, process in enumerate(processes):
            if process.poll() is None:
                print(f"✅ 进程 {i+1} 正在运行")
                running_count += 1
            else:
                print(f"❌ 进程 {i+1} 已停止")
        
        print(f"\n📊 运行统计: {running_count}/{len(processes)} 个进程正在运行")
        
        # 关闭所有进程
        print("\n🛑 关闭所有进程...")
        for i, process in enumerate(processes):
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
                print(f"✅ 进程 {i+1} 已关闭")
            except:
                print(f"⚠️ 进程 {i+1} 关闭失败")
        
        # 清理临时目录
        for i in range(3):
            temp_dir = f"temp_{i}"
            try:
                os.rmdir(temp_dir)
            except:
                pass
        
        return running_count > 1
        
    except Exception as e:
        print(f"❌ 多开测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("自动功能测试")
    
    # 运行各项测试
    subprocess_ok = test_direct_subprocess()
    instance_ok = test_instance_start_method()
    multiple_ok = test_multiple_direct()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"直接subprocess: {'✅ 通过' if subprocess_ok else '❌ 失败'}")
    print(f"实例start方法: {'✅ 通过' if instance_ok else '❌ 失败'}")
    print(f"直接多开: {'✅ 通过' if multiple_ok else '❌ 失败'}")
    
    if subprocess_ok and instance_ok and multiple_ok:
        print("\n🎉 所有功能正常！多开应该可以工作。")
    elif subprocess_ok and multiple_ok:
        print("\n⚠️ 基本功能正常，但实例管理器有问题。")
    elif subprocess_ok:
        print("\n⚠️ 基本启动正常，但多开可能受限。")
    else:
        print("\n❌ 基础功能有问题。")
    
    print("\n测试完成。")
