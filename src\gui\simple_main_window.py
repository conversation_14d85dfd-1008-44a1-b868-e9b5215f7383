#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的主窗口界面
按照用户要求简化功能和界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import threading
import time
from datetime import datetime

import os
import sys
def resource_path(relative_path):
    """ Get the absolute path to the resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

class SimpleMainWindow:
    def __init__(self, instance_manager):
        self.instance_manager = instance_manager
        self.root = tk.Tk()
        self.settings_file = "settings.json"

        # 设置默认的QingTalk路径
        self.exe_path = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
        
        # 界面组件
        self.tree = None
        self.status_label = None
        
        # 设置窗口
        self.setup_window()
        
        # 创建界面
        self.create_widgets()
        
        # 加载设置
        self.load_settings()

        # 更新设备信息按钮状态
        self.update_device_info_buttons()

        # 启动定时更新
        self.update_status()
    
    def setup_window(self):
        """设置窗口属性"""
        # 检查管理员权限并设置标题
        import ctypes
        if ctypes.windll.shell32.IsUserAnAdmin():
            self.root.title("QingTalk多开工具")
            self.admin_mode = True
        else:
            self.root.title("QingTalk多开工具")
            self.admin_mode = False

        self.root.geometry("900x500")
        self.root.minsize(800, 400)
        self.root.iconbitmap(resource_path("icon.ico"))

        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建工具栏
        self.create_toolbar()
        
        # 创建实例列表
        self.create_instance_list()
        
        # 创建状态栏
        self.create_statusbar()
    
    def create_toolbar(self):
        """创建简化工具栏"""
        toolbar_frame = ttk.Frame(self.root)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 新建实例按钮
        ttk.Button(
            toolbar_frame,
            text="新建实例",
            command=self.add_instance
        ).pack(side=tk.LEFT, padx=2)

        # 批量新建按钮
        ttk.Button(
            toolbar_frame,
            text="批量新建",
            command=self.batch_add_instances
        ).pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 启动所有按钮
        ttk.Button(
            toolbar_frame, 
            text="启动所有",
            command=self.start_all_instances
        ).pack(side=tk.LEFT, padx=2)
        
        # 停止所有按钮
        ttk.Button(
            toolbar_frame, 
            text="停止所有",
            command=self.stop_all_instances
        ).pack(side=tk.LEFT, padx=2)
        
        # 删除所有按钮
        ttk.Button(
            toolbar_frame,
            text="删除所有",
            command=self.delete_all_instances
        ).pack(side=tk.LEFT, padx=2)

        # 分隔符
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 导入代理IP按钮
        ttk.Button(
            toolbar_frame,
            text="导入代理IP",
            command=self.import_proxy_ips
        ).pack(side=tk.LEFT, padx=2)

        # 修改设备信息按钮
        # self.modify_info_button = ttk.Button(
        #     toolbar_frame,
        #     text="修改信息",
        #     command=self.modify_device_info
        # )
        #self.modify_info_button.pack(side=tk.LEFT, padx=2)

        # 长度安全修改按钮
        self.modify_light_button = ttk.Button(
            toolbar_frame,
            text="修改设备",
            command=self.modify_computer_name_only
        )
        self.modify_light_button.pack(side=tk.LEFT, padx=2)

        # 恢复设备信息按钮（初始隐藏）
        self.restore_info_button = ttk.Button(
            toolbar_frame,
            text="恢复信息",
            command=self.restore_device_info
        )
        # 初始不显示，只有修改后才显示

        # 搜索框
        ttk.Label(toolbar_frame, text="🔍").pack(side=tk.RIGHT, padx=2)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        search_entry = ttk.Entry(toolbar_frame, textvariable=self.search_var, width=15)
        search_entry.pack(side=tk.RIGHT, padx=2)
        ttk.Label(toolbar_frame, text="搜索:").pack(side=tk.RIGHT, padx=2)
    
    def create_instance_list(self):
        """创建实例列表"""
        # 创建框架
        list_frame = ttk.Frame(self.root)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview（添加复选框列）
        columns = ("selected", "name", "remark", "exe_path", "proxy_ip", "status", "pid")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.tree.heading("selected", text="☐", command=self.toggle_all_selection)  # 全选/取消全选
        self.tree.heading("name", text="实例名称")
        self.tree.heading("remark", text="备注")
        self.tree.heading("exe_path", text="程序路径")
        self.tree.heading("proxy_ip", text="代理IP")
        self.tree.heading("status", text="状态")
        self.tree.heading("pid", text="PID")

        # 设置列宽
        self.tree.column("selected", width=40, anchor="center")
        self.tree.column("name", width=120)
        self.tree.column("remark", width=100)
        self.tree.column("exe_path", width=250)
        self.tree.column("proxy_ip", width=120)
        self.tree.column("status", width=80)
        self.tree.column("pid", width=80)

        # 初始化选择状态字典
        self.selected_instances = {}
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定右键菜单
        self.tree.bind("<Button-3>", self.show_context_menu)
        
        # 绑定双击事件 - 智能处理
        self.tree.bind("<Double-1>", self.on_double_click_smart)

        # 绑定单击事件 - 处理复选框
        self.tree.bind("<Button-1>", self.on_single_click)

    def show_progress(self, show=True):
        """显示或隐藏进度条"""
        if show:
            self.progress_bar.pack(side=tk.LEFT, padx=10)
            self.progress_bar.start(10)  # 每10ms更新一次
        else:
            self.progress_bar.stop()
            self.progress_bar.pack_forget()
    
    def create_statusbar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)

        # 进度条（默认隐藏）
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')

        # 显示实例统计
        self.stats_label = ttk.Label(status_frame, text="")
        self.stats_label.pack(side=tk.RIGHT, padx=5, pady=2)
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return
        
        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="启动", command=lambda: self.start_instance(item))
        context_menu.add_command(label="停止", command=lambda: self.stop_instance(item))
        context_menu.add_separator()
        context_menu.add_command(label="编辑备注", command=lambda: self.edit_remark(item))
        context_menu.add_command(label="编辑代理IP", command=lambda: self.edit_proxy_ip_from_menu(item))
        context_menu.add_separator()
        context_menu.add_command(label="删除", command=lambda: self.delete_instance(item))
        
        # 显示菜单
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def edit_proxy_ip(self, event):
        """双击编辑代理IP"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return
        
        # 获取当前代理IP
        current_proxy = self.tree.item(item, "values")[2]
        
        # 创建编辑对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑代理IP")
        dialog.geometry("300x120")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))
        
        # 创建输入框
        ttk.Label(dialog, text="代理IP:").pack(pady=5)
        entry = ttk.Entry(dialog, width=30)
        entry.pack(pady=5)
        entry.insert(0, current_proxy)
        entry.focus()
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def save_proxy():
            new_proxy = entry.get().strip()
            instance_id = item
            
            # 更新实例的代理IP
            instance = self.instance_manager.get_instance(instance_id)
            if instance:
                instance.proxy_ip = new_proxy
                self.update_instance_list()
                self.save_settings()
            
            dialog.destroy()
        
        def cancel():
            dialog.destroy()
        
        ttk.Button(button_frame, text="保存", command=save_proxy).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        entry.bind("<Return>", lambda e: save_proxy())
        dialog.bind("<Escape>", lambda e: cancel())

    def on_double_click_smart(self, event):
        """智能双击处理 - 区分备注列和其他列"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return

        # 获取点击的列
        region = self.tree.identify("region", event.x, event.y)
        if region != "cell":
            return

        column = self.tree.identify("column", event.x, event.y)

        # 列索引：#1=复选框, #2=名称, #3=备注, #4=程序路径, #5=代理IP, #6=状态, #7=PID
        if column == "#1":  # 复选框列 - 单击处理，双击不处理
            return
        elif column == "#3":  # 备注列
            self.edit_remark(item)
        else:  # 其他列 - 激活QingTalk窗口或显示信息
            self.activate_or_show_instance(item)

    def on_single_click(self, event):
        """处理单击事件 - 主要用于复选框"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return

        # 获取点击的列
        region = self.tree.identify("region", event.x, event.y)
        if region != "cell":
            return

        column = self.tree.identify("column", event.x, event.y)

        # 如果点击的是复选框列
        if column == "#1":
            self.toggle_instance_selection(item)

    def toggle_instance_selection(self, item_id):
        """切换实例选择状态"""
        current_state = self.selected_instances.get(item_id, False)
        new_state = not current_state
        self.selected_instances[item_id] = new_state

        # 更新显示
        self.update_instance_checkbox_display(item_id, new_state)

        # 更新状态栏
        selected_count = sum(1 for selected in self.selected_instances.values() if selected)
        total_count = len(self.instance_manager.instances)
        self.status_label.config(text=f"已选择 {selected_count}/{total_count} 个实例")

    def update_instance_checkbox_display(self, item_id, selected):
        """更新实例复选框显示"""
        try:
            # 获取当前行的所有值
            values = list(self.tree.item(item_id, "values"))
            if values:
                # 更新复选框状态（第一列）
                values[0] = "☑" if selected else "☐"
                self.tree.item(item_id, values=values)
        except Exception as e:
            print(f"更新复选框显示失败: {e}")

    def toggle_all_selection(self):
        """全选/取消全选"""
        # 检查当前是否有选中的实例
        selected_count = sum(1 for selected in self.selected_instances.values() if selected)
        total_count = len(self.selected_instances)

        # 如果全部选中，则取消全选；否则全选
        new_state = selected_count < total_count

        for item_id in self.selected_instances.keys():
            self.selected_instances[item_id] = new_state
            self.update_instance_checkbox_display(item_id, new_state)

        # 更新表头
        header_text = "☑" if new_state else "☐"
        self.tree.heading("selected", text=header_text)

        # 更新状态栏
        selected_count = sum(1 for selected in self.selected_instances.values() if selected)
        total_count = len(self.instance_manager.instances)
        self.status_label.config(text=f"已选择 {selected_count}/{total_count} 个实例")

    def activate_or_show_instance(self, item_id):
        """双击激活实例窗口（窗口置前）"""
        try:
            instance = self.instance_manager.get_instance(item_id)
            if not instance:
                self.status_label.config(text="实例不存在")
                return

            # 如果实例正在运行，直接激活窗口（窗口置前）
            if instance.status == 'running' and instance.pid:
                print(f"双击激活实例 {item_id[:8]} 的窗口 (PID: {instance.pid})")
                success = self.try_activate_qingtalk_window(instance.pid)

                if success:
                    self.status_label.config(text=f"已激活实例 {instance.name or item_id[:8]} 的窗口")
                else:
                    # 第一次失败，等待后重试
                    self.status_label.config(text=f"正在重试激活实例 {instance.name or item_id[:8]} 的窗口...")

                    def retry_activation():
                        import time
                        print(f"等待3秒后重试激活实例 {item_id[:8]} 的窗口...")
                        time.sleep(1)
                        retry_success = self.try_activate_qingtalk_window(instance.pid)

                        if retry_success:
                            self.root.after(0, lambda: self.status_label.config(
                                text=f"重试成功，已激活实例 {instance.name or item_id[:8]} 的窗口"))
                        else:
                            self.root.after(0, lambda: self.status_label.config(
                                text=f"实例 {instance.name or item_id[:8]} 可能在系统托盘中，请查看任务栏右下角"))

                    # 在后台线程中重试
                    import threading
                    threading.Thread(target=retry_activation, daemon=True).start()
            else:
                # 如果实例未运行，只在状态栏提示，不弹窗询问
                self.status_label.config(text=f"实例 {instance.name or item_id[:8]} 未运行，请先启动")

        except Exception as e:
            print(f"处理实例激活失败: {e}")
            self.status_label.config(text=f"激活实例失败: {str(e)}")

    def try_activate_qingtalk_window_retry(self, pid):
        """重试激活QingTalk窗口"""
        success = self.try_activate_qingtalk_window(pid)
        if not success:
            print(f"重试后仍未找到窗口，QingTalk可能需要更多时间启动")

    def find_toplevel_windows(self, pid):
        """找到指定进程的所有顶级窗口"""
        import win32gui
        import win32process

        toplevel_windows = []

        def enum_windows_callback(hwnd, data):
            try:
                # 获取窗口所属进程ID
                _, window_pid = win32process.GetWindowThreadProcessId(hwnd)

                if window_pid == pid:
                    # 检查是否是顶级窗口（没有父窗口或父窗口是桌面）
                    parent = win32gui.GetParent(hwnd)
                    if parent == 0:  # 没有父窗口，是顶级窗口
                        window_text = win32gui.GetWindowText(hwnd)
                        class_name = win32gui.GetClassName(hwnd)
                        is_visible = win32gui.IsWindowVisible(hwnd)

                        # 获取窗口大小
                        try:
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]
                        except:
                            width = height = 0

                        # 只关注有意义的窗口（有标题或足够大）
                        if window_text or (width > 100 and height > 100):
                            window_info = {
                                'hwnd': hwnd,
                                'pid': window_pid,
                                'title': window_text,
                                'class': class_name,
                                'visible': is_visible,
                                'width': width,
                                'height': height
                            }
                            toplevel_windows.append(window_info)

            except Exception:
                pass
            return True

        win32gui.EnumWindows(enum_windows_callback, None)
        return toplevel_windows

    def try_activate_qingtalk_window(self, pid):
        """尝试激活QingTalk窗口（顶级窗口版本）"""
        try:
            import win32gui
            import win32con
            import win32process
            import psutil
            import time

            print(f"查找PID {pid} 的顶级窗口...")

            # 首先检查进程是否还存在
            try:
                process = psutil.Process(pid)
                if not process.is_running():
                    print(f"进程 {pid} 已停止")
                    return False

                # 获取进程信息
                process_name = process.name()
                print(f"进程信息: {process_name} (PID: {pid})")

                # 获取所有子进程
                children = process.children(recursive=True)
                all_pids = [pid] + [child.pid for child in children]
                print(f"搜索进程组: {all_pids}")

            except ImportError:
                print("psutil未安装，只搜索主进程")
                all_pids = [pid]
            except psutil.NoSuchProcess:
                print(f"进程 {pid} 不存在")
                return False
            except Exception as e:
                print(f"⚠️ 获取进程信息失败: {e}")
                all_pids = [pid]

            # 查找所有QingTalk进程的顶级窗口
            all_toplevel_windows = []

            for process_pid in all_pids:
                toplevel_windows = self.find_toplevel_windows(process_pid)
                if toplevel_windows:
                    print(f"PID {process_pid} 的顶级窗口:")
                    for window in toplevel_windows:
                        print(f"  '{window['title']}' [{window['class']}] "
                              f"{window['width']}x{window['height']} "
                              f"可见:{'✅' if window['visible'] else '❌'}")
                    all_toplevel_windows.extend(toplevel_windows)

            if not all_toplevel_windows:
                print(f"❌ 没有找到QingTalk顶级窗口")
                return False

            print(f"🎯 选择最佳顶级窗口:")

            # 选择策略：优先选择合理大小的窗口
            best_window = None
            selection_reason = ""

            # 策略1: 优先选择标题为"QingTalk"的合理大小窗口
            qingtalk_titled_windows = [w for w in all_toplevel_windows if w['title'] == 'QingTalk']
            if qingtalk_titled_windows:
                # 过滤出合理大小的QingTalk窗口（避免超大窗口）
                reasonable_qingtalk = [w for w in qingtalk_titled_windows
                                     if w['width'] <= 1000 and w['height'] <= 800]

                if reasonable_qingtalk:
                    # 在合理大小的窗口中选择
                    visible_reasonable = [w for w in reasonable_qingtalk if w['visible']]
                    if visible_reasonable:
                        best_window = max(visible_reasonable, key=lambda w: w['width'] * w['height'])
                        selection_reason = "可见的合理大小QingTalk窗口"
                    else:
                        best_window = max(reasonable_qingtalk, key=lambda w: w['width'] * w['height'])
                        selection_reason = "合理大小QingTalk窗口（需恢复）"
                else:
                    # 如果没有合理大小的，记录但不选择超大窗口
                    oversized = [w for w in qingtalk_titled_windows
                               if w['width'] > 1000 or w['height'] > 800]
                    if oversized:
                        print(f"  ⚠️ 发现超大QingTalk窗口，跳过: {oversized[0]['width']}x{oversized[0]['height']}")
                    best_window = None

            # 策略2: 如果没有合理大小的QingTalk窗口，选择其他合理大小的窗口
            if not best_window:
                reasonable_windows = [w for w in all_toplevel_windows
                                    if w['width'] <= 1000 and w['height'] <= 800
                                    and w['width'] > 200 and w['height'] > 200]

                if reasonable_windows:
                    visible_reasonable = [w for w in reasonable_windows if w['visible']]
                    if visible_reasonable:
                        best_window = max(visible_reasonable, key=lambda w: w['width'] * w['height'])
                        selection_reason = "最大的可见合理窗口"
                    else:
                        best_window = max(reasonable_windows, key=lambda w: w['width'] * w['height'])
                        selection_reason = "最大的合理窗口（需恢复）"

            if not best_window:
                print("❌ 无法找到合适大小的顶级窗口")
                return False

            print(f"✅ 选择窗口:")
            print(f"  PID: {best_window['pid']}")
            print(f"  标题: '{best_window['title']}'")
            print(f"  类名: {best_window['class']}")
            print(f"  大小: {best_window['width']}x{best_window['height']}")
            print(f"  可见: {'✅' if best_window['visible'] else '❌'}")
            print(f"  选择理由: {selection_reason}")

            # 激活顶级窗口
            print(f"🚀 激活顶级窗口...")
            try:
                hwnd = best_window['hwnd']

                # 如果窗口不可见或最小化，先恢复
                if not best_window['visible'] or win32gui.IsIconic(hwnd):
                    print("🔄 恢复窗口...")
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    time.sleep(0.5)

                # 激活窗口
                print("⚡ 激活窗口...")

                # 尝试多种激活方法
                try:
                    # 方法1: 直接设置前台窗口
                    win32gui.SetForegroundWindow(hwnd)
                except:
                    try:
                        # 方法2: 先显示再激活
                        win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                        win32gui.SetForegroundWindow(hwnd)
                    except:
                        # 方法3: 使用BringWindowToTop
                        win32gui.BringWindowToTop(hwnd)
                        win32gui.SetForegroundWindow(hwnd)

                # 验证激活结果
                time.sleep(1)
                current_foreground = win32gui.GetForegroundWindow()

                if current_foreground == hwnd:
                    print("✅ 顶级窗口激活成功！")

                    # 检查激活后状态
                    try:
                        rect = win32gui.GetWindowRect(hwnd)
                        current_width = rect[2] - rect[0]
                        current_height = rect[3] - rect[1]
                        is_visible = win32gui.IsWindowVisible(hwnd)

                        print(f"📊 激活后状态:")
                        print(f"  大小: {current_width}x{current_height}")
                        print(f"  可见: {'✅' if is_visible else '❌'}")
                        print(f"  标题: '{win32gui.GetWindowText(hwnd)}'")

                        print(f"🎉 成功激活QingTalk主窗口！")

                    except Exception as e:
                        print(f"⚠️ 无法获取激活后状态: {e}")

                    return True
                else:
                    print("❌ 顶级窗口激活失败")
                    return False

            except Exception as e:
                print(f"❌ 激活顶级窗口时出错: {e}")
                return False

        except ImportError:
            print("安装 pywin32 可以自动激活窗口: pip install pywin32")
            return False
        except Exception as e:
            print(f"查找顶级窗口时出错: {e}")
            return False


    def edit_remark_on_double_click(self, event):
        """双击编辑备注（保留兼容性）"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return

        self.edit_remark(item)

    def edit_remark(self, item_id):
        """编辑实例备注"""
        instance = self.instance_manager.get_instance(item_id)
        if not instance:
            return

        # 创建简化的备注编辑对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("修改备注")
        dialog.geometry("350x150")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # 实例信息显示
        info_frame = ttk.Frame(dialog)
        info_frame.pack(pady=10, padx=20, fill=tk.X)

        ttk.Label(info_frame, text=f"实例: {instance.name or f'QingTalk_{item_id[:8]}'}").pack(anchor=tk.W)

        # 备注输入
        ttk.Label(dialog, text="备注:").pack(pady=(10, 5))
        remark_var = tk.StringVar(value=instance.remark or "")
        remark_entry = ttk.Entry(dialog, textvariable=remark_var, width=40)
        remark_entry.pack(pady=5, padx=20)

        def save_remark():
            instance.remark = remark_var.get()
            self.update_tree_item(item_id, instance)
            dialog.destroy()

        def cancel_edit():
            dialog.destroy()

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=15)

        ttk.Button(button_frame, text="保存", command=save_remark).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=cancel_edit).pack(side=tk.LEFT, padx=10)

        # 焦点设置和快捷键
        remark_entry.focus_set()
        remark_entry.select_range(0, tk.END)

        # 回车保存，ESC取消
        dialog.bind('<Return>', lambda e: save_remark())
        dialog.bind('<Escape>', lambda e: cancel_edit())

    def batch_add_instances(self):
        """批量添加实例"""
        dialog = tk.Toplevel(self.root)
        dialog.title("批量新建实例")
        dialog.geometry("500x450")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="批量新建实例", font=("", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 选择exe文件
        exe_frame = ttk.LabelFrame(main_frame, text="选择程序", padding="10")
        exe_frame.pack(fill=tk.X, pady=(0, 15))

        exe_path_var = tk.StringVar(value=self.exe_path)
        ttk.Label(exe_frame, text="程序路径:").pack(anchor=tk.W)

        path_frame = ttk.Frame(exe_frame)
        path_frame.pack(fill=tk.X, pady=(5, 0))

        path_entry = ttk.Entry(path_frame, textvariable=exe_path_var, state="readonly")
        path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        def browse_exe():
            exe_path = filedialog.askopenfilename(
                title="选择要多开的exe文件",
                filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
            )
            if exe_path:
                exe_path_var.set(exe_path)

        ttk.Button(path_frame, text="浏览", command=browse_exe).pack(side=tk.RIGHT, padx=(5, 0))

        # 创建设置
        settings_frame = ttk.LabelFrame(main_frame, text="创建设置", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 15))

        # 实例数量
        count_frame = ttk.Frame(settings_frame)
        count_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(count_frame, text="创建数量:").pack(side=tk.LEFT)
        count_var = tk.StringVar(value="3")

        # 动态计算最大创建数量
        max_count = self.calculate_max_instances()
        count_spinbox = ttk.Spinbox(count_frame, from_=1, to=max_count, width=10, textvariable=count_var)
        count_spinbox.pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(count_frame, text=f"个实例 (最大{max_count}个)").pack(side=tk.LEFT, padx=(5, 0))

        # 系统资源提示
        resource_info = self.get_system_resource_info()
        resource_label = ttk.Label(count_frame, text=resource_info, foreground="gray")
        resource_label.pack(side=tk.LEFT, padx=(10, 0))

        # 实例前缀
        prefix_frame = ttk.Frame(settings_frame)
        prefix_frame.pack(fill=tk.X)

        ttk.Label(prefix_frame, text="名称前缀:").pack(side=tk.LEFT)
        prefix_var = tk.StringVar(value="实例")
        ttk.Entry(prefix_frame, textvariable=prefix_var, width=15).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(prefix_frame, text="(将自动添加序号)").pack(side=tk.LEFT, padx=(5, 0))

        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="创建进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
        progress_bar.pack(fill=tk.X, pady=(0, 5))

        status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(progress_frame, textvariable=status_var)
        status_label.pack(anchor=tk.W)

        def create_instances():
            exe_path = exe_path_var.get()
            if not exe_path:
                messagebox.showerror("错误", "请先选择exe文件")
                return

            try:
                count = int(count_var.get())
                max_count = self.calculate_max_instances()

                if count < 1:
                    messagebox.showerror("错误", "创建数量必须大于0")
                    return
                elif count > max_count:
                    messagebox.showerror("错误", f"创建数量不能超过{max_count}个\n\n原因：防止太卡，可以再次创建")
                    return

                # 检查系统资源
                if not self.check_system_resources(count):
                    if not messagebox.askyesno("资源警告",
                        "系统资源可能不足，继续创建可能导致性能问题。\n\n是否继续？"):
                        return

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数量")
                return

            prefix = prefix_var.get().strip()
            if not prefix:
                messagebox.showerror("错误", "请输入名称前缀")
                return

            # 禁用创建按钮
            create_button.config(state="disabled")

            # 在后台线程中创建实例
            def create_thread():
                try:
                    created_count = 0
                    for i in range(count):
                        # 更新进度
                        progress = (i / count) * 100
                        dialog.after(0, lambda p=progress: progress_var.set(p))
                        dialog.after(0, lambda i=i: status_var.set(f"正在创建第 {i+1} 个实例..."))

                        # 创建实例
                        instance_id = self.instance_manager.create_instance(exe_path)
                        instance = self.instance_manager.get_instance(instance_id)
                        if instance:
                            # 添加时间戳确保名称唯一（与新建实例格式一致）
                            import time
                            timestamp = str(int(time.time() * 1000))  # 13位毫秒时间戳
                            instance.name = f"{prefix}{i+1:02d}_{timestamp[-6:]}"  # 使用后6位避免太长
                            instance.remark = f"批量创建 {i+1}/{count} - {timestamp}"

                            # 立即创建完整环境
                            dialog.after(0, lambda i=i: status_var.set(f"正在为第 {i+1} 个实例创建环境..."))
                            try:
                                self.create_complete_environment(instance_id, exe_path)
                                created_count += 1
                                dialog.after(0, lambda i=i: status_var.set(f"第 {i+1} 个实例创建成功"))
                            except Exception as env_error:
                                print(f"创建实例 {i+1} 环境失败: {env_error}")
                                dialog.after(0, lambda i=i: status_var.set(f"第 {i+1} 个实例环境创建失败"))

                    # 完成
                    dialog.after(0, lambda: progress_var.set(100))
                    dialog.after(0, lambda: status_var.set(f"成功创建 {count} 个实例"))
                    dialog.after(0, lambda: messagebox.showinfo("成功", f"成功创建 {count} 个实例！"))
                    dialog.after(0, lambda: self.update_instance_list())
                    dialog.after(0, dialog.destroy)

                except Exception as e:
                    dialog.after(0, lambda: messagebox.showerror("错误", f"创建实例失败: {str(e)}"))
                    dialog.after(0, lambda: create_button.config(state="normal"))

            threading.Thread(target=create_thread, daemon=True).start()

        def cancel_create():
            dialog.destroy()

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="取消", command=cancel_create).pack(side=tk.RIGHT, padx=(5, 0))
        create_button = ttk.Button(button_frame, text="开始创建", command=create_instances)
        create_button.pack(side=tk.RIGHT)

        # 焦点和快捷键
        count_spinbox.focus_set()
        dialog.bind('<Escape>', lambda e: cancel_create())

    def create_complete_environment(self, instance_id, exe_path):
        """立即创建完整环境"""
        try:
            # 获取实例
            instance = self.instance_manager.get_instance(instance_id)
            if not instance:
                print(f"实例 {instance_id} 不存在")
                return

            # 检测应用类型并创建完整环境
            app_name = os.path.basename(exe_path).lower()

            if 'qingtalk' in app_name:
                # QingTalk应用，使用完美方案创建环境
                from ..utils.qingtalk_perfect import create_qingtalk_perfect_environment

                # 创建完整的QingTalk环境
                print(f"为实例 {instance_id} 创建QingTalk完整环境...")

                # 调用完美方案创建环境
                isolation_info = create_qingtalk_perfect_environment(
                    instance_id=instance_id,
                    exe_path=exe_path,
                    is_first_time=True
                )

                # 保存隔离信息到实例
                if isolation_info:
                    instance.isolation_info = isolation_info
                    print(f"✅ 实例 {instance_id} 环境创建完成")
                else:
                    print(f"❌ 实例 {instance_id} 环境创建失败")
            else:
                # 其他应用，创建基本的隔离环境
                print(f"为实例 {instance_id} 创建基本隔离环境...")
                # 这里可以添加其他应用的环境创建逻辑

        except Exception as e:
            print(f"创建完整环境失败: {e}")

    def calculate_max_instances(self):
        return 10

        """计算最大可创建实例数量"""
        try:
            import psutil

            # 获取系统资源
            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)

            # 获取当前实例数量
            current_instances = len(self.instance_manager.instances)

            # 估算每个QingTalk实例需要的内存（约500MB）
            memory_per_instance = 0.5  # GB

            # 基于内存计算最大实例数
            max_by_memory = int(available_memory_gb / memory_per_instance)

            # 设置绝对上限（避免系统过载）
            absolute_max = 100

            # 考虑当前已有实例
            max_new_instances = min(max_by_memory, absolute_max - current_instances)

            # 至少允许创建1个，最多20个（单次批量创建限制）
            return max(1, min(max_new_instances, 20))

        except ImportError:
            # 如果没有psutil，使用保守估计
            current_instances = len(self.instance_manager.instances)
            return max(1, min(10, 20 - current_instances))
        except Exception as e:
            print(f"计算最大实例数失败: {e}")
            return 5

    def get_system_resource_info(self):
        """获取系统资源信息"""
        try:
            import psutil

            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)
            total_memory_gb = memory.total / (1024**3)

            cpu_percent = psutil.cpu_percent(interval=0.1)

            return f"内存: {available_memory_gb:.1f}/{total_memory_gb:.1f}GB 可用, CPU: {cpu_percent:.1f}%"

        except ImportError:
            return "系统资源信息不可用"
        except Exception as e:
            return f"资源检测失败: {e}"

    def check_system_resources(self, new_instances_count):
        return True
        """检查系统资源是否足够创建新实例"""
        try:
            import psutil

            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)

            # 估算需要的内存
            memory_needed = new_instances_count * 0.5  # 每个实例约500MB

            # 检查内存是否足够（保留2GB给系统）
            if available_memory_gb - memory_needed < 2.0:
                return False

            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > 80:
                return False

            return True

        except ImportError:
            # 没有psutil，保守估计
            current_instances = len(self.instance_manager.instances)
            return (current_instances + new_instances_count) <= 10
        except Exception as e:
            print(f"资源检查失败: {e}")
            return True  # 检查失败时允许创建

    def edit_proxy_ip_from_menu(self, item_id):
        """从右键菜单编辑代理IP"""
        self.edit_proxy_ip_for_item(item_id)

    def edit_proxy_ip_for_item(self, item_id):
        """编辑指定实例的代理IP"""
        instance = self.instance_manager.get_instance(item_id)
        if not instance:
            return

        # 获取当前代理IP
        current_proxy = instance.proxy_ip

        # 创建编辑对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑代理IP")
        dialog.geometry("350x150")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))

        # 创建输入框
        ttk.Label(dialog, text="代理IP (格式: IP:端口:用户名:密码):").pack(pady=5)
        entry = ttk.Entry(dialog, width=40)
        entry.pack(pady=5)
        entry.insert(0, current_proxy)
        entry.focus()

        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)

        def save_proxy():
            new_proxy = entry.get().strip()

            # 解析代理信息
            if ':' in new_proxy:
                parts = new_proxy.split(':')
                instance.proxy_ip = f"{parts[0]}:{parts[1]}" if len(parts) >= 2 else new_proxy
                instance.proxy_port = parts[1] if len(parts) >= 2 else ''
                instance.proxy_username = parts[2] if len(parts) >= 3 else ''
                instance.proxy_password = parts[3] if len(parts) >= 4 else ''
            else:
                instance.proxy_ip = new_proxy

            self.update_instance_list()
            self.save_settings()
            dialog.destroy()

        def cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_proxy).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=5)

        # 绑定回车键
        entry.bind("<Return>", lambda e: save_proxy())
        dialog.bind("<Escape>", lambda e: cancel())

    def edit_remark(self, item_id):
        """编辑实例备注"""
        instance = self.instance_manager.get_instance(item_id)
        if not instance:
            return

        # 获取当前备注
        current_remark = getattr(instance, 'remark', '')

        # 创建编辑对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑备注")
        dialog.geometry("300x120")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))

        # 创建输入框
        ttk.Label(dialog, text="备注:").pack(pady=5)
        entry = ttk.Entry(dialog, width=30)
        entry.pack(pady=5)
        entry.insert(0, current_remark)
        entry.focus()

        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)

        def save_remark():
            new_remark = entry.get().strip()
            instance.remark = new_remark
            self.update_instance_list()
            self.save_settings()
            dialog.destroy()

        def cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_remark).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=5)

        # 绑定回车键
        entry.bind("<Return>", lambda e: save_remark())
        dialog.bind("<Escape>", lambda e: cancel())

    def import_proxy_ips(self):
        """导入代理IP列表"""
        # 选择txt文件
        file_path = filedialog.askopenfilename(
            title="选择代理IP文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 解析代理IP
            proxy_ips = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):  # 跳过空行和注释
                    proxy_ips.append(line)

            if not proxy_ips:
                messagebox.showwarning("警告", "文件中没有找到有效的代理IP")
                return

            # 获取当前实例列表
            instance_ids = list(self.instance_manager.instances.keys())

            if not instance_ids:
                messagebox.showwarning("警告", "没有实例可以分配代理IP")
                return

            # 按顺序分配代理IP
            assigned_count = 0
            for i, instance_id in enumerate(instance_ids):
                if i < len(proxy_ips):
                    instance = self.instance_manager.get_instance(instance_id)
                    if instance:
                        proxy_line = proxy_ips[i]

                        # 解析代理信息 (IP:端口:用户名:密码)
                        if ':' in proxy_line:
                            parts = proxy_line.split(':')
                            instance.proxy_ip = f"{parts[0]}:{parts[1]}" if len(parts) >= 2 else proxy_line
                            instance.proxy_port = parts[1] if len(parts) >= 2 else ''
                            instance.proxy_username = parts[2] if len(parts) >= 3 else ''
                            instance.proxy_password = parts[3] if len(parts) >= 4 else ''
                        else:
                            instance.proxy_ip = proxy_line

                        assigned_count += 1

            # 更新界面和保存设置
            self.update_instance_list()
            self.save_settings()

            # 显示结果
            total_instances = len(instance_ids)
            total_proxies = len(proxy_ips)

            if assigned_count == total_instances and total_proxies >= total_instances:
                self.status_label.config(text=f"成功为 {assigned_count} 个实例分配代理IP")
            elif assigned_count < total_instances:
                remaining = total_instances - assigned_count
                self.status_label.config(text=f"已分配 {assigned_count} 个，还有 {remaining} 个实例未分配代理")

            messagebox.showinfo("导入完成",
                f"成功导入 {total_proxies} 个代理IP\n"
                f"已为 {assigned_count} 个实例分配代理\n"
                f"实例总数: {total_instances}")

        except Exception as e:
            messagebox.showerror("错误", f"导入代理IP失败: {e}")

    def modify_device_info(self):
        """修改设备信息"""
        # 检查管理员权限
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showwarning("权限不足",
                "修改设备信息需要管理员权限！\n\n"
                "请以管理员身份重新运行程序。")
            return

        # 确认对话框
        if not messagebox.askyesno("确认修改",
            "确定要修改系统设备信息吗？\n\n"
            "这将修改：\n"
            "• 计算机名\n"
            "• 硬件信息\n"
            "• 环境变量\n\n"
            "修改后将在所有实例停止时自动恢复。"):
            return

        try:
            # 导入设备修改模块
            from src.utils.device_modifier import DeviceModifier

            # 创建设备修改器
            device_modifier = DeviceModifier()

            # 显示进度
            self.show_progress(True)
            self.status_label.config(text="正在修改设备信息...")

            # 在后台线程中执行修改
            def modify_thread():
                try:
                    success = device_modifier.modify_device_info()

                    if success:
                        self.root.after(0, lambda: self.status_label.config(text="设备信息修改成功"))
                        self.root.after(0, lambda: self.update_device_info_buttons())
                        self.root.after(0, lambda: messagebox.showinfo("修改成功",
                            "设备信息修改成功！\n\n"
                            "现在启动的QingTalk实例将使用新的设备信息。\n"
                            "设备信息将在程序关闭时自动恢复。"))
                    else:
                        self.root.after(0, lambda: self.status_label.config(text="❌ 设备信息修改失败"))
                        self.root.after(0, lambda: messagebox.showerror("修改失败",
                            "设备信息修改失败！\n\n"
                            "请检查管理员权限或查看控制台错误信息。"))
                except Exception as e:
                    self.root.after(0, lambda: self.status_label.config(text="❌ 设备信息修改出错"))
                    self.root.after(0, lambda: messagebox.showerror("修改出错", f"设备信息修改出错：\n{str(e)}"))
                finally:
                    self.root.after(0, lambda: self.show_progress(False))

            # 启动后台线程
            import threading
            threading.Thread(target=modify_thread, daemon=True).start()

        except ImportError:
            messagebox.showerror("功能不可用",
                "设备修改模块未找到！\n\n"
                "请确保相关文件存在。")
        except Exception as e:
            self.show_progress(False)
            messagebox.showerror("错误", f"修改设备信息失败：\n{str(e)}")

    def modify_computer_name_only(self):
        """轻量级修改（仅计算机名）"""
        # 检查管理员权限
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showwarning("权限不足",
                "修改计算机名需要管理员权限！\n\n"
                "请以管理员身份重新运行程序。")
            return

        # 确认对话框
        # if not messagebox.askyesno("确认长度安全修改",
        #     "确定要进行长度安全修改吗？\n\n"
        #     "这将只修改：\n"
        #     "• ComputerName（GetComputerNameEx数据源）\n"
        #     "• ActiveComputerName（UI立即生效的关键）\n"
        #     "• COMPUTERNAME环境变量\n\n"
        #     "🛡️ 长度安全特性：\n"
        #     "• 确保新名称长度与原名称完全一致\n"
        #     "• 避免缓冲区溢出和网络接口错误\n"
        #     "• 系统UI立即显示新计算机名\n"
        #     "• 不影响网络设置，系统稳定\n\n"
        #     "修改后将在所有实例停止时自动恢复。"):
        #     return

        try:
            # 导入设备修改模块
            from src.utils.device_modifier import DeviceModifier

            # 创建设备修改器
            device_modifier = DeviceModifier()

            # 显示进度
            self.show_progress(True)
            self.status_label.config(text="正在进行修改设备...")

            # 在后台线程中执行修改
            def modify_thread():
                try:
                    success = device_modifier.modify_computer_name_only()

                    if success:
                        self.root.after(0, lambda: self.status_label.config(text="修改设备成功"))
                        self.root.after(0, lambda: self.update_device_info_buttons())
                        self.root.after(0, lambda: messagebox.showinfo("修改成功",
                            "设备信息修改成功！\n\n"))
                    else:
                        self.root.after(0, lambda: self.status_label.config(text="修改设备失败"))
                        self.root.after(0, lambda: messagebox.showerror("修改失败",
                            "修改设备失败！\n\n"
                            "请检查管理员权限或查看控制台错误信息。"))
                except Exception as e:
                    self.root.after(0, lambda: self.status_label.config(text="修改设备出错"))
                    self.root.after(0, lambda: messagebox.showerror("修改出错", f"修改设备出错：\n{str(e)}"))
                finally:
                    self.root.after(0, lambda: self.show_progress(False))

            # 启动后台线程
            import threading
            threading.Thread(target=modify_thread, daemon=True).start()

        except ImportError:
            messagebox.showerror("功能不可用",
                "设备修改模块未找到！\n\n"
                "请确保相关文件存在。")
        except Exception as e:
            self.show_progress(False)
            messagebox.showerror("错误", f"修改设备失败：\n{str(e)}")

    def restore_device_info(self):
        """恢复设备信息"""
        # 确认对话框
        if not messagebox.askyesno("确认恢复",
            "确定要恢复原始设备信息吗？\n\n"
            "这将恢复所有被修改的系统信息。"):
            return

        try:
            # 导入设备修改模块
            from src.utils.device_modifier import DeviceModifier

            # 创建设备修改器
            device_modifier = DeviceModifier()

            # 显示进度
            self.show_progress(True)
            self.status_label.config(text="正在恢复设备信息...")

            # 在后台线程中执行恢复
            def restore_thread():
                try:
                    device_modifier.restore_device_info()

                    self.root.after(0, lambda: self.status_label.config(text="✅ 设备信息恢复成功"))
                    self.root.after(0, lambda: self.update_device_info_buttons())
                    self.root.after(0, lambda: messagebox.showinfo("恢复成功",
                        "设备信息恢复成功！\n\n"
                        "系统已恢复到原始状态。"))
                except Exception as e:
                    self.root.after(0, lambda: self.status_label.config(text="❌ 设备信息恢复出错"))
                    self.root.after(0, lambda: messagebox.showerror("恢复出错", f"设备信息恢复出错：\n{str(e)}"))
                finally:
                    self.root.after(0, lambda: self.show_progress(False))

            # 启动后台线程
            import threading
            threading.Thread(target=restore_thread, daemon=True).start()

        except ImportError:
            messagebox.showerror("功能不可用",
                "设备修改模块未找到！\n\n"
                "请确保相关文件存在。")
        except Exception as e:
            self.show_progress(False)
            messagebox.showerror("错误", f"恢复设备信息失败：\n{str(e)}")

    def update_device_info_buttons(self):
        """更新设备信息相关按钮的状态"""
        try:
            from src.utils.device_modifier import device_modifier

            if device_modifier.is_modified():
                # 设备信息已修改，显示恢复按钮，修改按钮显示为已修改状态
                #self.modify_info_button.config(text="信息已修改", state="disabled")
                self.modify_light_button.config(text="已修改", state="disabled")
                self.restore_info_button.pack(side=tk.LEFT, padx=2, after=self.modify_light_button)
            else:
                # 设备信息未修改，隐藏恢复按钮，修改按钮恢复正常
                #self.modify_info_button.config(text="修改信息", state="normal")
                self.modify_light_button.config(text="修改设备", state="normal")
                self.restore_info_button.pack_forget()

        except ImportError:
            # 如果模块不存在，禁用按钮
            #self.modify_info_button.config(text="修改信息", state="disabled")
            self.modify_light_button.config(text="修改设备", state="disabled")
            self.restore_info_button.pack_forget()
        except Exception as e:
            print(f"更新设备信息按钮状态失败: {e}")

    def on_search_changed(self, *args):
        """搜索内容改变时的处理"""
        search_text = self.search_var.get().lower()
        self.filter_instances(search_text)

    def filter_instances(self, search_text):
        """根据搜索文本过滤实例"""
        # 保存当前选中的项目
        selected_items = self.tree.selection()

        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 添加匹配的实例
        for instance_id, instance in self.instance_manager.instances.items():
            # 检查是否匹配搜索条件
            if not search_text or self.instance_matches_search(instance, search_text):
                # 状态中文化
                status_map = {
                    'running': '正在运行',
                    'stopped': '已停止',
                    'starting': '启动中',
                    'stopping': '停止中',
                    'error': '错误'
                }
                status_text = status_map.get(instance.status, instance.status)

                self.tree.insert("", "end", iid=instance_id, values=(
                    instance.name,
                    getattr(instance, 'remark', ''),
                    instance.exe_path,
                    getattr(instance, 'proxy_ip', ''),
                    status_text,
                    instance.pid if instance.pid else ''
                ))

        # 恢复选中状态
        self._restore_selection(selected_items)

    def instance_matches_search(self, instance, search_text):
        """检查实例是否匹配搜索条件"""
        # 搜索实例名称、备注、程序路径、代理IP
        searchable_fields = [
            instance.name.lower(),
            getattr(instance, 'remark', '').lower(),
            os.path.basename(instance.exe_path).lower(),
            getattr(instance, 'proxy_ip', '').lower()
        ]

        return any(search_text in field for field in searchable_fields)
    
    def add_instance(self):
        """添加新实例"""
        # 选择exe文件
        exe_path = filedialog.askopenfilename(
            title="选择要多开的程序",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        
        if exe_path:
            try:
                instance_id = self.instance_manager.create_instance(exe_path)
                self.update_instance_list()
                self.save_settings()
                self.status_label.config(text=f"已添加实例: {os.path.basename(exe_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"添加实例失败: {e}")
    
    def start_instance(self, item_id):
        """启动指定实例（多线程）"""
        def start_thread():
            try:
                self.root.after(0, lambda: self.show_progress(True))
                self.root.after(0, lambda: self.status_label.config(text="正在启动实例..."))

                success = self.instance_manager.start_instance(item_id)

                self.root.after(0, lambda: self.show_progress(False))
                if success:
                    self.root.after(0, lambda: self.status_label.config(text="实例启动成功（保持登录状态）"))
                else:
                    self.root.after(0, lambda: self.status_label.config(text="实例启动失败"))
            except Exception as e:
                self.root.after(0, lambda: self.show_progress(False))
                self.root.after(0, lambda: messagebox.showerror("错误", f"启动实例失败: {e}"))

        # 在后台线程中启动
        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_instance(self, item_id):
        """停止指定实例（多线程）"""
        def stop_thread():
            try:
                self.root.after(0, lambda: self.status_label.config(text="正在停止实例..."))
                success = self.instance_manager.stop_instance(item_id)

                if success:
                    self.root.after(0, lambda: self.status_label.config(text="实例停止成功"))
                else:
                    self.root.after(0, lambda: self.status_label.config(text="实例停止失败"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"停止实例失败: {e}"))

        # 在后台线程中停止
        threading.Thread(target=stop_thread, daemon=True).start()
    
    def delete_instance(self, item_id):
        """删除指定实例"""
        if messagebox.askyesno("确认删除", "确定要删除这个实例吗？这将删除所有相关文件和数据。"):
            try:
                # 先检查实例是否正在运行
                instance = self.instance_manager.get_instance(item_id)
                if instance and instance.status == 'running':
                    print(f"实例正在运行，先停止实例 {item_id[:8]}...")
                    self.instance_manager.stop_instance(item_id)
                    # 等待停止完成
                    import time
                    time.sleep(2)

                # 删除实例文件夹
                import shutil
                instance_folder = os.path.join(os.getcwd(), "persistent_data", f"instance_{item_id[:8]}")
                if os.path.exists(instance_folder):
                    shutil.rmtree(instance_folder, ignore_errors=True)
                    print(f"删除实例文件夹: {instance_folder}")

                # 删除实例
                self.instance_manager.remove_instance(item_id)
                self.update_instance_list()
                self.save_settings()
                self.status_label.config(text="实例和相关文件删除成功")
            except Exception as e:
                messagebox.showerror("错误", f"删除实例失败: {e}")
    
    def start_all_instances(self):
        """启动选中的实例（多线程）"""
        def start_selected_thread():
            try:
                # 获取选中的实例ID
                selected_ids = [instance_id for instance_id, selected in self.selected_instances.items()
                               if selected and instance_id in self.instance_manager.instances]

                # 如果没有选中任何实例，启动所有实例
                if not selected_ids:
                    selected_ids = list(self.instance_manager.instances.keys())
                    action_text = "所有"
                else:
                    action_text = "选中的"

                total = len(selected_ids)

                if total == 0:
                    self.root.after(0, lambda: self.status_label.config(text="没有实例可启动"))
                    return

                self.root.after(0, lambda: self.status_label.config(text=f"正在启动{action_text} {total} 个实例..."))

                count = 0
                for i, instance_id in enumerate(selected_ids):
                    instance = self.instance_manager.get_instance(instance_id)
                    instance_name = instance.name if instance else instance_id[:8]

                    self.root.after(0, lambda i=i, total=total, name=instance_name:
                                   self.status_label.config(text=f"正在启动 {name} ({i+1}/{total})..."))

                    if self.instance_manager.start_instance(instance_id):
                        count += 1

                    # 延迟启动避免冲突
                    if i < total - 1:  # 最后一个不需要延迟
                        time.sleep(2)

                self.root.after(0, lambda: self.status_label.config(text=f"已启动 {count}/{total} 个{action_text}实例"))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"启动实例失败: {e}"))

        # 在后台线程中启动
        threading.Thread(target=start_selected_thread, daemon=True).start()
    
    def stop_all_instances(self):
        """停止选中的实例（多线程）"""
        def stop_selected_thread():
            try:
                # 获取选中的实例ID
                selected_ids = [instance_id for instance_id, selected in self.selected_instances.items()
                               if selected and instance_id in self.instance_manager.instances]

                # 如果没有选中任何实例，停止所有实例
                if not selected_ids:
                    selected_ids = list(self.instance_manager.instances.keys())
                    action_text = "所有"
                else:
                    action_text = "选中的"

                total = len(selected_ids)

                if total == 0:
                    self.root.after(0, lambda: self.status_label.config(text="没有实例可停止"))
                    return

                self.root.after(0, lambda: self.status_label.config(text=f"正在停止{action_text} {total} 个实例..."))

                count = 0
                for i, instance_id in enumerate(selected_ids):
                    instance = self.instance_manager.get_instance(instance_id)
                    instance_name = instance.name if instance else instance_id[:8]

                    self.root.after(0, lambda i=i, total=total, name=instance_name:
                                   self.status_label.config(text=f"正在停止 {name} ({i+1}/{total})..."))

                    if self.instance_manager.stop_instance(instance_id):
                        count += 1

                self.root.after(0, lambda: self.status_label.config(text=f"已停止 {count}/{total} 个{action_text}实例"))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"停止实例失败: {e}"))

        # 在后台线程中停止
        threading.Thread(target=stop_selected_thread, daemon=True).start()
    
    def delete_all_instances(self):
        """删除所有实例"""
        if messagebox.askyesno("确认删除", "确定要删除所有实例吗？这将删除所有相关文件和数据。"):
            try:
                import shutil
                instance_ids = list(self.instance_manager.instances.keys())
                deleted_count = 0

                for instance_id in instance_ids:
                    # 先检查实例是否正在运行
                    instance = self.instance_manager.get_instance(instance_id)
                    if instance and instance.status == 'running':
                        print(f"🛑 实例正在运行，先停止实例 {instance_id[:8]}...")
                        self.instance_manager.stop_instance(instance_id)

                    # 删除实例文件夹
                    instance_folder = os.path.join(os.getcwd(), "persistent_data", f"instance_{instance_id[:8]}")
                    if os.path.exists(instance_folder):
                        shutil.rmtree(instance_folder, ignore_errors=True)
                        print(f"删除实例文件夹: {instance_folder}")

                    # 删除实例
                    self.instance_manager.remove_instance(instance_id)
                    deleted_count += 1

                self.update_instance_list()
                self.save_settings()
                self.status_label.config(text=f"已删除 {deleted_count} 个实例和相关文件")
            except Exception as e:
                messagebox.showerror("错误", f"删除所有实例失败: {e}")
    
    def update_instance_list(self):
        """更新实例列表"""
        # 保存当前选中的项目
        selected_items = self.tree.selection()

        # 如果有搜索条件，使用过滤显示
        search_text = getattr(self, 'search_var', None)
        if search_text and search_text.get():
            self.filter_instances(search_text.get().lower())
            # 恢复选中状态
            self._restore_selection(selected_items)
            return

        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 添加实例
        for instance_id, instance in self.instance_manager.instances.items():
            # 初始化选择状态（如果不存在）
            if instance_id not in self.selected_instances:
                self.selected_instances[instance_id] = False

            # 状态中文化
            status_map = {
                'running': '正在运行',
                'stopped': '已停止',
                'starting': '启动中',
                'stopping': '停止中',
                'error': '错误'
            }
            status_text = status_map.get(instance.status, instance.status)

            # 复选框状态
            checkbox_text = "☑" if self.selected_instances.get(instance_id, False) else "☐"

            self.tree.insert("", "end", iid=instance_id, values=(
                checkbox_text,  # 复选框列
                instance.name,
                getattr(instance, 'remark', ''),
                instance.exe_path,
                getattr(instance, 'proxy_ip', ''),
                status_text,
                instance.pid if instance.pid else ''
            ))

        # 清理已删除实例的选择状态
        existing_ids = set(self.instance_manager.instances.keys())
        self.selected_instances = {k: v for k, v in self.selected_instances.items() if k in existing_ids}

        # 恢复选中状态
        self._restore_selection(selected_items)

    def _restore_selection(self, selected_items):
        """恢复选中状态"""
        try:
            for item_id in selected_items:
                if self.tree.exists(item_id):
                    self.tree.selection_add(item_id)
        except Exception as e:
            # 如果恢复失败，忽略错误
            pass
    
    def update_status(self):
        """更新状态信息"""
        try:
            # 更新实例列表
            self.update_instance_list()
            
            # 更新统计信息
            total = len(self.instance_manager.instances)
            running = sum(1 for instance in self.instance_manager.instances.values() 
                         if instance.status == "running")
            
            self.stats_label.config(text=f"总计: {total} | 运行中: {running}")
            
        except Exception as e:
            print(f"更新状态失败: {e}")
        
        # 每秒更新一次
        self.root.after(1000, self.update_status)
    
    def save_settings(self):
        """保存设置到文件"""
        try:
            settings = {
                'instances': [],
                'window_geometry': self.root.geometry(),
                'saved_time': datetime.now().isoformat()
            }
            
            # 保存实例信息
            for instance_id, instance in self.instance_manager.instances.items():
                settings['instances'].append({
                    'id': instance_id,
                    'name': instance.name,
                    'exe_path': instance.exe_path,
                    'remark': getattr(instance, 'remark', ''),
                    'proxy_ip': getattr(instance, 'proxy_ip', ''),
                    'proxy_port': getattr(instance, 'proxy_port', ''),
                    'proxy_username': getattr(instance, 'proxy_username', ''),
                    'proxy_password': getattr(instance, 'proxy_password', '')
                })
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def load_settings(self):
        """从文件加载设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                # 恢复窗口大小
                if 'window_geometry' in settings:
                    self.root.geometry(settings['window_geometry'])
                
                # 恢复实例
                for instance_data in settings.get('instances', []):
                    try:
                        # 创建代理配置字典
                        proxy_config = {
                            'ip': instance_data.get('proxy_ip', ''),
                            'port': instance_data.get('proxy_port', ''),
                            'username': instance_data.get('proxy_username', ''),
                            'password': instance_data.get('proxy_password', '')
                        }

                        # 使用保存的实例ID，而不是重新生成
                        saved_instance_id = instance_data.get('id')
                        if saved_instance_id:
                            # 直接使用保存的ID创建实例
                            from src.core.instance_manager import Instance
                            instance = Instance(saved_instance_id, instance_data['exe_path'], proxy_config)

                            # 设置实例名称和备注
                            if instance_data.get('name'):
                                instance.name = instance_data['name']
                            if instance_data.get('remark'):
                                instance.remark = instance_data['remark']

                            # 添加到实例管理器
                            self.instance_manager.instances[saved_instance_id] = instance

                            print(f"恢复实例: {saved_instance_id[:8]} - {instance_data.get('name', 'Unknown')}")
                        else:
                            # 如果没有保存的ID，创建新实例
                            instance_id = self.instance_manager.create_instance(
                                instance_data['exe_path'],
                                proxy_config
                            )

                            # 设置实例名称和备注
                            instance = self.instance_manager.get_instance(instance_id)
                            if instance:
                                if instance_data.get('name'):
                                    instance.name = instance_data['name']
                                if instance_data.get('remark'):
                                    instance.remark = instance_data['remark']

                            print(f"创建新实例: {instance_id[:8]} - {instance_data.get('name', 'Unknown')}")

                    except Exception as e:
                        print(f"恢复实例失败: {e}")
                        import traceback
                        traceback.print_exc()
                
                self.status_label.config(text=f"已加载 {len(settings.get('instances', []))} 个实例")
                
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def on_closing(self):
        """窗口关闭事件"""
        print("🔄 程序正在关闭，清理所有进程...")

        # 自动保存设置
        self.save_settings()

        # 强制停止所有实例
        try:
            running_instances = []
            for instance_id in list(self.instance_manager.instances.keys()):
                instance = self.instance_manager.get_instance(instance_id)
                if instance and instance.status == 'running':
                    running_instances.append(instance_id)

            if running_instances:
                print(f"🛑 发现 {len(running_instances)} 个运行中的实例，正在强制关闭...")

                # 使用实例管理器的停止方法
                for instance_id in running_instances:
                    print(f"  停止实例: {instance_id[:8]}")
                    self.instance_manager.stop_instance(instance_id)

                # 额外的强制清理
                self.force_cleanup_all_processes()

                print("✅ 所有实例已关闭")
            else:
                print("✅ 没有运行中的实例")

        except Exception as e:
            print(f"关闭实例时出错: {e}")
            # 即使出错也要尝试强制清理
            self.force_cleanup_all_processes()

        # 恢复设备信息（如果已修改）
        try:
            from src.utils.device_modifier import device_modifier
            if device_modifier.is_modified():
                print(f"🔄 恢复原始设备信息...")
                device_modifier.restore_device_info()
        except ImportError:
            pass
        except Exception as e:
            print(f"恢复设备信息时出错: {e}")

        # 关闭窗口
        self.root.destroy()

    def force_cleanup_all_processes(self):
        """强制清理所有相关进程（增强版）"""
        try:
            import psutil
            import os
            import time

            print("🧹 开始强制清理所有相关进程...")

            # 收集所有需要清理的进程
            processes_to_kill = []

            # 1. 获取当前进程的所有子进程
            try:
                current_pid = os.getpid()
                current_process = psutil.Process(current_pid)
                children = current_process.children(recursive=True)

                print(f"🔍 发现 {len(children)} 个子进程")

                for child in children:
                    try:
                        if child.is_running():
                            processes_to_kill.append({
                                'process': child,
                                'pid': child.pid,
                                'name': child.name(),
                                'type': '子进程'
                            })
                    except psutil.NoSuchProcess:
                        pass
            except Exception as e:
                print(f"⚠️ 获取子进程失败: {e}")

            # 2. 查找所有QingTalk相关进程
            try:
                for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
                    try:
                        proc_info = proc.info
                        if not proc_info['name']:
                            continue

                        # 检查是否是QingTalk进程
                        is_qingtalk = False
                        process_type = ""

                        # 通过进程名判断
                        if 'qingtalk' in proc_info['name'].lower():
                            is_qingtalk = True
                            process_type = "QingTalk进程"

                        # 通过exe路径判断（我们启动的进程）
                        if proc_info['exe'] and 'persistent_data' in proc_info['exe']:
                            is_qingtalk = True
                            process_type = "多开进程"

                        # 通过命令行参数判断
                        if proc_info['cmdline']:
                            cmdline_str = ' '.join(proc_info['cmdline'])
                            if 'persistent_data' in cmdline_str:
                                is_qingtalk = True
                                process_type = "多开进程"

                        if is_qingtalk:
                            # 避免重复添加
                            if not any(p['pid'] == proc_info['pid'] for p in processes_to_kill):
                                processes_to_kill.append({
                                    'process': proc,
                                    'pid': proc_info['pid'],
                                    'name': proc_info['name'],
                                    'type': process_type,
                                    'exe': proc_info['exe']
                                })

                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                    except Exception as e:
                        pass

            except Exception as e:
                print(f"⚠️ 搜索QingTalk进程失败: {e}")

            # 3. 执行清理
            if processes_to_kill:
                print(f"🛑 准备清理 {len(processes_to_kill)} 个进程...")

                for proc_info in processes_to_kill:
                    try:
                        proc = proc_info['process']
                        pid = proc_info['pid']
                        name = proc_info['name']
                        proc_type = proc_info['type']

                        if proc.is_running():
                            print(f"  🔄 正在关闭{proc_type}: {pid} ({name})")

                            # 先尝试优雅关闭
                            proc.terminate()
                            try:
                                proc.wait(timeout=3)
                                print(f"    ✅ {proc_type} {pid} 优雅关闭成功")
                            except psutil.TimeoutExpired:
                                # 强制杀死
                                print(f"    ⚡ 强制关闭{proc_type} {pid}")
                                proc.kill()
                                try:
                                    proc.wait(timeout=2)
                                    print(f"    ✅ {proc_type} {pid} 强制关闭成功")
                                except:
                                    print(f"    ❌ {proc_type} {pid} 关闭失败")
                        else:
                            print(f"    ✅ {proc_type} {pid} 已经停止")

                    except psutil.NoSuchProcess:
                        print(f"    ✅ 进程 {pid} 已不存在")
                    except Exception as e:
                        print(f"    ❌ 清理进程 {pid} 失败: {e}")

                # 等待一下确保进程完全关闭
                time.sleep(1)

                # 4. 验证清理结果
                print("🔍 验证清理结果...")
                remaining_count = 0
                for proc_info in processes_to_kill:
                    try:
                        if proc_info['process'].is_running():
                            remaining_count += 1
                            print(f"    ⚠️ 进程 {proc_info['pid']} 仍在运行")
                    except psutil.NoSuchProcess:
                        pass

                if remaining_count == 0:
                    print("✅ 所有进程已成功清理")
                else:
                    print(f"⚠️ 仍有 {remaining_count} 个进程未能清理")

            else:
                print("✅ 没有发现需要清理的进程")

        except ImportError:
            print("⚠️ psutil未安装，无法进行强制清理")
            print("💡 安装psutil可以更好地管理进程: pip install psutil")
        except Exception as e:
            print(f"❌ 强制清理失败: {e}")
    
    def run(self):
        """运行主循环"""
        self.root.mainloop()
