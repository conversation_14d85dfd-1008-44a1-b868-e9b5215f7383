# 多开管理器

一个类似雷电模拟器的多开管理工具，支持exe程序的沙盒多开、代理IP、系统标识修改等功能。

## 功能特性

### 🚀 核心功能
- **多开管理**: 支持任意exe程序的多开启动
- **沙盒隔离**: 每个实例运行在独立的沙盒环境中
- **代理支持**: 为每个实例分配不同的代理IP
- **系统标识修改**: 修改MAC地址、计算机名、机器GUID等
- **中控界面**: 类似雷电模拟器的直观管理界面

### 🛡️ 安全特性
- 进程隔离
- 注册表隔离
- 文件系统隔离
- 环境变量隔离

### 🌐 代理功能
- 支持HTTP/HTTPS/SOCKS4/SOCKS5代理
- 自动代理分配
- 代理健康检查
- 代理统计监控

### ⚙️ 系统修改
- MAC地址随机化
- 计算机名修改
- 机器GUID修改
- 硬件配置文件修改

## 系统要求

- Windows 10/11
- Python 3.7+
- 管理员权限（某些功能需要）

## 安装使用

### 方法一：使用启动脚本（推荐）
1. 下载项目到本地
2. 双击运行 `start.bat`
3. 脚本会自动创建虚拟环境并安装依赖

### 方法二：手动安装
1. 克隆项目
```bash
git clone <项目地址>
cd duokai
```

2. 创建虚拟环境
```bash
python -m venv venv
venv\Scripts\activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 运行程序
```bash
python main.py
```

## 使用说明

### 1. 新建实例
- 点击"新建实例"按钮
- 选择要多开的exe文件
- 系统会自动创建实例并分配代理

### 2. 管理实例
- 在"实例管理"标签页查看所有实例
- 可以启动、停止、删除实例
- 双击实例查看详细信息

### 3. 代理管理
- 在"代理管理"标签页添加代理服务器
- 支持HTTP/HTTPS/SOCKS4/SOCKS5协议
- 可以测试代理连通性

### 4. 系统设置
- 在"设置"标签页配置程序参数
- 可以设置沙盒选项、代理选项等
- 支持导入导出配置

## 项目结构

```
duokai/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── start.bat              # 启动脚本
├── README.md              # 说明文档
├── src/                   # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── config_manager.py      # 配置管理
│   │   ├── instance_manager.py    # 实例管理
│   │   ├── sandbox_manager.py     # 沙盒管理
│   │   ├── proxy_manager.py       # 代理管理
│   │   └── system_modifier.py     # 系统修改
│   └── gui/               # 界面模块
│       ├── main_window.py         # 主窗口
│       ├── instance_panel.py      # 实例面板
│       ├── proxy_panel.py         # 代理面板
│       └── settings_panel.py      # 设置面板
└── config/                # 配置文件目录（运行时创建）
    ├── app_config.json    # 应用配置
    ├── instances.json     # 实例配置
    └── proxies.json       # 代理配置
```

## 注意事项

### ⚠️ 重要提醒
1. **管理员权限**: 某些功能（如修改系统标识）需要管理员权限
2. **杀毒软件**: 可能被杀毒软件误报，请添加到白名单
3. **法律合规**: 请遵守相关法律法规，不要用于非法用途
4. **系统稳定性**: 修改系统标识可能影响系统稳定性，请谨慎使用

### 🔧 故障排除
1. **程序无法启动**: 检查Python版本和依赖包
2. **权限不足**: 以管理员身份运行
3. **代理连接失败**: 检查代理服务器设置
4. **实例启动失败**: 检查exe文件路径和权限
5. **无法多开**:
   - 某些程序有单实例限制，程序会自动创建隔离的exe副本
   - 尝试以管理员身份运行获得更好的隔离效果
   - 检查目标程序是否支持多开
6. **停止功能不工作**: 已修复，现在支持强制终止进程
7. **系统标识修改失败**: 需要管理员权限

### 🆕 最新修复 (v1.2)
- ✅ 修复了停止功能不工作的问题
- ✅ 增加了进程隔离功能，通过创建exe副本避免进程名冲突
- ✅ 改进了进程终止机制，支持优雅关闭和强制终止
- ✅ 增强了环境变量隔离，每个实例有独立的标识
- ✅ 自动清理临时文件和隔离的exe文件
- ✅ **新增配置文件隔离**：解决Electron应用（如钉钉）的配置冲突
- ✅ **应用特定支持**：为不同应用提供专门的启动参数
- ✅ **用户数据目录隔离**：每个实例使用独立的用户数据目录

## 开发说明

### 技术栈
- **语言**: Python 3.7+
- **GUI框架**: Tkinter
- **进程管理**: psutil
- **网络请求**: requests
- **系统API**: pywin32

### 开发环境
1. 安装开发依赖
```bash
pip install -r requirements.txt
```

2. 运行测试
```bash
python -m pytest tests/
```

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 免责声明

本软件仅供学习和研究使用，开发者不对使用本软件造成的任何后果负责。请用户遵守当地法律法规，合理使用本软件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发起 Pull Request
- 邮件联系: [<EMAIL>]

---

**感谢使用多开管理器！**
