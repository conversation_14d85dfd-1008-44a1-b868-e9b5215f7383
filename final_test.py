#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试
完全自动化的多开测试
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_instance_manager():
    """测试实例管理器"""
    print("=" * 50)
    print("实例管理器测试")
    print("=" * 50)
    
    try:
        from src.core.config_manager import ConfigManager
        from src.core.instance_manager import InstanceManager
        
        # 创建管理器
        config_manager = ConfigManager()
        config_manager.load_config()
        instance_manager = InstanceManager(config_manager)
        
        print("✅ 管理器创建成功")
        
        # 测试程序
        test_exe = r"C:\Windows\System32\notepad.exe"
        
        if not os.path.exists(test_exe):
            print(f"❌ 测试程序不存在: {test_exe}")
            return False
        
        print(f"📝 使用测试程序: {test_exe}")
        
        # 创建实例
        instance_ids = []
        for i in range(2):
            instance_id = instance_manager.create_instance(test_exe)
            instance_ids.append(instance_id)
            print(f"✅ 实例 {i+1} 创建成功: {instance_id[:8]}...")
        
        # 启动实例
        running_count = 0
        for i, instance_id in enumerate(instance_ids):
            print(f"\n启动实例 {i+1}...")
            success = instance_manager.start_instance(instance_id)
            
            if success:
                instance = instance_manager.get_instance(instance_id)
                print(f"✅ 实例 {i+1} 启动成功 (PID: {instance.pid})")
                
                # 检查是否真的在运行
                time.sleep(2)
                if instance.is_running():
                    print(f"✅ 实例 {i+1} 确认运行中")
                    running_count += 1
                else:
                    print(f"❌ 实例 {i+1} 启动后停止了")
            else:
                print(f"❌ 实例 {i+1} 启动失败")
        
        print(f"\n📊 运行统计: {running_count}/{len(instance_ids)} 个实例成功运行")
        
        # 自动停止所有实例
        print("\n🛑 停止所有实例...")
        for i, instance_id in enumerate(instance_ids):
            instance_manager.stop_instance(instance_id)
            instance_manager.remove_instance(instance_id)
            print(f"✅ 实例 {i+1} 已停止并清理")
        
        return running_count > 1
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("多开管理器 - 最终测试")
    
    # 测试实例管理器
    success = test_instance_manager()
    
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    
    if success:
        print("🎉 多开功能测试成功！")
        print("现在可以启动主程序进行完整测试。")
    else:
        print("❌ 多开功能测试失败。")
        print("可能的原因：")
        print("1. 目标程序有单实例限制")
        print("2. 系统权限不足")
        print("3. 代码逻辑问题")
    
    print("\n测试完成。")

if __name__ == "__main__":
    main()
