#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面设备信息修改工具
修改所有可能被QingTalk检测的设备信息
"""

import os
import subprocess
import winreg
import tempfile
import shutil
import time
from typing import Dict, List, Tuple

class ComprehensiveDeviceModifier:
    """全面设备信息修改器"""
    
    def __init__(self):
        self.backup_values = {}
        self.modified_keys = []
        self.temp_files = []
    
    def backup_and_modify_registry(self, hkey, subkey: str, value_name: str, new_value: str) -> bool:
        """备份并修改注册表值"""
        try:
            # 备份原值
            with winreg.OpenKey(hkey, subkey) as key:
                original_value, value_type = winreg.QueryValueEx(key, value_name)
                
                backup_key = f"{hkey}\\{subkey}\\{value_name}"
                self.backup_values[backup_key] = (original_value, value_type)
                
                print(f"  📋 备份: {value_name} = {original_value}")
            
            # 修改值
            with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, new_value)
                
            self.modified_keys.append((hkey, subkey, value_name))
            print(f"  ✅ 修改: {value_name} = {new_value}")
            return True
            
        except Exception as e:
            print(f"  ❌ 修改失败: {subkey}\\{value_name} - {e}")
            return False
    
    def modify_all_computer_names(self, fake_computer: str) -> int:
        """修改所有计算机名相关的注册表项"""
        print(f"🖥️ 修改所有计算机名信息 -> {fake_computer}")
        
        # 所有可能的计算机名注册表位置
        computer_registry_keys = [
            # 基本计算机名
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", "ComputerName"),
            
            # TCP/IP参数
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Hostname"),
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Hostname"),
            
            # 网络相关
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\lanmanserver\parameters", "srvcomment"),
            
            # 系统信息
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOwner"),
        ]
        
        success_count = 0
        for hkey, subkey, value_name in computer_registry_keys:
            if self.backup_and_modify_registry(hkey, subkey, value_name, fake_computer):
                success_count += 1
        
        return success_count
    
    def modify_hardware_info(self, fake_info: Dict[str, str]) -> int:
        """修改硬件信息"""
        print(f"💻 修改硬件信息")
        
        # 硬件信息注册表位置
        hardware_keys = [
            # BIOS信息
            (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemManufacturer", fake_info.get('manufacturer', 'ACME Corp')),
            (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemProductName", fake_info.get('product', 'TestPC-2024')),
            (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "BIOSVendor", fake_info.get('bios_vendor', 'Phoenix Technologies')),
            
            # 处理器信息
            (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0", "ProcessorNameString", fake_info.get('processor', 'Intel(R) Core(TM) i5-8400 CPU @ 2.80GHz')),
            (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0", "Identifier", fake_info.get('cpu_id', 'x86 Family 6 Model 158 Stepping 10')),
        ]
        
        success_count = 0
        for hkey, subkey, value_name, new_value in hardware_keys:
            if self.backup_and_modify_registry(hkey, subkey, value_name, new_value):
                success_count += 1
        
        return success_count
    
    def modify_environment_variables(self, fake_computer: str, fake_user: str) -> bool:
        """修改当前进程的环境变量"""
        print(f"🌍 修改环境变量")
        
        try:
            # 修改当前进程的环境变量
            os.environ['COMPUTERNAME'] = fake_computer
            os.environ['USERNAME'] = fake_user
            os.environ['USERDOMAIN'] = fake_computer
            os.environ['LOGONSERVER'] = f'\\\\{fake_computer}'
            
            print(f"  ✅ COMPUTERNAME = {fake_computer}")
            print(f"  ✅ USERNAME = {fake_user}")
            print(f"  ✅ USERDOMAIN = {fake_computer}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 环境变量修改失败: {e}")
            return False
    
    def create_fake_system_files(self, fake_computer: str) -> bool:
        """创建伪造的系统文件"""
        print(f"📁 创建伪造系统文件")
        
        try:
            # 创建临时hosts文件修改
            hosts_content = f"""
# 临时hosts文件修改
127.0.0.1 {fake_computer}
127.0.0.1 {fake_computer.lower()}
"""
            
            temp_dir = tempfile.mkdtemp(prefix="fake_system_")
            hosts_file = os.path.join(temp_dir, "hosts_backup")
            
            with open(hosts_file, 'w') as f:
                f.write(hosts_content)
            
            self.temp_files.append(temp_dir)
            print(f"  ✅ 创建临时系统文件: {temp_dir}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 创建系统文件失败: {e}")
            return False
    
    def restore_all_changes(self):
        """恢复所有修改"""
        print(f"🔄 恢复所有修改...")
        
        # 恢复注册表
        for backup_key, (original_value, value_type) in self.backup_values.items():
            try:
                parts = backup_key.split('\\')
                hkey_str = parts[0]
                subkey = '\\'.join(parts[1:-1])
                value_name = parts[-1]
                
                if 'HKEY_LOCAL_MACHINE' in hkey_str:
                    hkey = winreg.HKEY_LOCAL_MACHINE
                elif 'HKEY_CURRENT_USER' in hkey_str:
                    hkey = winreg.HKEY_CURRENT_USER
                else:
                    continue
                
                with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                    winreg.SetValueEx(key, value_name, 0, value_type, original_value)
                    
                print(f"  ✅ 恢复: {value_name} = {original_value}")
                
            except Exception as e:
                print(f"  ❌ 恢复失败: {backup_key} - {e}")
        
        # 清理临时文件
        for temp_dir in self.temp_files:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"  🗑️ 清理临时文件: {temp_dir}")
            except Exception as e:
                print(f"  ❌ 清理失败: {temp_dir} - {e}")

def generate_fake_device_info(instance_id: str) -> Dict[str, str]:
    """生成伪造的设备信息"""
    import random
    import string
    import hashlib
    
    # 使用实例ID作为种子确保一致性
    seed_value = int(hashlib.md5(instance_id.encode()).hexdigest()[:8], 16)
    random.seed(seed_value)
    
    unique_suffix = instance_id[:8].upper()
    
    fake_info = {
        'computer': f'TESTPC-{unique_suffix}',
        'user': f'TestUser{unique_suffix}',
        'manufacturer': random.choice(['Dell Inc.', 'HP', 'Lenovo', 'ASUS', 'Acer']),
        'product': f'TestModel-{unique_suffix}',
        'bios_vendor': random.choice(['American Megatrends Inc.', 'Phoenix Technologies', 'Insyde Corp.']),
        'processor': f'Intel(R) Core(TM) i{random.choice([3,5,7])}-{random.randint(8000,12000)} CPU @ {random.uniform(2.0,4.0):.1f}GHz',
        'cpu_id': f'x86 Family 6 Model {random.randint(140,200)} Stepping {random.randint(1,15)}',
    }
    
    return fake_info

def test_comprehensive_modification():
    """测试全面设备信息修改"""
    print("=" * 60)
    print("全面设备信息修改测试")
    print("=" * 60)
    
    # 检查管理员权限
    import ctypes
    if not ctypes.windll.shell32.IsUserAnAdmin():
        print("❌ 需要管理员权限！")
        print("💡 请以管理员身份运行此脚本")
        return False
    
    print("✅ 检测到管理员权限")
    
    # 生成伪造信息
    import uuid
    instance_id = str(uuid.uuid4())[:8]
    fake_info = generate_fake_device_info(instance_id)
    
    print(f"\n🎯 将要修改的设备信息:")
    print(f"  计算机名: {fake_info['computer']}")
    print(f"  用户名: {fake_info['user']}")
    print(f"  制造商: {fake_info['manufacturer']}")
    print(f"  产品型号: {fake_info['product']}")
    print(f"  处理器: {fake_info['processor']}")
    
    # 创建修改器
    modifier = ComprehensiveDeviceModifier()
    
    try:
        print(f"\n🔧 开始全面修改...")
        
        # 修改所有计算机名
        computer_count = modifier.modify_all_computer_names(fake_info['computer'])
        print(f"  📊 计算机名修改: {computer_count} 项成功")
        
        # 修改硬件信息
        hardware_count = modifier.modify_hardware_info(fake_info)
        print(f"  📊 硬件信息修改: {hardware_count} 项成功")
        
        # 修改环境变量
        env_success = modifier.modify_environment_variables(fake_info['computer'], fake_info['user'])
        print(f"  📊 环境变量修改: {'成功' if env_success else '失败'}")
        
        # 创建伪造文件
        file_success = modifier.create_fake_system_files(fake_info['computer'])
        print(f"  📊 系统文件创建: {'成功' if file_success else '失败'}")
        
        total_success = computer_count + hardware_count + (1 if env_success else 0) + (1 if file_success else 0)
        
        if total_success > 0:
            print(f"\n✅ 修改完成！成功修改 {total_success} 项")
            print(f"⏳ 等待5秒让修改生效...")
            time.sleep(5)
            
            # 验证修改结果
            print(f"\n🔍 验证修改结果:")
            print(f"  环境变量 COMPUTERNAME: {os.environ.get('COMPUTERNAME', 'Unknown')}")
            print(f"  Python platform.node(): {__import__('platform').node()}")
            
            try:
                result = subprocess.run(['hostname'], capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"  命令行 hostname: {result.stdout.strip()}")
            except:
                pass
            
            print(f"\n🎯 现在请启动QingTalk测试设备信息变化！")
            input("按回车键恢复所有修改...")
            
        else:
            print(f"\n❌ 所有修改都失败了")
        
        # 恢复修改
        modifier.restore_all_changes()
        print(f"\n✅ 所有修改已恢复")
        
        return total_success > 0
        
    except Exception as e:
        print(f"❌ 修改过程出错: {e}")
        # 确保恢复修改
        modifier.restore_all_changes()
        return False

def main():
    print("QingTalk全面设备信息修改工具")
    print("=" * 50)
    
    print("💡 此工具将修改所有可能被QingTalk检测的设备信息:")
    print("  - 注册表中的计算机名")
    print("  - 硬件信息 (BIOS、处理器等)")
    print("  - 环境变量")
    print("  - 系统文件")
    
    print("\n⚠️ 注意事项:")
    print("  - 需要管理员权限")
    print("  - 会自动备份和恢复所有修改")
    print("  - 测试完成后立即恢复原始设置")
    
    choice = input("\n是否继续测试？(y/n): ").lower()
    
    if choice == 'y':
        success = test_comprehensive_modification()
        
        if success:
            print("\n🎉 全面修改测试完成！")
            print("💡 请检查QingTalk是否显示了新的设备信息")
        else:
            print("\n❌ 全面修改测试失败")
            print("💡 QingTalk可能使用了更深层的检测方法")
    else:
        print("测试取消")

if __name__ == "__main__":
    main()
