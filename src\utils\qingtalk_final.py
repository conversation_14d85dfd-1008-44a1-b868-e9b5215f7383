#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk最终多开方案
使用进程名伪装和完全隔离
"""

import os
import subprocess
import tempfile
import shutil
import time
import uuid
from typing import Optional

def launch_qingtalk_final(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk最终多开方案 - 进程名伪装
    """
    try:
        print(f"🎯 QingTalk最终多开启动 (实例: {instance_id[:8]})")
        
        # 1. 创建持久化环境
        persistent_base = os.path.join(os.getcwd(), "persistent_data")
        os.makedirs(persistent_base, exist_ok=True)
        
        instance_dir = os.path.join(persistent_base, f"instance_{instance_id[:8]}")
        qingtalk_program_dir = os.path.join(instance_dir, "QingTalk")
        
        # 检查是否需要复制程序
        is_first_time = not os.path.exists(qingtalk_program_dir)
        
        if is_first_time:
            print(f"  🆕 首次启动，复制QingTalk程序...")
            os.makedirs(instance_dir, exist_ok=True)
            
            qingtalk_source_dir = os.path.dirname(exe_path)
            shutil.copytree(qingtalk_source_dir, qingtalk_program_dir, 
                           ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))
            
            # 重命名可执行文件（进程名伪装）
            original_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")
            disguised_exe = os.path.join(qingtalk_program_dir, f"QingTalk_{instance_id[:8]}.exe")
            
            if os.path.exists(original_exe):
                shutil.copy2(original_exe, disguised_exe)
                print(f"  🎭 创建伪装程序: QingTalk_{instance_id[:8]}.exe")
        else:
            print(f"  🔄 重用现有QingTalk程序...")
        
        # 使用伪装的可执行文件
        disguised_exe = os.path.join(qingtalk_program_dir, f"QingTalk_{instance_id[:8]}.exe")
        
        if not os.path.exists(disguised_exe):
            # 如果伪装文件不存在，创建它
            original_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")
            if os.path.exists(original_exe):
                shutil.copy2(original_exe, disguised_exe)
                print(f"  🎭 创建伪装程序: QingTalk_{instance_id[:8]}.exe")
        
        # 2. 创建用户数据目录
        user_data_dir = os.path.join(instance_dir, 'UserData')
        appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
        temp_dir = os.path.join(user_data_dir, 'Temp')
        
        # 创建目录结构
        for path in [appdata_roaming, appdata_local, temp_dir]:
            os.makedirs(path, exist_ok=True)
        
        # 3. 设置完全隔离的环境
        env = os.environ.copy()
        
        # 基础隔离
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': user_data_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
        })
        
        # 系统标识完全伪造
        unique_suffix = instance_id[:8]
        fake_computer = f'DESKTOP-{unique_suffix.upper()}'
        fake_user = f'User{unique_suffix}'
        
        env.update({
            'COMPUTERNAME': fake_computer,
            'USERNAME': fake_user,
            'USERDOMAIN': fake_computer,
            'SESSIONNAME': f'Console',
            'LOGONSERVER': f'\\\\{fake_computer}',
            'USERDNSDOMAIN': f'{fake_computer}.local',
        })
        
        # 硬件标识伪造
        env.update({
            'PROCESSOR_IDENTIFIER': f'Intel64 Family 6 Model {sum(ord(c) for c in instance_id) % 100} Stepping 1',
            'PROCESSOR_ARCHITECTURE': 'AMD64',
            'NUMBER_OF_PROCESSORS': '4',
            'PROCESSOR_LEVEL': '6',
            'PROCESSOR_REVISION': f'{sum(ord(c) for c in instance_id) % 9999:04x}',
        })
        
        # 网络隔离
        port_base = 40000 + (sum(ord(c) for c in instance_id) % 10000)
        env.update({
            'QINGTALK_PORT_BASE': str(port_base),
            'QINGTALK_IPC_PORT': str(port_base + 1),
            'QINGTALK_SYNC_PORT': str(port_base + 2),
            'QINGTALK_UPDATE_PORT': str(port_base + 3),
        })
        
        # 进程通信隔离
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_ISOLATION_MODE': '1',
            'QINGTALK_MUTEX_PREFIX': f'QT_{unique_suffix}_',
            'QINGTALK_PIPE_PREFIX': f'QTPipe_{unique_suffix}_',
            'QINGTALK_SHARED_MEM_PREFIX': f'QTMem_{unique_suffix}_',
        })
        
        # Windows目录隔离
        env.update({
            'ALLUSERSPROFILE': os.path.join(user_data_dir, 'ProgramData'),
            'PROGRAMDATA': os.path.join(user_data_dir, 'ProgramData'),
            'PUBLIC': os.path.join(user_data_dir, 'Public'),
        })
        
        # 创建必要目录
        for env_path in [env['ALLUSERSPROFILE'], env['PUBLIC']]:
            os.makedirs(env_path, exist_ok=True)
        
        print(f"  📂 程序目录: {qingtalk_program_dir}")
        print(f"  🎭 伪装程序: QingTalk_{instance_id[:8]}.exe")
        print(f"  💾 数据目录: {user_data_dir}")
        print(f"  🖥️ 伪造计算机: {fake_computer}")
        print(f"  👤 伪造用户: {fake_user}")
        print(f"  🔧 端口基数: {port_base}")
        print(f"  {'🆕 首次创建' if is_first_time else '🔄 重用现有'}")
        
        # 4. 启动伪装的QingTalk
        creation_flags = subprocess.CREATE_NEW_PROCESS_GROUP
        
        process = subprocess.Popen(
            disguised_exe,
            env=env,
            cwd=qingtalk_program_dir,
            creationflags=creation_flags
        )
        
        # 保存信息
        process.isolation_info = {
            'program_dir': qingtalk_program_dir,
            'user_data_dir': instance_dir,
            'disguised_exe': disguised_exe,
            'method': 'qingtalk_final',
            'instance_id': instance_id,
            'port_base': port_base,
            'persistent': True,
            'is_first_time': is_first_time
        }
        
        print(f"  ✅ QingTalk最终多开启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk最终多开启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_qingtalk_final(process):
    """清理最终多开资源（保持程序和数据）"""
    try:
        if hasattr(process, 'isolation_info'):
            isolation_info = process.isolation_info
            instance_id = isolation_info.get('instance_id', 'unknown')
            
            # 不清理程序目录和用户数据，保持登录状态
            print(f"💾 保持实例 {instance_id[:8]} 的程序文件和登录状态")
            print(f"📂 程序目录保留: {isolation_info.get('program_dir', 'unknown')}")
            print(f"💾 数据目录保留: {isolation_info.get('user_data_dir', 'unknown')}")
            print(f"🎭 伪装程序保留: {isolation_info.get('disguised_exe', 'unknown')}")
            
    except Exception as e:
        print(f"⚠️ 清理最终多开资源失败: {e}")

if __name__ == "__main__":
    # 测试最终多开
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("测试QingTalk最终多开")
        
        # 启动两个实例
        process1 = launch_qingtalk_final(qingtalk_exe, str(uuid.uuid4()))
        time.sleep(5)
        process2 = launch_qingtalk_final(qingtalk_exe, str(uuid.uuid4()))
        
        if process1 and process2:
            print("🎉 两个实例都启动成功！")
            
            # 等待测试
            input("按回车键停止测试...")
            
            # 清理
            if process1.poll() is None:
                process1.terminate()
                cleanup_qingtalk_final(process1)
            
            if process2.poll() is None:
                process2.terminate()
                cleanup_qingtalk_final(process2)
        else:
            print("❌ 启动失败")
    else:
        print("❌ QingTalk程序不存在")
