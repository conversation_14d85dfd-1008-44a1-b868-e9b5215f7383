#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("测试模块导入...")
    
    from src.core.config_manager import ConfigManager
    print("✅ ConfigManager")
    
    from src.core.instance_manager import InstanceManager
    print("✅ InstanceManager")
    
    from src.core.proxy_manager import ProxyManager
    print("✅ ProxyManager")
    
    print("\n测试基础功能...")
    
    # 测试配置管理器
    config_manager = ConfigManager()
    config_manager.load_config()
    print("✅ 配置管理器初始化")
    
    # 测试实例管理器
    instance_manager = InstanceManager(config_manager)
    print("✅ 实例管理器初始化")
    
    # 测试创建实例（使用一个肯定存在的文件）
    test_exe = r"C:\Windows\System32\notepad.exe"
    if os.path.exists(test_exe):
        instance_id = instance_manager.create_instance(test_exe)
        print(f"✅ 实例创建成功: {instance_id[:8]}...")
        
        # 获取实例信息
        instance = instance_manager.get_instance(instance_id)
        if instance:
            info = instance.get_info()
            print(f"✅ 实例信息获取成功")
            print(f"   状态: {info['status']}")
            print(f"   代理配置: {type(info['proxy_config'])}")
        
        # 删除实例
        instance_manager.remove_instance(instance_id)
        print("✅ 实例删除成功")
    else:
        print("⚠️ 跳过实例测试（notepad.exe不存在）")
    
    print("\n🎉 基础功能测试通过！")
    
except Exception as e:
    print(f"\n❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()

input("\n按回车键退出...")
