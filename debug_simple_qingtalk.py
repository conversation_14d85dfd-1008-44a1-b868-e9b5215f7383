#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的QingTalk启动测试，不使用复杂的环境配置
"""

import os
import subprocess
import time

def test_simple_qingtalk():
    """简单启动QingTalk测试"""
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk不存在: {qingtalk_exe}")
        return False
    
    print("🧪 测试简单启动QingTalk")
    print("-" * 40)
    
    try:
        # 最简单的启动方式
        process = subprocess.Popen(
            qingtalk_exe,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            poll_result = process.poll()
            if poll_result is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {poll_result})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_qingtalk_with_minimal_env():
    """使用最小环境变量启动QingTalk"""
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk不存在: {qingtalk_exe}")
        return False
    
    print("\n🧪 测试最小环境变量启动QingTalk")
    print("-" * 40)
    
    try:
        # 使用最小的环境变量
        env = os.environ.copy()
        env['INSTANCE_ID'] = 'test123'
        
        process = subprocess.Popen(
            qingtalk_exe,
            env=env,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            poll_result = process.poll()
            if poll_result is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {poll_result})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_qingtalk_with_disguised_exe():
    """使用伪装exe启动QingTalk"""
    original_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    disguised_exe = r"D:\text\py\duokai\persistent_data\instance_e0b6d2d7\QingTalk\QingTalk_e0b6d2d7.exe"
    
    if not os.path.exists(disguised_exe):
        print(f"❌ 伪装QingTalk不存在: {disguised_exe}")
        return False
    
    print("\n🧪 测试伪装exe启动QingTalk")
    print("-" * 40)
    
    try:
        # 使用伪装的exe
        process = subprocess.Popen(
            disguised_exe,
            cwd=os.path.dirname(disguised_exe),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            poll_result = process.poll()
            if poll_result is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {poll_result})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("QingTalk简单启动测试")
    print("=" * 60)
    
    tests = [
        ("简单启动", test_simple_qingtalk),
        ("最小环境变量启动", test_qingtalk_with_minimal_env),
        ("伪装exe启动", test_qingtalk_with_disguised_exe),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count > 0:
        print("\n💡 建议:")
        if results.get("简单启动", False):
            print("- 简单启动成功，问题可能在复杂的环境配置")
        if results.get("最小环境变量启动", False):
            print("- 最小环境变量启动成功，可以逐步添加环境变量")
        if not results.get("伪装exe启动", False):
            print("- 伪装exe启动失败，可能是文件权限或路径问题")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
