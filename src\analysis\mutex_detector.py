#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互斥体检测工具
检测QingTalk使用的互斥体和其他内核对象
"""

import subprocess
import re
import json
from datetime import datetime
from typing import List, Dict

class MutexDetector:
    def __init__(self):
        self.mutex_list = []
        self.handle_list = []
    
    def get_system_mutexes(self) -> List[Dict]:
        """获取系统中的所有互斥体"""
        print("获取系统互斥体列表...")
        
        try:
            # 使用handle.exe工具（需要从Sysinternals下载）
            result = subprocess.run(['handle.exe', '-a', '-l'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return self._parse_handle_output(result.stdout)
            else:
                print("handle.exe 不可用，尝试使用PowerShell...")
                return self._get_mutexes_powershell()
                
        except FileNotFoundError:
            print("handle.exe 未找到，尝试使用PowerShell...")
            return self._get_mutexes_powershell()
        except Exception as e:
            print(f"获取互斥体失败: {e}")
            return []
    
    def _parse_handle_output(self, output: str) -> List[Dict]:
        """解析handle.exe的输出"""
        mutexes = []
        lines = output.split('\n')
        
        current_process = None
        
        for line in lines:
            line = line.strip()
            
            # 检测进程行
            if line and not line.startswith(' ') and 'pid:' in line:
                # 解析进程信息
                match = re.search(r'(.+?)\s+pid:\s*(\d+)', line)
                if match:
                    current_process = {
                        'name': match.group(1).strip(),
                        'pid': int(match.group(2))
                    }
            
            # 检测互斥体行
            elif 'Mutant' in line or 'Mutex' in line:
                if current_process:
                    # 解析互斥体信息
                    parts = line.split()
                    if len(parts) >= 4:
                        mutexes.append({
                            'process': current_process['name'],
                            'pid': current_process['pid'],
                            'handle': parts[0],
                            'type': parts[1],
                            'name': ' '.join(parts[3:]) if len(parts) > 3 else ''
                        })
        
        return mutexes
    
    def _get_mutexes_powershell(self) -> List[Dict]:
        """使用PowerShell获取互斥体信息"""
        try:
            # PowerShell脚本来获取句柄信息
            ps_script = '''
            Get-Process | ForEach-Object {
                $proc = $_
                try {
                    $handles = Get-Handle -ProcessId $proc.Id -ErrorAction SilentlyContinue
                    $handles | Where-Object { $_.ObjectType -eq "Mutant" } | ForEach-Object {
                        [PSCustomObject]@{
                            ProcessName = $proc.ProcessName
                            PID = $proc.Id
                            Handle = $_.Handle
                            ObjectName = $_.ObjectName
                        }
                    }
                } catch {}
            }
            '''
            
            result = subprocess.run(['powershell', '-Command', ps_script], 
                                  capture_output=True, text=True, timeout=60)
            
            # 这里需要解析PowerShell输出
            # 由于Get-Handle可能不可用，我们返回空列表
            return []
            
        except Exception as e:
            print(f"PowerShell获取互斥体失败: {e}")
            return []
    
    def find_qingtalk_mutexes(self) -> List[Dict]:
        """查找QingTalk相关的互斥体"""
        print("查找QingTalk相关的互斥体...")
        
        all_mutexes = self.get_system_mutexes()
        qingtalk_mutexes = []
        
        keywords = ['qingtalk', 'dingtalk', 'taobao', 'alibaba', 'alipay']
        
        for mutex in all_mutexes:
            # 检查进程名
            if any(keyword.lower() in mutex['process'].lower() for keyword in keywords):
                qingtalk_mutexes.append(mutex)
                continue
            
            # 检查互斥体名称
            if mutex.get('name') and any(keyword.lower() in mutex['name'].lower() for keyword in keywords):
                qingtalk_mutexes.append(mutex)
        
        return qingtalk_mutexes
    
    def analyze_mutex_patterns(self, mutexes: List[Dict]) -> Dict:
        """分析互斥体模式"""
        analysis = {
            'total_mutexes': len(mutexes),
            'processes': {},
            'mutex_names': [],
            'common_patterns': []
        }
        
        for mutex in mutexes:
            # 按进程分组
            proc_key = f"{mutex['process']} (PID: {mutex['pid']})"
            if proc_key not in analysis['processes']:
                analysis['processes'][proc_key] = []
            analysis['processes'][proc_key].append(mutex)
            
            # 收集互斥体名称
            if mutex.get('name'):
                analysis['mutex_names'].append(mutex['name'])
        
        # 分析常见模式
        name_patterns = {}
        for name in analysis['mutex_names']:
            if name:
                # 提取可能的模式
                patterns = self._extract_patterns(name)
                for pattern in patterns:
                    if pattern not in name_patterns:
                        name_patterns[pattern] = 0
                    name_patterns[pattern] += 1
        
        # 找出常见模式
        analysis['common_patterns'] = [
            {'pattern': pattern, 'count': count}
            for pattern, count in sorted(name_patterns.items(), key=lambda x: x[1], reverse=True)
            if count > 1
        ]
        
        return analysis
    
    def _extract_patterns(self, name: str) -> List[str]:
        """从互斥体名称中提取模式"""
        patterns = []
        
        # GUID模式
        guid_pattern = re.findall(r'[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', name)
        patterns.extend([f"GUID:{guid}" for guid in guid_pattern])
        
        # 数字模式
        number_pattern = re.findall(r'\d+', name)
        if number_pattern:
            patterns.append(f"Numbers:{','.join(number_pattern)}")
        
        # 前缀模式
        if '\\' in name:
            parts = name.split('\\')
            if len(parts) > 1:
                patterns.append(f"Prefix:{parts[0]}")
        
        # 关键词模式
        keywords = ['Global', 'Local', 'Session', 'Mutex', 'Event', 'Semaphore']
        for keyword in keywords:
            if keyword.lower() in name.lower():
                patterns.append(f"Keyword:{keyword}")
        
        return patterns
    
    def save_results(self, mutexes: List[Dict], analysis: Dict, filename: str = None) -> str:
        """保存检测结果"""
        if not filename:
            filename = f"mutex_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'mutexes': mutexes,
            'analysis': analysis
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"互斥体分析结果已保存到: {filename}")
        return filename

def detect_qingtalk_mutexes():
    """检测QingTalk的互斥体"""
    print("=" * 50)
    print("QingTalk互斥体检测")
    print("=" * 50)
    
    detector = MutexDetector()
    
    # 查找QingTalk相关的互斥体
    mutexes = detector.find_qingtalk_mutexes()
    
    # 分析互斥体模式
    analysis = detector.analyze_mutex_patterns(mutexes)
    
    # 保存结果
    filename = detector.save_results(mutexes, analysis)
    
    # 显示结果
    print("\n" + "=" * 50)
    print("检测结果")
    print("=" * 50)
    
    print(f"找到 {analysis['total_mutexes']} 个QingTalk相关的互斥体")
    
    if analysis['processes']:
        print(f"\n相关进程:")
        for proc, proc_mutexes in analysis['processes'].items():
            print(f"  {proc}: {len(proc_mutexes)} 个互斥体")
    
    if analysis['mutex_names']:
        print(f"\n互斥体名称:")
        for name in analysis['mutex_names'][:10]:  # 显示前10个
            print(f"  {name}")
        if len(analysis['mutex_names']) > 10:
            print(f"  ... 还有 {len(analysis['mutex_names']) - 10} 个")
    
    if analysis['common_patterns']:
        print(f"\n常见模式:")
        for pattern_info in analysis['common_patterns'][:5]:
            print(f"  {pattern_info['pattern']}: {pattern_info['count']} 次")
    
    return filename, mutexes, analysis

if __name__ == "__main__":
    detect_qingtalk_mutexes()
