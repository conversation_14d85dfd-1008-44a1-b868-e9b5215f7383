#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度设备信息分析工具
分析QingTalk可能使用的所有设备检测方法
"""

import os
import subprocess
import winreg
import platform
import socket

def analyze_all_device_info():
    """分析所有可能的设备信息来源"""
    
    print("=" * 60)
    print("深度设备信息分析")
    print("=" * 60)
    
    device_info = {}
    
    # 1. 环境变量
    print("\n📋 1. 环境变量:")
    env_vars = ['COMPUTERNAME', 'USERNAME', 'USERDOMAIN', 'PROCESSOR_IDENTIFIER', 
                'PROCESSOR_ARCHITECTURE', 'NUMBER_OF_PROCESSORS']
    for var in env_vars:
        value = os.environ.get(var, 'Not Found')
        device_info[f'env_{var}'] = value
        print(f"  {var}: {value}")
    
    # 2. Python platform模块
    print("\n🐍 2. Python Platform模块:")
    try:
        platform_info = {
            'node': platform.node(),
            'system': platform.system(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'platform': platform.platform(),
            'version': platform.version(),
        }
        
        for key, value in platform_info.items():
            device_info[f'platform_{key}'] = value
            print(f"  {key}(): {value}")
            
    except Exception as e:
        print(f"  获取失败: {e}")
    
    # 3. 注册表信息
    print("\n📝 3. 注册表信息:")
    registry_keys = [
        (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
        (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0", "ProcessorNameString"),
        (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemManufacturer"),
        (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemProductName"),
        (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "BIOSVendor"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "ProductName"),
    ]
    
    for hkey, subkey, value_name in registry_keys:
        try:
            with winreg.OpenKey(hkey, subkey) as key:
                value, _ = winreg.QueryValueEx(key, value_name)
                device_info[f'registry_{value_name}'] = value
                print(f"  {subkey}\\{value_name}: {value}")
        except Exception as e:
            print(f"  {subkey}\\{value_name}: 读取失败 - {e}")
    
    # 4. 网络信息
    print("\n🌐 4. 网络信息:")
    try:
        hostname = socket.gethostname()
        device_info['network_hostname'] = hostname
        print(f"  socket.gethostname(): {hostname}")
        
        fqdn = socket.getfqdn()
        device_info['network_fqdn'] = fqdn
        print(f"  socket.getfqdn(): {fqdn}")
        
    except Exception as e:
        print(f"  网络信息获取失败: {e}")
    
    # 5. 命令行工具
    print("\n🪟 5. 命令行工具:")
    commands = [
        ('hostname', ['hostname']),
        ('whoami', ['whoami']),
        ('wmic computersystem', ['wmic', 'computersystem', 'get', 'name', '/value']),
        ('wmic bios', ['wmic', 'bios', 'get', 'serialnumber', '/value']),
        ('wmic baseboard', ['wmic', 'baseboard', 'get', 'serialnumber', '/value']),
    ]
    
    for name, cmd in commands:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                output = result.stdout.strip()
                device_info[f'cmd_{name}'] = output
                print(f"  {name}: {output[:100]}...")  # 限制输出长度
            else:
                print(f"  {name}: 执行失败")
        except Exception as e:
            print(f"  {name}: 异常 - {e}")
    
    return device_info

def test_wmi_info():
    """测试WMI信息获取"""
    print("\n🔍 6. WMI信息 (如果可用):")
    
    try:
        import wmi
        c = wmi.WMI()
        
        # 计算机系统信息
        print("  计算机系统:")
        for computer in c.Win32_ComputerSystem():
            print(f"    名称: {computer.Name}")
            print(f"    制造商: {computer.Manufacturer}")
            print(f"    型号: {computer.Model}")
            if hasattr(computer, 'UserName') and computer.UserName:
                print(f"    用户: {computer.UserName}")
            break
        
        # 主板信息
        print("  主板信息:")
        for board in c.Win32_BaseBoard():
            print(f"    制造商: {board.Manufacturer}")
            print(f"    产品: {board.Product}")
            print(f"    序列号: {board.SerialNumber}")
            break
        
        # BIOS信息
        print("  BIOS信息:")
        for bios in c.Win32_BIOS():
            print(f"    制造商: {bios.Manufacturer}")
            print(f"    版本: {bios.Version}")
            print(f"    序列号: {bios.SerialNumber}")
            break
        
        # 处理器信息
        print("  处理器信息:")
        for processor in c.Win32_Processor():
            print(f"    名称: {processor.Name}")
            print(f"    制造商: {processor.Manufacturer}")
            print(f"    处理器ID: {processor.ProcessorId}")
            break
            
    except ImportError:
        print("  WMI模块未安装")
        print("  安装命令: pip install pywin32")
    except Exception as e:
        print(f"  WMI获取失败: {e}")

def compare_before_after_change():
    """比较修改前后的设备信息"""
    
    print("\n" + "=" * 60)
    print("设备信息变化对比测试")
    print("=" * 60)
    
    print("📋 获取修改前的设备信息...")
    before_info = analyze_all_device_info()
    
    print(f"\n⏳ 现在请:")
    print(f"  1. 启动QingTalk")
    print(f"  2. 查看显示的设备信息")
    print(f"  3. 记录下来")
    
    input("\n按回车键继续...")
    
    print("\n📋 获取修改后的设备信息...")
    after_info = analyze_all_device_info()
    
    print(f"\n🔍 对比变化:")
    changes_found = False
    
    for key in before_info:
        if key in after_info:
            if before_info[key] != after_info[key]:
                print(f"  ✅ {key}:")
                print(f"    修改前: {before_info[key]}")
                print(f"    修改后: {after_info[key]}")
                changes_found = True
    
    if not changes_found:
        print(f"  ❌ 没有检测到任何变化")
        print(f"  💡 这说明QingTalk可能使用了我们未检测到的方法")

def create_device_info_report():
    """创建设备信息报告"""
    
    print("\n📝 创建设备信息报告...")
    
    device_info = analyze_all_device_info()
    test_wmi_info()
    
    # 保存到文件
    with open('device_info_report.txt', 'w', encoding='utf-8') as f:
        f.write("QingTalk设备信息分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("检测到的设备信息:\n")
        for key, value in device_info.items():
            f.write(f"{key}: {value}\n")
        
        f.write(f"\n分析结论:\n")
        f.write(f"1. QingTalk显示的设备信息: PC-20250528RNJH\n")
        f.write(f"2. 环境变量COMPUTERNAME: {device_info.get('env_COMPUTERNAME', 'Unknown')}\n")
        f.write(f"3. 注册表ComputerName: {device_info.get('registry_ComputerName', 'Unknown')}\n")
        f.write(f"4. Platform.node(): {device_info.get('platform_node', 'Unknown')}\n")
        f.write(f"5. Socket.gethostname(): {device_info.get('network_hostname', 'Unknown')}\n")
        
        f.write(f"\n建议:\n")
        f.write(f"- QingTalk可能使用WMI或BIOS信息\n")
        f.write(f"- 需要更深层的虚拟化方案\n")
        f.write(f"- 当前数据隔离方案仍然有效\n")
    
    print(f"✅ 报告已保存到: device_info_report.txt")

def main():
    print("QingTalk深度设备信息分析工具")
    
    print(f"\n选择操作:")
    print(f"  1. 分析所有设备信息")
    print(f"  2. 测试WMI信息")
    print(f"  3. 创建设备信息报告")
    print(f"  4. 比较修改前后变化")
    print(f"  5. 退出")
    
    try:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            analyze_all_device_info()
        elif choice == '2':
            test_wmi_info()
        elif choice == '3':
            create_device_info_report()
        elif choice == '4':
            compare_before_after_change()
        elif choice == '5':
            print("退出")
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
