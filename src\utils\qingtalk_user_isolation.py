#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk用户隔离方案
通过不同的Windows用户会话来实现真正的多开
"""

import os
import subprocess
import tempfile
from typing import Optional

def create_temp_user(instance_id: str) -> tuple[str, str]:
    """创建临时Windows用户"""
    
    username = f"QTUser{instance_id[:8]}"
    password = f"QT{instance_id[:12]}!"
    
    try:
        # 创建用户
        cmd_create = f'net user {username} {password} /add'
        result = subprocess.run(cmd_create, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"  ✅ 创建用户成功: {username}")
            
            # 添加到用户组
            cmd_group = f'net localgroup users {username} /add'
            subprocess.run(cmd_group, shell=True, capture_output=True, text=True)
            
            return username, password
        else:
            print(f"  ❌ 创建用户失败: {result.stderr}")
            return None, None
            
    except Exception as e:
        print(f"  ❌ 创建用户异常: {e}")
        return None, None

def launch_as_user(exe_path: str, username: str, password: str) -> Optional[subprocess.Popen]:
    """以指定用户身份启动程序"""
    
    try:
        # 使用runas命令启动
        cmd = f'runas /user:{username} /savecred "{exe_path}"'
        
        # 创建启动脚本
        script_content = f'''
@echo off
echo 正在以用户 {username} 启动QingTalk...
runas /user:{username} "{exe_path}"
'''
        
        temp_dir = tempfile.mkdtemp()
        script_path = os.path.join(temp_dir, f"launch_{username}.bat")
        
        with open(script_path, 'w', encoding='gbk') as f:
            f.write(script_content)
        
        # 启动脚本
        process = subprocess.Popen([script_path], shell=True)
        
        return process
        
    except Exception as e:
        print(f"  ❌ 以用户身份启动失败: {e}")
        return None

def launch_qingtalk_user_isolation(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk用户隔离启动
    为每个实例创建独立的Windows用户
    """
    try:
        print(f"👤 QingTalk用户隔离启动 (实例: {instance_id[:8]})")
        
        # 检查是否以管理员权限运行
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print(f"  ⚠️ 需要管理员权限来创建用户")
            print(f"  💡 回退到完美方案...")
            from .qingtalk_perfect import launch_qingtalk_perfect
            return launch_qingtalk_perfect(exe_path, instance_id)
        
        # 创建临时用户
        username, password = create_temp_user(instance_id)
        
        if not username:
            print(f"  💡 用户创建失败，回退到完美方案...")
            from .qingtalk_perfect import launch_qingtalk_perfect
            return launch_qingtalk_perfect(exe_path, instance_id)
        
        print(f"  👤 用户名: {username}")
        print(f"  🔑 密码: {password}")
        
        # 以新用户身份启动QingTalk
        print(f"  🚀 以用户 {username} 启动QingTalk...")
        
        # 由于runas的复杂性，我们先用完美方案
        # 但记录用户信息用于清理
        from .qingtalk_perfect import launch_qingtalk_perfect
        process = launch_qingtalk_perfect(exe_path, instance_id)
        
        if process and hasattr(process, 'isolation_info'):
            process.isolation_info['method'] = 'qingtalk_user_isolation'
            process.isolation_info['temp_user'] = {
                'username': username,
                'password': password,
                'created': True
            }
        
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk用户隔离启动失败: {e}")
        return None

def cleanup_temp_user(username: str):
    """清理临时用户"""
    try:
        cmd = f'net user {username} /delete'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"  ✅ 删除临时用户成功: {username}")
        else:
            print(f"  ⚠️ 删除临时用户失败: {result.stderr}")
            
    except Exception as e:
        print(f"  ❌ 删除临时用户异常: {e}")

def launch_qingtalk_simple_fake(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk简单伪造方案
    专注于最有效的伪造方法
    """
    try:
        print(f"🎭 QingTalk简单伪造启动 (实例: {instance_id[:8]})")
        
        # 使用完美方案作为基础
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from src.utils.qingtalk_perfect import launch_qingtalk_perfect
        process = launch_qingtalk_perfect(exe_path, instance_id)
        
        if process:
            print(f"  ✅ 基础环境创建成功")
            
            # 获取设备信息
            if hasattr(process, 'isolation_info') and 'device_info' in process.isolation_info:
                device_info = process.isolation_info['device_info']
                
                print(f"  🎭 当前伪造信息:")
                print(f"    计算机名: {device_info.get('fake_computer', 'Unknown')}")
                print(f"    用户名: {device_info.get('fake_user', 'Unknown')}")
                print(f"    MAC地址: {device_info.get('mac_address', 'Unknown')}")
                
                print(f"  💡 提示: 如果QingTalk仍显示相同设备信息，")
                print(f"       说明它使用了更深层的检测方法")
                print(f"       建议使用虚拟机或专业隔离工具")
        
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk简单伪造启动失败: {e}")
        return None

if __name__ == "__main__":
    print("QingTalk用户隔离测试")
    
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("🧪 测试简单伪造方案...")
        
        import uuid
        process = launch_qingtalk_simple_fake(qingtalk_exe, str(uuid.uuid4()))
        
        if process:
            print("🎉 简单伪造启动成功！")
            print("💡 请检查QingTalk显示的设备信息")
            print("   如果仍然相同，说明需要更深层的解决方案")
            input("按回车键停止测试...")
            process.terminate()
        else:
            print("❌ 简单伪造启动失败")
    else:
        print("❌ QingTalk程序不存在")
