#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守的启动策略
使用最小的修改来启动QingTalk
"""

import os
import subprocess
import tempfile
import shutil
from typing import Optional

def launch_qingtalk_conservative(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    使用保守策略启动QingTalk
    只修改最必要的环境变量
    """
    try:
        print(f"开始保守启动: {os.path.basename(exe_path)}")
        
        # 创建最小的用户数据目录
        user_data_dir = tempfile.mkdtemp(prefix=f"qingtalk_conservative_{instance_id[:8]}_")
        
        # 创建基本目录结构
        appdata_dir = os.path.join(user_data_dir, 'QingTalk')
        os.makedirs(appdata_dir, exist_ok=True)
        
        # 设置最小的环境变量修改
        env = os.environ.copy()
        
        # 只修改QingTalk相关的路径
        original_appdata = env.get('APPDATA', '')
        qingtalk_appdata = os.path.join(original_appdata, f'QingTalk_{instance_id[:8]}')
        
        # 创建QingTalk专用目录
        os.makedirs(qingtalk_appdata, exist_ok=True)
        
        # 最小环境变量修改
        env.update({
            'QINGTALK_USER_DATA': qingtalk_appdata,
            'INSTANCE_ID': instance_id,
            'QINGTALK_INSTANCE': instance_id[:8]
        })
        
        print(f"QingTalk数据目录: {qingtalk_appdata}")
        
        # 使用最简单的启动方式
        process = subprocess.Popen(
            exe_path,
            env=env,
            cwd=os.path.dirname(exe_path),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        # 保存清理信息
        process.cleanup_dirs = [user_data_dir, qingtalk_appdata]
        
        print(f"保守启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"保守启动失败: {e}")
        return None

def launch_qingtalk_minimal_args(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    使用最少参数启动QingTalk
    """
    try:
        print(f"开始最少参数启动: {os.path.basename(exe_path)}")
        
        # 创建用户数据目录
        user_data_dir = tempfile.mkdtemp(prefix=f"qingtalk_minimal_{instance_id[:8]}_")
        
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'INSTANCE_ID': instance_id,
            'QINGTALK_INSTANCE': instance_id[:8]
        })
        
        # 只使用一个关键参数
        command = [
            exe_path,
            f'--user-data-dir={user_data_dir}'
        ]
        
        print(f"启动命令: {' '.join(command)}")
        
        process = subprocess.Popen(
            command,
            env=env,
            cwd=os.path.dirname(exe_path),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        process.cleanup_dirs = [user_data_dir]
        
        print(f"最少参数启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"最少参数启动失败: {e}")
        return None

def launch_qingtalk_no_args(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    不使用任何参数启动QingTalk
    只修改环境变量
    """
    try:
        print(f"开始无参数启动: {os.path.basename(exe_path)}")
        
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'INSTANCE_ID': instance_id,
            'QINGTALK_INSTANCE': instance_id[:8],
            'PROCESS_UNIQUE_ID': instance_id
        })
        
        process = subprocess.Popen(
            exe_path,
            env=env,
            cwd=os.path.dirname(exe_path),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        process.cleanup_dirs = []
        
        print(f"无参数启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"无参数启动失败: {e}")
        return None

def cleanup_conservative(process: subprocess.Popen):
    """
    清理保守启动的资源
    """
    try:
        if hasattr(process, 'cleanup_dirs'):
            for dir_path in process.cleanup_dirs:
                if os.path.exists(dir_path):
                    print(f"清理目录: {dir_path}")
                    shutil.rmtree(dir_path, ignore_errors=True)
    except Exception as e:
        print(f"清理失败: {e}")

def test_all_strategies(exe_path: str) -> dict:
    """
    测试所有启动策略
    """
    strategies = {
        'conservative': launch_qingtalk_conservative,
        'minimal_args': launch_qingtalk_minimal_args,
        'no_args': launch_qingtalk_no_args
    }
    
    results = {}
    
    for name, strategy in strategies.items():
        print(f"\n{'='*30}")
        print(f"测试策略: {name}")
        print(f"{'='*30}")
        
        try:
            process = strategy(exe_path, f"test-{name}")
            
            if process:
                # 等待5秒检查状态
                import time
                time.sleep(5)
                
                if process.poll() is None:
                    print(f"✅ 策略 {name} 成功 - 进程仍在运行")
                    results[name] = True
                    
                    # 停止进程
                    process.terminate()
                    process.wait(timeout=5)
                else:
                    print(f"❌ 策略 {name} 失败 - 进程已停止")
                    results[name] = False
                
                cleanup_conservative(process)
            else:
                print(f"❌ 策略 {name} 失败 - 无法启动")
                results[name] = False
                
        except Exception as e:
            print(f"❌ 策略 {name} 异常: {e}")
            results[name] = False
    
    return results
