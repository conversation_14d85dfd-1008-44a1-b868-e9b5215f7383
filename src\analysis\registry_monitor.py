#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册表监控工具
监控QingTalk对注册表的访问
"""

import winreg
import os
import json
from datetime import datetime
from typing import Dict, List

class RegistryMonitor:
    def __init__(self):
        self.qingtalk_keys = []
        self.scan_results = {}
    
    def scan_registry_for_qingtalk(self) -> Dict:
        """扫描注册表中与QingTalk相关的项"""
        print("扫描注册表中的QingTalk相关项...")
        
        # 要扫描的注册表根键
        root_keys = [
            (winreg.HKEY_CURRENT_USER, "HKEY_CURRENT_USER"),
            (winreg.HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE"),
            (winreg.HKEY_USERS, "HKEY_USERS")
        ]
        
        results = {}
        
        for root_key, root_name in root_keys:
            print(f"扫描 {root_name}...")
            results[root_name] = self._scan_key_recursive(root_key, "", max_depth=3)
        
        self.scan_results = results
        return results
    
    def _scan_key_recursive(self, key, path: str, max_depth: int = 3, current_depth: int = 0) -> List[Dict]:
        """递归扫描注册表键"""
        if current_depth >= max_depth:
            return []
        
        found_items = []
        
        try:
            # 扫描子键
            i = 0
            while True:
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    if any(keyword.lower() in subkey_name.lower() 
                          for keyword in ['qingtalk', 'dingtalk', 'taobao', 'alibaba']):
                        
                        subkey_path = f"{path}\\{subkey_name}" if path else subkey_name
                        
                        try:
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                item_info = {
                                    'type': 'key',
                                    'path': subkey_path,
                                    'name': subkey_name,
                                    'values': self._get_key_values(subkey),
                                    'subkeys': []
                                }
                                
                                # 递归扫描子键
                                if current_depth < max_depth - 1:
                                    item_info['subkeys'] = self._scan_key_recursive(
                                        subkey, subkey_path, max_depth, current_depth + 1
                                    )
                                
                                found_items.append(item_info)
                        except PermissionError:
                            found_items.append({
                                'type': 'key',
                                'path': subkey_path,
                                'name': subkey_name,
                                'error': 'Access Denied'
                            })
                    
                    i += 1
                except OSError:
                    break
            
            # 扫描值
            i = 0
            while True:
                try:
                    value_name, value_data, value_type = winreg.EnumValue(key, i)
                    if any(keyword.lower() in str(value_name).lower() or 
                          keyword.lower() in str(value_data).lower()
                          for keyword in ['qingtalk', 'dingtalk', 'taobao', 'alibaba']):
                        
                        found_items.append({
                            'type': 'value',
                            'path': path,
                            'name': value_name,
                            'data': str(value_data),
                            'reg_type': value_type
                        })
                    
                    i += 1
                except OSError:
                    break
                    
        except PermissionError:
            pass
        except Exception as e:
            print(f"扫描错误 {path}: {e}")
        
        return found_items
    
    def _get_key_values(self, key) -> List[Dict]:
        """获取注册表键的所有值"""
        values = []
        i = 0
        while True:
            try:
                value_name, value_data, value_type = winreg.EnumValue(key, i)
                values.append({
                    'name': value_name,
                    'data': str(value_data),
                    'type': value_type
                })
                i += 1
            except OSError:
                break
        return values
    
    def check_common_single_instance_keys(self) -> Dict:
        """检查常见的单实例检测注册表键"""
        print("检查常见的单实例检测键...")
        
        common_paths = [
            (winreg.HKEY_CURRENT_USER, "Software\\QingTalk"),
            (winreg.HKEY_CURRENT_USER, "Software\\DingTalk"),
            (winreg.HKEY_CURRENT_USER, "Software\\Taobao"),
            (winreg.HKEY_CURRENT_USER, "Software\\Alibaba"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\QingTalk"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\DingTalk"),
            (winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Run"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"),
        ]
        
        results = {}
        
        for root_key, path in common_paths:
            try:
                with winreg.OpenKey(root_key, path) as key:
                    values = self._get_key_values(key)
                    if values:
                        results[path] = values
                        print(f"找到键: {path}")
            except FileNotFoundError:
                pass
            except PermissionError:
                results[path] = {'error': 'Access Denied'}
        
        return results
    
    def save_results(self, filename: str = None) -> str:
        """保存扫描结果"""
        if not filename:
            filename = f"registry_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
        
        print(f"注册表扫描结果已保存到: {filename}")
        return filename

def scan_qingtalk_registry():
    """扫描QingTalk相关的注册表项"""
    print("=" * 50)
    print("QingTalk注册表扫描")
    print("=" * 50)
    
    monitor = RegistryMonitor()
    
    # 扫描注册表
    results = monitor.scan_registry_for_qingtalk()
    
    # 检查常见键
    common_keys = monitor.check_common_single_instance_keys()
    
    # 保存结果
    filename = monitor.save_results()
    
    # 显示摘要
    print("\n" + "=" * 50)
    print("扫描结果摘要")
    print("=" * 50)
    
    total_items = 0
    for root_name, items in results.items():
        count = len(items) if isinstance(items, list) else 0
        total_items += count
        if count > 0:
            print(f"{root_name}: {count} 个相关项")
    
    print(f"\n总共找到 {total_items} 个QingTalk相关的注册表项")
    
    if common_keys:
        print(f"\n常见检测键:")
        for path, values in common_keys.items():
            if isinstance(values, dict) and 'error' in values:
                print(f"  {path}: {values['error']}")
            else:
                print(f"  {path}: {len(values)} 个值")
    
    return filename, results

if __name__ == "__main__":
    scan_qingtalk_registry()
