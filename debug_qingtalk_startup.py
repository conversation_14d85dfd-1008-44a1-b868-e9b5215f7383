#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试QingTalk启动问题
"""

import os
import subprocess
import time
import sys

def test_qingtalk_direct():
    """直接启动QingTalk测试"""
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk不存在: {qingtalk_exe}")
        return False
    
    print("🧪 测试1: 直接启动QingTalk")
    print("-" * 40)
    
    try:
        # 直接启动，不重定向输出
        process = subprocess.Popen(
            qingtalk_exe,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            if process.poll() is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {process.returncode})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_qingtalk_with_pipes():
    """使用管道重定向启动QingTalk测试"""
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk不存在: {qingtalk_exe}")
        return False
    
    print("\n🧪 测试2: 使用管道重定向启动QingTalk")
    print("-" * 40)
    
    try:
        # 使用管道重定向
        process = subprocess.Popen(
            qingtalk_exe,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            if process.poll() is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {process.returncode})")
                
                # 读取输出
                try:
                    stdout, stderr = process.communicate(timeout=1)
                    if stdout:
                        print(f"标准输出: {stdout.decode('utf-8', errors='ignore')[:200]}")
                    if stderr:
                        print(f"错误输出: {stderr.decode('utf-8', errors='ignore')[:200]}")
                except:
                    pass
                
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_qingtalk_with_devnull():
    """使用DEVNULL重定向启动QingTalk测试"""
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk不存在: {qingtalk_exe}")
        return False
    
    print("\n🧪 测试3: 使用DEVNULL重定向启动QingTalk")
    print("-" * 40)
    
    try:
        # 使用DEVNULL重定向
        process = subprocess.Popen(
            qingtalk_exe,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ QingTalk启动成功 (PID: {process.pid})")
        
        # 检查10秒
        for i in range(10):
            time.sleep(1)
            if process.poll() is not None:
                print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {process.returncode})")
                return False
            print(f"  {i+1}秒: 进程运行中")
        
        print("✅ QingTalk运行10秒成功")
        
        # 停止进程
        process.terminate()
        process.wait(timeout=5)
        print("✅ QingTalk已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_existing_instance():
    """测试现有实例启动"""
    print("\n🧪 测试4: 测试现有实例启动")
    print("-" * 40)
    
    try:
        # 导入现有的启动函数
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from src.utils.qingtalk_optimized import launch_qingtalk_optimized
        
        qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
        
        if not os.path.exists(qingtalk_exe):
            print(f"❌ QingTalk不存在: {qingtalk_exe}")
            return False
        
        # 使用现有的启动函数
        process = launch_qingtalk_optimized(qingtalk_exe, "debug_test")
        
        if process:
            print(f"✅ QingTalk启动成功 (PID: {process.pid})")
            
            # 检查10秒
            for i in range(10):
                time.sleep(1)
                if process.poll() is not None:
                    print(f"❌ QingTalk在 {i+1} 秒后退出 (返回码: {process.returncode})")
                    return False
                print(f"  {i+1}秒: 进程运行中")
            
            print("✅ QingTalk运行10秒成功")
            
            # 停止进程
            process.terminate()
            process.wait(timeout=5)
            print("✅ QingTalk已停止")
            
            return True
        else:
            print("❌ QingTalk启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("QingTalk启动问题调试")
    print("=" * 60)
    
    tests = [
        ("直接启动", test_qingtalk_direct),
        ("管道重定向启动", test_qingtalk_with_pipes),
        ("DEVNULL重定向启动", test_qingtalk_with_devnull),
        ("现有实例启动", test_existing_instance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count < total_count:
        print("\n💡 建议:")
        if not results.get("直接启动", False):
            print("- QingTalk本身可能有问题，检查QingTalk是否能正常启动")
        elif not results.get("管道重定向启动", False):
            print("- 管道重定向导致问题，避免使用stdout=PIPE, stderr=PIPE")
        elif results.get("DEVNULL重定向启动", False):
            print("- 使用DEVNULL重定向可以解决问题")
        
        if not results.get("现有实例启动", False):
            print("- 现有启动函数有问题，需要修复")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
