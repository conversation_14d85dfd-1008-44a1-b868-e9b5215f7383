#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析QingTalk窗口的登录状态
"""

import psutil
import win32gui
import win32process
import win32con
import win32api
import time
from ctypes import windll, byref, c_int, c_uint, c_void_p, c_bool
from ctypes.wintypes import RECT, HWND

def get_window_content_info(hwnd):
    """获取窗口内容信息"""
    try:
        # 获取窗口类名
        class_name = win32gui.GetClassName(hwnd)
        
        # 获取窗口文本
        window_text = win32gui.GetWindowText(hwnd)
        
        # 获取窗口状态
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_enabled = win32gui.IsWindowEnabled(hwnd)
        
        # 获取窗口位置和大小
        try:
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
        except:
            width = height = 0
        
        # 尝试获取子窗口数量（可能反映内容复杂度）
        child_windows = []
        def enum_child_proc(child_hwnd, param):
            child_class = win32gui.GetClassName(child_hwnd)
            child_text = win32gui.GetWindowText(child_hwnd)
            if child_class or child_text:
                child_windows.append({
                    'class': child_class,
                    'text': child_text,
                    'visible': win32gui.IsWindowVisible(child_hwnd)
                })
            return True
        
        try:
            win32gui.EnumChildWindows(hwnd, enum_child_proc, None)
        except:
            pass
        
        return {
            'class_name': class_name,
            'window_text': window_text,
            'is_visible': is_visible,
            'is_enabled': is_enabled,
            'width': width,
            'height': height,
            'child_count': len(child_windows),
            'child_windows': child_windows[:5]  # 只保留前5个子窗口
        }
    
    except Exception as e:
        return {
            'error': str(e),
            'class_name': '',
            'window_text': '',
            'is_visible': False,
            'is_enabled': False,
            'width': 0,
            'height': 0,
            'child_count': 0,
            'child_windows': []
        }

def analyze_qingtalk_windows():
    """分析QingTalk窗口状态"""
    print("🔍 分析QingTalk窗口登录状态...")
    
    # 查找QingTalk进程
    qingtalk_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'create_time']):
        try:
            if proc.info['name'] and 'qingtalk' in proc.info['name'].lower():
                qingtalk_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if not qingtalk_processes:
        print("❌ 没有找到QingTalk进程")
        return
    
    print(f"📊 找到 {len(qingtalk_processes)} 个QingTalk进程:")
    qingtalk_pids = []
    for proc in qingtalk_processes:
        # 计算进程运行时间
        create_time = proc['create_time']
        current_time = time.time()
        runtime = current_time - create_time
        
        print(f"  PID: {proc['pid']} - {proc['name']} (运行时间: {runtime:.1f}秒)")
        qingtalk_pids.append(proc['pid'])
    
    # 查找QingTalk窗口
    qingtalk_windows = []
    
    def enum_windows_callback(hwnd, data):
        try:
            window_text = win32gui.GetWindowText(hwnd)
            _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
            
            if window_pid in qingtalk_pids and window_text == 'QingTalk':
                content_info = get_window_content_info(hwnd)
                content_info['hwnd'] = hwnd
                content_info['pid'] = window_pid
                qingtalk_windows.append(content_info)
        
        except Exception:
            pass
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    
    if not qingtalk_windows:
        print("❌ 没有找到QingTalk窗口")
        return
    
    print(f"\n📋 详细分析 {len(qingtalk_windows)} 个QingTalk窗口:")
    print("=" * 100)
    
    # 按大小排序
    qingtalk_windows.sort(key=lambda w: w['width'] * w['height'], reverse=True)
    
    for i, window in enumerate(qingtalk_windows):
        print(f"\n🪟 窗口 {i+1}:")
        print(f"  PID: {window['pid']}")
        print(f"  大小: {window['width']}x{window['height']}")
        print(f"  可见: {'✅' if window['is_visible'] else '❌'}")
        print(f"  启用: {'✅' if window['is_enabled'] else '❌'}")
        print(f"  类名: {window['class_name']}")
        print(f"  子窗口数量: {window['child_count']}")
        
        # 分析窗口状态
        area = window['width'] * window['height']
        print(f"  窗口面积: {area:,}")
        
        # 判断窗口类型
        window_type = "未知"
        confidence = 0
        reasons = []
        
        # 大小判断
        if window['width'] > 1000 or window['height'] > 800:
            window_type = "超大窗口"
            reasons.append("尺寸异常大")
            confidence = 10
        elif 600 <= window['width'] <= 1000 and 600 <= window['height'] <= 800:
            window_type = "主界面候选"
            reasons.append("主界面尺寸")
            confidence = 60
        elif 300 <= window['width'] <= 600 and 400 <= window['height'] <= 800:
            window_type = "登录界面候选"
            reasons.append("登录界面尺寸")
            confidence = 50
        else:
            window_type = "小窗口"
            reasons.append("尺寸较小")
            confidence = 20
        
        # 子窗口数量判断
        if window['child_count'] > 10:
            reasons.append("子窗口多(内容丰富)")
            confidence += 30
        elif window['child_count'] > 5:
            reasons.append("子窗口中等")
            confidence += 15
        elif window['child_count'] > 0:
            reasons.append("子窗口少")
            confidence += 5
        else:
            reasons.append("无子窗口(可能白屏)")
            confidence -= 20
        
        # 可见性判断
        if window['is_visible']:
            reasons.append("当前可见")
            confidence += 20
        else:
            reasons.append("当前隐藏")
            confidence -= 10
        
        # 启用状态判断
        if window['is_enabled']:
            reasons.append("窗口启用")
            confidence += 10
        else:
            reasons.append("窗口禁用")
            confidence -= 15
        
        print(f"  🎯 窗口类型: {window_type}")
        print(f"  📊 置信度: {confidence}%")
        print(f"  📝 判断依据: {', '.join(reasons)}")
        
        # 显示部分子窗口信息
        if window['child_windows']:
            print(f"  🔍 子窗口示例:")
            for j, child in enumerate(window['child_windows'][:3]):
                print(f"    {j+1}. {child['class']} - {child['text'][:30]} - 可见:{child['visible']}")
        
        print("-" * 80)
    
    # 推荐最佳窗口
    print(f"\n🎯 窗口选择建议:")
    
    # 计算每个窗口的综合得分
    for window in qingtalk_windows:
        score = 0
        
        # 大小得分
        if 600 <= window['width'] <= 1000 and 600 <= window['height'] <= 800:
            score += 40  # 主界面尺寸
        elif 300 <= window['width'] <= 600 and 400 <= window['height'] <= 800:
            score += 30  # 登录界面尺寸
        elif window['width'] <= 1000 and window['height'] <= 800:
            score += 20  # 合理尺寸
        else:
            score -= 20  # 异常尺寸
        
        # 内容得分
        if window['child_count'] > 10:
            score += 30  # 内容丰富
        elif window['child_count'] > 5:
            score += 20
        elif window['child_count'] > 0:
            score += 10
        else:
            score -= 10  # 可能是白屏
        
        # 状态得分
        if window['is_visible']:
            score += 20
        if window['is_enabled']:
            score += 10
        
        window['score'] = score
    
    # 按得分排序
    qingtalk_windows.sort(key=lambda w: w['score'], reverse=True)
    
    print("📊 窗口得分排名:")
    for i, window in enumerate(qingtalk_windows):
        print(f"  {i+1}. PID {window['pid']}, "
              f"大小 {window['width']}x{window['height']}, "
              f"得分 {window['score']}, "
              f"子窗口 {window['child_count']}")
    
    if qingtalk_windows:
        best_window = qingtalk_windows[0]
        print(f"\n✅ 推荐选择:")
        print(f"  PID: {best_window['pid']}")
        print(f"  大小: {best_window['width']}x{best_window['height']}")
        print(f"  得分: {best_window['score']}")
        print(f"  子窗口: {best_window['child_count']}")
        
        if best_window['child_count'] > 5:
            print(f"  💡 这个窗口有较多子窗口，可能是已登录的主界面")
        elif best_window['child_count'] == 0:
            print(f"  ⚠️ 这个窗口没有子窗口，可能是白屏或加载中")
        else:
            print(f"  🤔 这个窗口子窗口较少，可能是登录界面")

def main():
    print("=" * 100)
    print("QingTalk窗口登录状态分析")
    print("=" * 100)
    print("💡 分析窗口内容复杂度来判断登录状态")
    print("💡 子窗口多的通常是已登录的主界面")
    print("💡 子窗口少或无的可能是登录界面或白屏")
    print()
    
    analyze_qingtalk_windows()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
