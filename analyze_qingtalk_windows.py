#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析QingTalk窗口结构，找出真正的主窗口
"""

import psutil
import win32gui
import win32process
import time

def analyze_qingtalk_windows():
    """分析QingTalk窗口结构"""
    print("🔍 深度分析QingTalk窗口结构...")
    
    # 查找QingTalk进程
    qingtalk_processes = []
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if proc.info['name'] and 'qingtalk' in proc.info['name'].lower():
                qingtalk_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    print(f"📊 找到 {len(qingtalk_processes)} 个QingTalk进程:")
    qingtalk_pids = []
    for proc in qingtalk_processes:
        print(f"  PID: {proc['pid']} - {proc['name']}")
        qingtalk_pids.append(proc['pid'])
    
    if not qingtalk_processes:
        print("❌ 没有QingTalk进程")
        return
    
    # 收集所有QingTalk窗口的详细信息
    qingtalk_windows = []
    
    def enum_windows_callback(hwnd, data):
        try:
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            is_visible = win32gui.IsWindowVisible(hwnd)
            _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
            
            if window_pid in qingtalk_pids:
                try:
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                    
                    # 获取窗口状态
                    is_minimized = win32gui.IsIconic(hwnd)
                    is_maximized = win32gui.IsZoomed(hwnd)
                    
                    # 尝试获取窗口的父窗口
                    parent_hwnd = win32gui.GetParent(hwnd)
                    
                    # 获取窗口在Z轴的位置（层级）
                    try:
                        window_long = win32gui.GetWindowLong(hwnd, -16)  # GWL_STYLE
                        has_caption = bool(window_long & 0x00C00000)  # WS_CAPTION
                        has_sysmenu = bool(window_long & 0x00080000)  # WS_SYSMENU
                    except:
                        has_caption = False
                        has_sysmenu = False
                    
                except Exception as e:
                    width = height = 0
                    is_minimized = is_maximized = False
                    parent_hwnd = 0
                    has_caption = has_sysmenu = False
                
                window_info = {
                    'hwnd': hwnd,
                    'pid': window_pid,
                    'title': window_text,
                    'class': class_name,
                    'visible': is_visible,
                    'width': width,
                    'height': height,
                    'minimized': is_minimized,
                    'maximized': is_maximized,
                    'parent': parent_hwnd,
                    'has_caption': has_caption,
                    'has_sysmenu': has_sysmenu,
                    'area': width * height
                }
                qingtalk_windows.append(window_info)
        
        except Exception:
            pass
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    
    print(f"\n📋 详细窗口分析 (共{len(qingtalk_windows)}个):")
    print("=" * 120)
    
    # 按窗口大小排序
    qingtalk_windows.sort(key=lambda w: w['area'], reverse=True)
    
    main_window_candidates = []
    
    for i, window in enumerate(qingtalk_windows):
        print(f"窗口 {i+1}:")
        print(f"  句柄: {window['hwnd']}")
        print(f"  PID: {window['pid']}")
        print(f"  标题: '{window['title']}'")
        print(f"  类名: '{window['class']}'")
        print(f"  大小: {window['width']}x{window['height']} (面积: {window['area']})")
        print(f"  状态: 可见={window['visible']}, 最小化={window['minimized']}, 最大化={window['maximized']}")
        print(f"  窗口特性: 标题栏={window['has_caption']}, 系统菜单={window['has_sysmenu']}")
        print(f"  父窗口: {window['parent']}")
        
        # 判断是否是主窗口候选
        is_main_candidate = False
        candidate_reason = ""
        
        if (window['title'] == 'QingTalk' and 
            window['class'] == 'Chrome_WidgetWin_1' and
            window['width'] > 300 and window['height'] > 300):
            
            if window['visible'] and not window['minimized']:
                is_main_candidate = True
                candidate_reason = "可见的QingTalk主窗口"
            elif not window['visible'] and window['area'] > 200000:  # 大窗口
                is_main_candidate = True
                candidate_reason = "隐藏的大QingTalk窗口"
            elif window['has_caption'] and window['has_sysmenu']:
                is_main_candidate = True
                candidate_reason = "有完整窗口特性的QingTalk窗口"
        
        if is_main_candidate:
            main_window_candidates.append((window, candidate_reason))
            print(f"  🎯 主窗口候选: {candidate_reason}")
        else:
            print(f"  ❌ 非主窗口: 可能是工具窗口或子窗口")
        
        print()
    
    # 分析最佳主窗口
    print("🎯 主窗口候选分析:")
    print("-" * 60)
    
    if not main_window_candidates:
        print("❌ 没有找到主窗口候选")
        return
    
    for i, (window, reason) in enumerate(main_window_candidates):
        print(f"候选 {i+1}: {reason}")
        print(f"  PID: {window['pid']}")
        print(f"  标题: '{window['title']}'")
        print(f"  大小: {window['width']}x{window['height']}")
        print(f"  可见: {window['visible']}")
        print(f"  最小化: {window['minimized']}")
        print()
    
    # 选择最佳窗口的策略
    print("🚀 选择最佳主窗口:")
    
    # 策略1: 优先选择可见且未最小化的最大窗口
    visible_unminimized = [(w, r) for w, r in main_window_candidates 
                          if w['visible'] and not w['minimized']]
    
    if visible_unminimized:
        best_window, reason = max(visible_unminimized, key=lambda x: x[0]['area'])
        print(f"✅ 策略1成功: 选择可见未最小化的最大窗口")
    else:
        # 策略2: 选择最大的窗口
        best_window, reason = max(main_window_candidates, key=lambda x: x[0]['area'])
        print(f"⚠️ 策略2: 选择最大的窗口 (可能需要恢复)")
    
    print(f"  最佳窗口: PID {best_window['pid']}")
    print(f"  标题: '{best_window['title']}'")
    print(f"  大小: {best_window['width']}x{best_window['height']}")
    print(f"  状态: 可见={best_window['visible']}, 最小化={best_window['minimized']}")
    print(f"  选择原因: {reason}")
    
    # 测试激活
    print(f"\n🧪 测试激活最佳窗口...")
    try:
        import win32con
        hwnd = best_window['hwnd']
        
        # 如果窗口被最小化，先恢复
        if best_window['minimized']:
            print(f"  恢复最小化窗口...")
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            time.sleep(0.5)
        
        # 如果窗口不可见，先显示
        if not best_window['visible']:
            print(f"  显示隐藏窗口...")
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            time.sleep(0.5)
        
        # 激活窗口
        print(f"  激活窗口...")
        win32gui.SetForegroundWindow(hwnd)
        
        # 确保窗口在最前面
        win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, 0, 0, 
                            win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        
        print("✅ 窗口激活完成")
        
        # 等待一下，然后检查窗口状态
        time.sleep(1)
        current_foreground = win32gui.GetForegroundWindow()
        if current_foreground == hwnd:
            print("✅ 确认窗口已成为前台窗口")
        else:
            print("⚠️ 窗口可能没有成功成为前台窗口")
        
    except Exception as e:
        print(f"❌ 窗口激活失败: {e}")

def main():
    print("=" * 80)
    print("QingTalk窗口结构深度分析")
    print("=" * 80)
    print("💡 分析所有QingTalk窗口，找出真正的主窗口")
    print("💡 避免激活登录窗口或空白窗口")
    print()
    
    analyze_qingtalk_windows()
    
    print("\n" + "=" * 80)
    print("分析完成")
    print("=" * 80)
    print("💡 请观察激活的窗口是否正确")
    print("💡 如果还有问题，请告诉我具体的窗口行为")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
