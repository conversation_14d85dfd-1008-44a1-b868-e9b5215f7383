#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急恢复脚本
立即恢复所有被修改的设备信息，解决系统错误
"""

import os
import sys
import ctypes
import winreg
import json
import tempfile
import subprocess

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_admin():
    """检查管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def emergency_restore_registry():
    """紧急恢复注册表"""
    print("🚨 紧急恢复注册表...")
    
    # 尝试恢复常见的计算机名注册表项
    restore_keys = [
        (r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
        (r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", "ComputerName"),
        (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Hostname"),
        (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Hostname"),
    ]
    
    # 尝试从备份文件恢复
    backup_file = os.path.join(tempfile.gettempdir(), "device_modifier_backup.json")
    
    if os.path.exists(backup_file):
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            print(f"  📋 找到备份文件: {backup_file}")
            
            # 恢复注册表值
            if 'registry' in backup_data:
                for key_info, original_value in backup_data['registry'].items():
                    try:
                        # 解析键信息
                        parts = key_info.split('|')
                        if len(parts) == 3:
                            hkey_name, subkey, value_name = parts
                            
                            # 转换HKEY
                            if hkey_name == 'HKEY_LOCAL_MACHINE':
                                hkey = winreg.HKEY_LOCAL_MACHINE
                            else:
                                continue
                            
                            # 恢复值
                            with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                                winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, original_value)
                                print(f"    ✅ 恢复: {subkey}\\{value_name} = {original_value}")
                    
                    except Exception as e:
                        print(f"    ⚠️ 恢复失败: {key_info} - {e}")
            
            # 恢复环境变量
            if 'environment' in backup_data:
                for var_name, original_value in backup_data['environment'].items():
                    try:
                        if original_value is not None:
                            os.environ[var_name] = original_value
                        elif var_name in os.environ:
                            del os.environ[var_name]
                        print(f"    ✅ 恢复环境变量: {var_name}")
                    except Exception as e:
                        print(f"    ⚠️ 恢复环境变量失败: {var_name} - {e}")
            
            print(f"  ✅ 从备份文件恢复完成")
            return True
            
        except Exception as e:
            print(f"  ❌ 备份文件恢复失败: {e}")
    
    else:
        print(f"  ⚠️ 未找到备份文件，尝试手动恢复...")
        
        # 手动恢复到常见的默认值
        try:
            # 获取当前的原始计算机名（从环境变量或其他来源）
            original_name = None
            
            # 尝试从多个来源获取原始计算机名
            sources = [
                lambda: subprocess.check_output(['wmic', 'computersystem', 'get', 'name'], text=True).split('\n')[1].strip(),
                lambda: os.environ.get('COMPUTERNAME'),
                lambda: subprocess.check_output(['hostname'], text=True).strip(),
            ]
            
            for source in sources:
                try:
                    name = source()
                    if name and name != 'Name' and len(name) > 0:
                        original_name = name
                        break
                except:
                    continue
            
            if not original_name:
                print(f"    ⚠️ 无法确定原始计算机名，请手动检查")
                return False
            
            print(f"    🎯 检测到可能的原始计算机名: {original_name}")
            
            # 恢复注册表
            for subkey, value_name in restore_keys:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, subkey, 0, winreg.KEY_SET_VALUE) as key:
                        winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, original_name)
                        print(f"    ✅ 恢复: {subkey}\\{value_name} = {original_name}")
                except Exception as e:
                    print(f"    ⚠️ 恢复失败: {subkey}\\{value_name} - {e}")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 手动恢复失败: {e}")
            return False

def emergency_restore_services():
    """紧急重启相关服务"""
    print("🔄 重启相关系统服务...")
    
    services = ['Workstation', 'Server', 'Netlogon']
    
    for service in services:
        try:
            # 重启服务
            subprocess.run(['net', 'stop', service], capture_output=True, timeout=10)
            subprocess.run(['net', 'start', service], capture_output=True, timeout=10)
            print(f"  ✅ 重启服务: {service}")
        except Exception as e:
            print(f"  ⚠️ 重启服务失败: {service} - {e}")

def emergency_flush_cache():
    """紧急刷新系统缓存"""
    print("🧹 刷新系统缓存...")
    
    try:
        # 刷新DNS缓存
        subprocess.run(['ipconfig', '/flushdns'], capture_output=True, timeout=10)
        print(f"  ✅ DNS缓存已刷新")
    except Exception as e:
        print(f"  ⚠️ DNS缓存刷新失败: {e}")
    
    try:
        # 发送系统更改通知
        HWND_BROADCAST = 0xFFFF
        WM_SETTINGCHANGE = 0x001A
        
        ctypes.windll.user32.SendMessageTimeoutW(
            HWND_BROADCAST,
            WM_SETTINGCHANGE,
            0,
            "Environment",
            0x0002,  # SMTO_ABORTIFHUNG
            5000,
            None
        )
        print(f"  ✅ 系统更改通知已发送")
    except Exception as e:
        print(f"  ⚠️ 系统更改通知失败: {e}")

def clean_temp_files():
    """清理临时文件"""
    print("🗑️ 清理临时文件...")
    
    temp_patterns = [
        "qingtalk_*",
        "fake_system_*",
        "device_modifier_*"
    ]
    
    temp_dir = tempfile.gettempdir()
    
    for pattern in temp_patterns:
        try:
            import glob
            files = glob.glob(os.path.join(temp_dir, pattern))
            for file_path in files:
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"  ✅ 删除文件: {file_path}")
                    elif os.path.isdir(file_path):
                        import shutil
                        shutil.rmtree(file_path)
                        print(f"  ✅ 删除目录: {file_path}")
                except Exception as e:
                    print(f"  ⚠️ 删除失败: {file_path} - {e}")
        except Exception as e:
            print(f"  ⚠️ 清理模式失败: {pattern} - {e}")

def main():
    """主函数"""
    print("🚨 紧急恢复脚本")
    print("=" * 50)
    print("此脚本将尝试恢复所有被修改的设备信息")
    print("解决系统错误和网络问题")
    print("=" * 50)
    
    # 检查管理员权限
    if not check_admin():
        print("❌ 需要管理员权限来执行恢复操作")
        print("请以管理员身份重新运行此脚本")
        input("按回车键退出...")
        return
    
    print("✅ 管理员权限确认")
    
    try:
        # 1. 恢复注册表
        registry_success = emergency_restore_registry()
        
        # 2. 重启服务
        emergency_restore_services()
        
        # 3. 刷新缓存
        emergency_flush_cache()
        
        # 4. 清理临时文件
        clean_temp_files()
        
        print("\n" + "=" * 50)
        if registry_success:
            print("✅ 紧急恢复完成！")
            print("\n建议操作：")
            print("1. 重启计算机以确保所有更改生效")
            print("2. 检查系统设置是否正常")
            print("3. 测试网络连接是否正常")
            print("4. 重新启动QingTalk测试")
        else:
            print("⚠️ 部分恢复可能失败")
            print("\n如果问题仍然存在：")
            print("1. 立即重启计算机")
            print("2. 检查系统还原点")
            print("3. 考虑使用系统还原功能")
        
        print("\n❗ 重要提醒：")
        print("修改计算机名确实会影响系统稳定性")
        print("建议使用其他方式实现多开功能")
        
    except Exception as e:
        print(f"\n❌ 紧急恢复过程出错: {e}")
        print("建议立即重启计算机")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
