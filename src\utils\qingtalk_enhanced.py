#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk增强隔离方案
基于6进程架构的深度隔离
"""

import os
import subprocess
import tempfile
import shutil
import socket
import time
from typing import Optional

def launch_qingtalk_enhanced(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk增强隔离启动
    针对6进程架构的深度隔离
    """
    try:
        print(f"🛡️ QingTalk增强隔离启动 (实例: {instance_id[:8]})")
        
        # 1. 创建完全隔离的QingTalk副本
        base_dir = tempfile.mkdtemp(prefix=f"qingtalk_enhanced_{instance_id[:8]}_")
        qingtalk_source_dir = os.path.dirname(exe_path)
        qingtalk_isolated_dir = os.path.join(base_dir, "QingTalk")
        
        print(f"  📂 复制QingTalk程序...")
        shutil.copytree(qingtalk_source_dir, qingtalk_isolated_dir, 
                       ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))
        
        isolated_exe = os.path.join(qingtalk_isolated_dir, "QingTalk.exe")
        
        # 2. 创建完全独立的用户数据目录
        user_data_dir = os.path.join(base_dir, 'UserData')
        appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
        temp_dir = os.path.join(user_data_dir, 'Temp')
        
        # 创建目录结构
        for path in [appdata_roaming, appdata_local, temp_dir]:
            os.makedirs(path, exist_ok=True)
        
        # 3. 设置强隔离环境变量
        env = os.environ.copy()
        
        # 基础用户数据重定向
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': user_data_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
        })
        
        # 进程隔离标识
        unique_suffix = instance_id[:8]
        env.update({
            'COMPUTERNAME': f'QINGTALK-{unique_suffix.upper()}',
            'USERNAME': f'QTUser_{unique_suffix}',
            'USERDOMAIN': f'QTDOMAIN_{unique_suffix.upper()}',
            'SESSIONNAME': f'QTSession_{unique_suffix}',
        })
        
        # 网络隔离 - 重定向可能的通信端口
        port_base = 40000 + (sum(ord(c) for c in instance_id) % 10000)
        env.update({
            'QINGTALK_PORT_BASE': str(port_base),
            'QINGTALK_IPC_PORT': str(port_base + 1),
            'QINGTALK_SYNC_PORT': str(port_base + 2),
            'QINGTALK_UPDATE_PORT': str(port_base + 3),
        })
        
        # 进程间通信隔离
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_ISOLATION_MODE': '1',
            'QINGTALK_MUTEX_PREFIX': f'QT_{unique_suffix}_',
            'QINGTALK_PIPE_PREFIX': f'QTPipe_{unique_suffix}_',
            'QINGTALK_SHARED_MEM_PREFIX': f'QTMem_{unique_suffix}_',
        })
        
        # 系统标识隔离
        env.update({
            'PROCESSOR_IDENTIFIER': f'Intel64 Family 6 Model {sum(ord(c) for c in instance_id) % 100} Stepping 1',
            'NUMBER_OF_PROCESSORS': '4',
            'PROCESSOR_LEVEL': '6',
            'PROCESSOR_REVISION': f'{sum(ord(c) for c in instance_id) % 9999:04x}',
        })
        
        print(f"  📂 隔离目录: {base_dir}")
        print(f"  🔧 端口基数: {port_base}")
        print(f"  🏷️ 计算机名: QINGTALK-{unique_suffix.upper()}")
        
        # 4. 启动隔离的QingTalk
        process = subprocess.Popen(
            isolated_exe,
            env=env,
            cwd=qingtalk_isolated_dir,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NEW_CONSOLE
        )
        
        # 保存隔离信息
        process.isolation_info = {
            'base_dir': base_dir,
            'user_data_dir': user_data_dir,
            'isolated_exe': isolated_exe,
            'method': 'qingtalk_enhanced',
            'instance_id': instance_id,
            'port_base': port_base
        }
        
        print(f"  ✅ QingTalk增强隔离启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk增强隔离启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def launch_qingtalk_network_isolated(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk网络隔离启动
    专门针对进程间网络通信
    """
    try:
        print(f"🌐 QingTalk网络隔离启动 (实例: {instance_id[:8]})")
        
        # 创建用户数据目录
        base_dir = tempfile.mkdtemp(prefix=f"qingtalk_network_{instance_id[:8]}_")
        appdata_roaming = os.path.join(base_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(base_dir, 'AppData', 'Local')
        temp_dir = os.path.join(base_dir, 'Temp')
        
        for path in [appdata_roaming, appdata_local, temp_dir]:
            os.makedirs(path, exist_ok=True)
        
        # 设置网络隔离环境
        env = os.environ.copy()
        
        # 基础重定向
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': base_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
        })
        
        # 网络命名空间隔离（模拟）
        unique_ip_suffix = (sum(ord(c) for c in instance_id) % 254) + 1
        env.update({
            'QINGTALK_BIND_IP': f'127.0.0.{unique_ip_suffix}',
            'QINGTALK_LOCALHOST_OVERRIDE': f'127.0.0.{unique_ip_suffix}',
            'NETWORK_ISOLATION': '1',
            'INSTANCE_NETWORK_ID': instance_id,
        })
        
        # 禁用网络发现和共享
        env.update({
            'NO_PROXY': '*',
            'HTTP_PROXY': '',
            'HTTPS_PROXY': '',
            'FTP_PROXY': '',
            'SOCKS_PROXY': '',
        })
        
        print(f"  🌐 网络隔离IP: 127.0.0.{unique_ip_suffix}")
        
        # 启动QingTalk
        process = subprocess.Popen(
            exe_path,
            env=env,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        process.isolation_info = {
            'base_dir': base_dir,
            'method': 'qingtalk_network_isolated',
            'instance_id': instance_id,
            'network_ip': f'127.0.0.{unique_ip_suffix}'
        }
        
        print(f"  ✅ QingTalk网络隔离启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk网络隔离启动失败: {e}")
        return None

def cleanup_qingtalk_enhanced(process: subprocess.Popen):
    """
    清理QingTalk增强隔离资源
    """
    try:
        if hasattr(process, 'isolation_info'):
            isolation_info = process.isolation_info
            base_dir = isolation_info.get('base_dir')
            
            if base_dir and os.path.exists(base_dir):
                print(f"🧹 清理QingTalk增强隔离环境: {base_dir}")
                shutil.rmtree(base_dir, ignore_errors=True)
                
    except Exception as e:
        print(f"⚠️ 清理QingTalk增强隔离环境失败: {e}")

def test_qingtalk_enhanced():
    """
    测试QingTalk增强隔离方案
    """
    print("=" * 60)
    print("QingTalk增强隔离方案测试")
    print("针对6进程架构的深度隔离")
    print("=" * 60)
    
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk不存在: {qingtalk_exe}")
        return False
    
    methods = [
        ("增强隔离", launch_qingtalk_enhanced),
        ("网络隔离", launch_qingtalk_network_isolated)
    ]
    
    for method_name, method_func in methods:
        print(f"\n🧪 测试方法: {method_name}")
        print("-" * 40)
        
        processes = []
        
        # 启动2个实例
        for i in range(2):
            print(f"\n🚀 启动实例 {i+1}...")
            process = method_func(qingtalk_exe, f"test_{method_name}_{i}")
            
            if process:
                processes.append(process)
                print(f"✅ 实例 {i+1} 启动成功 (PID: {process.pid})")
            else:
                print(f"❌ 实例 {i+1} 启动失败")
            
            # 延迟启动
            time.sleep(5)
        
        if not processes:
            print(f"❌ {method_name} - 没有实例启动成功")
            continue
        
        # 等待15秒检查状态
        print(f"\n⏳ 等待15秒检查运行状态...")
        time.sleep(15)
        
        running_count = 0
        for i, process in enumerate(processes):
            if process.poll() is None:
                print(f"✅ 实例 {i+1} 正在运行 (PID: {process.pid})")
                running_count += 1
            else:
                print(f"❌ 实例 {i+1} 已停止")
        
        print(f"\n📊 {method_name} 结果: {running_count}/{len(processes)} 个实例成功运行")
        
        # 清理
        print(f"\n🛑 清理实例...")
        for i, process in enumerate(processes):
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=10)
                
                cleanup_qingtalk_enhanced(process)
                print(f"✅ 实例 {i+1} 已清理")
            except Exception as e:
                print(f"⚠️ 实例 {i+1} 清理失败: {e}")
        
        if running_count > 1:
            print(f"🎉 {method_name} 方法成功！")
            return True
        else:
            print(f"❌ {method_name} 方法失败")
    
    return False

if __name__ == "__main__":
    test_qingtalk_enhanced()
