#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk分析启动脚本
一键运行所有分析工具
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("QingTalk多开检测分析工具")
    print("=" * 50)
    print("此工具将帮助分析QingTalk的多开检测机制")
    print()
    print("可用的分析选项:")
    print("1. 综合分析 (推荐) - 包含所有分析功能")
    print("2. 进程监控 - 只监控进程行为")
    print("3. 注册表扫描 - 只扫描注册表")
    print("4. 互斥体检测 - 只检测互斥体")
    print("5. 退出")
    print()
    
    while True:
        choice = input("请选择分析选项 (1-5): ").strip()
        
        if choice == "1":
            run_comprehensive_analysis()
            break
        elif choice == "2":
            run_process_monitor()
            break
        elif choice == "3":
            run_registry_scan()
            break
        elif choice == "4":
            run_mutex_detection()
            break
        elif choice == "5":
            print("退出分析工具")
            break
        else:
            print("无效选择，请输入 1-5")

def run_comprehensive_analysis():
    """运行综合分析"""
    try:
        from src.analysis.comprehensive_analyzer import run_comprehensive_analysis
        run_comprehensive_analysis()
    except Exception as e:
        print(f"综合分析失败: {e}")
        import traceback
        traceback.print_exc()

def run_process_monitor():
    """运行进程监控"""
    try:
        from src.analysis.process_monitor import monitor_qingtalk_startup
        monitor_qingtalk_startup()
    except Exception as e:
        print(f"进程监控失败: {e}")
        import traceback
        traceback.print_exc()

def run_registry_scan():
    """运行注册表扫描"""
    try:
        from src.analysis.registry_monitor import scan_qingtalk_registry
        scan_qingtalk_registry()
    except Exception as e:
        print(f"注册表扫描失败: {e}")
        import traceback
        traceback.print_exc()

def run_mutex_detection():
    """运行互斥体检测"""
    try:
        from src.analysis.mutex_detector import detect_qingtalk_mutexes
        detect_qingtalk_mutexes()
    except Exception as e:
        print(f"互斥体检测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
