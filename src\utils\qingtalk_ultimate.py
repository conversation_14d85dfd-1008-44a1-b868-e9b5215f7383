#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk终极方案
集成全面设备信息修改 + 完美多开 + 持久化登录
"""

import os
import subprocess
import winreg
import tempfile
import shutil
import time
from typing import Optional, Dict

# 全局设备信息管理器
class GlobalDeviceManager:
    """全局设备信息管理器 - 避免多个实例互相覆盖"""

    _instance = None
    _current_modifier = None
    _active_instances = set()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def register_instance(self, instance_id: str, modifier):
        """注册实例"""
        self._active_instances.add(instance_id)
        if self._current_modifier is None:
            self._current_modifier = modifier
            return True  # 可以修改设备信息
        return False  # 已有其他实例在修改

    def unregister_instance(self, instance_id: str):
        """注销实例"""
        if instance_id in self._active_instances:
            self._active_instances.remove(instance_id)

        # 如果是最后一个实例，恢复设备信息
        if len(self._active_instances) == 0 and self._current_modifier:
            print(f"  🔄 最后一个实例停止，恢复原始设备信息...")
            self._current_modifier.restore_device_info()
            self._current_modifier = None

    def get_active_count(self):
        """获取活跃实例数量"""
        return len(self._active_instances)

# 全局管理器实例
global_device_manager = GlobalDeviceManager()

class UltimateDeviceModifier:
    """终极设备修改器 - 使用comprehensive_device_modifier的成功逻辑"""

    def __init__(self):
        self.backup_values = {}
        self.temp_files = []
        self.is_modified = False
    
    def modify_device_info(self, fake_computer: str, fake_user: str, fake_info: Dict[str, str]) -> bool:
        """修改设备信息 - 使用comprehensive_device_modifier的成功方法"""
        try:
            print(f"  🔧 全面修改设备信息 -> {fake_computer}")

            # 1. 修改所有计算机名信息
            print(f"  🖥️ 修改所有计算机名信息 -> {fake_computer}")
            computer_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", "ComputerName"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Hostname"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Hostname"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOwner"),
            ]

            success_count = 0
            for hkey, subkey, value_name in computer_keys:
                if self._backup_and_modify_registry_comprehensive(hkey, subkey, value_name, fake_computer):
                    success_count += 1

            print(f"    📊 计算机名修改: {success_count} 项成功")

            # 2. 修改硬件信息
            print(f"  💻 修改硬件信息")
            hardware_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemManufacturer", fake_info.get('manufacturer', 'Dell Inc.')),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemProductName", fake_info.get('product', f'TestModel-{fake_computer[-8:]}')),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "BIOSVendor", fake_info.get('bios_vendor', 'American Megatrends Inc.')),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0", "ProcessorNameString", fake_info.get('processor', 'Intel(R) Core(TM) i7-9400 CPU @ 2.90GHz')),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0", "Identifier", fake_info.get('cpu_id', 'x86 Family 6 Model 158 Stepping 10')),
            ]

            hardware_count = 0
            for hkey, subkey, value_name, new_value in hardware_keys:
                if self._backup_and_modify_registry_comprehensive(hkey, subkey, value_name, new_value):
                    hardware_count += 1

            print(f"    📊 硬件信息修改: {hardware_count} 项成功")

            # 3. 修改环境变量
            print(f"  🌍 修改环境变量")
            os.environ['COMPUTERNAME'] = fake_computer
            os.environ['USERNAME'] = fake_user
            os.environ['USERDOMAIN'] = fake_computer
            os.environ['LOGONSERVER'] = f'\\\\{fake_computer}'

            # 添加网络相关环境变量，避免网络接口错误
            os.environ['USERDNSDOMAIN'] = fake_computer
            os.environ['CLIENTNAME'] = fake_computer
            os.environ['NODE_OPTIONS'] = '--max-old-space-size=4096'
            os.environ['UV_THREADPOOL_SIZE'] = '4'

            # 应用专门的网络修复
            try:
                from .network_fix import create_qingtalk_network_fix
                network_fix_dir = create_qingtalk_network_fix()
                if network_fix_dir:
                    os.environ['QINGTALK_NETWORK_FIX'] = network_fix_dir
                    print(f"    ✅ QingTalk网络修复已应用")
            except Exception as e:
                print(f"    ⚠️ QingTalk网络修复失败: {e}")

            print(f"    ✅ COMPUTERNAME = {fake_computer}")
            print(f"    ✅ USERNAME = {fake_user}")
            print(f"    ✅ USERDOMAIN = {fake_computer}")
            print(f"    📊 环境变量修改: 成功")

            # 4. 创建伪造系统文件
            print(f"  📁 创建伪造系统文件")
            temp_dir = tempfile.mkdtemp(prefix="fake_system_")
            hosts_content = f"""
# 临时hosts文件修改
127.0.0.1 {fake_computer}
127.0.0.1 {fake_computer.lower()}
"""
            hosts_file = os.path.join(temp_dir, "hosts_backup")
            with open(hosts_file, 'w') as f:
                f.write(hosts_content)

            self.temp_files.append(temp_dir)
            print(f"    ✅ 创建临时系统文件: {temp_dir}")
            print(f"    📊 系统文件创建: 成功")

            self.is_modified = True
            total_success = success_count + hardware_count + 2  # +2 for env vars and system files
            print(f"  ✅ 修改完成！成功修改 {total_success} 项")

            # 5. 等待修改生效
            print(f"  ⏳ 等待5秒让修改生效...")
            time.sleep(5)

            # 6. 验证修改结果
            print(f"  🔍 验证修改结果:")
            print(f"    环境变量 COMPUTERNAME: {os.environ.get('COMPUTERNAME')}")
            print(f"    Python platform.node(): {__import__('platform').node()}")

            try:
                result = subprocess.run(['hostname'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"    命令行 hostname: {result.stdout.strip()}")
            except:
                pass

            return True

        except Exception as e:
            print(f"  ❌ 设备信息修改失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _backup_and_modify_registry_comprehensive(self, hkey, subkey: str, value_name: str, new_value: str) -> bool:
        """备份并修改注册表值 - comprehensive方式"""
        try:
            # 备份
            with winreg.OpenKey(hkey, subkey) as key:
                original_value, value_type = winreg.QueryValueEx(key, value_name)
                backup_key = f"{hkey}\\{subkey}\\{value_name}"
                self.backup_values[backup_key] = (original_value, value_type)
                print(f"    📋 备份: {value_name} = {original_value}")

            # 修改
            with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, new_value)
                print(f"    ✅ 修改: {value_name} = {new_value}")

            return True

        except Exception as e:
            print(f"    ❌ 修改失败: {subkey}\\{value_name} - {e}")
            return False

    def _backup_and_modify_registry(self, hkey, subkey: str, value_name: str, new_value: str) -> bool:
        """备份并修改注册表值 - 兼容性方法"""
        return self._backup_and_modify_registry_comprehensive(hkey, subkey, value_name, new_value)
    
    def restore_device_info(self):
        """恢复设备信息"""
        if not self.is_modified:
            return
        
        try:
            print(f"  🔄 恢复原始设备信息...")
            
            # 恢复注册表
            for backup_key, (original_value, value_type) in self.backup_values.items():
                try:
                    parts = backup_key.split('\\')
                    hkey_str = parts[0]
                    subkey = '\\'.join(parts[1:-1])
                    value_name = parts[-1]
                    
                    if 'HKEY_LOCAL_MACHINE' in hkey_str:
                        hkey = winreg.HKEY_LOCAL_MACHINE
                    else:
                        continue
                    
                    with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                        winreg.SetValueEx(key, value_name, 0, value_type, original_value)
                        
                except Exception as e:
                    print(f"    ⚠️ 恢复失败: {value_name}")
            
            # 清理临时文件
            for temp_dir in self.temp_files:
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                except:
                    pass
            
            self.is_modified = False
            print(f"  ✅ 设备信息已恢复")
            
        except Exception as e:
            print(f"  ❌ 恢复设备信息失败: {e}")

def generate_fake_device_info(instance_id: str) -> Dict[str, str]:
    """生成伪造的设备信息"""
    import random
    import hashlib
    import time

    # 使用实例ID + 当前时间戳作为种子，增加随机性
    timestamp = str(int(time.time() * 1000))  # 毫秒时间戳
    seed_string = f"{instance_id}_{timestamp}"
    seed_value = int(hashlib.md5(seed_string.encode()).hexdigest()[:8], 16)
    random.seed(seed_value)

    # 生成随机的设备标识
    random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=8))

    manufacturers = ['Dell Inc.', 'HP', 'Lenovo', 'ASUS', 'Acer', 'MSI', 'Gigabyte', 'ASRock']
    processors = [
        'Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz',
        'Intel(R) Core(TM) i7-8700 CPU @ 3.20GHz',
        'Intel(R) Core(TM) i5-10400 CPU @ 2.90GHz',
        'Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz',
        'Intel(R) Core(TM) i5-11400 CPU @ 2.60GHz',
        'Intel(R) Core(TM) i7-11700 CPU @ 2.50GHz',
        'AMD Ryzen 5 3600 6-Core Processor',
        'AMD Ryzen 7 3700X 8-Core Processor',
    ]

    computer_prefixes = ['QTPC', 'DESKTOP', 'LAPTOP', 'WORKSTATION', 'PC', 'WIN']

    bios_vendors = ['American Megatrends Inc.', 'Phoenix Technologies', 'Insyde Corp.', 'Award Software']

    fake_info = {
        'computer': f'{random.choice(computer_prefixes)}-{random_suffix}',
        'user': f'User{random_suffix}',
        'manufacturer': random.choice(manufacturers),
        'product': f'Model-{random_suffix}',
        'processor': random.choice(processors),
        'cpu_id': f'x86 Family 6 Model {random.randint(140,200)} Stepping {random.randint(1,15)}',
        'bios_vendor': random.choice(bios_vendors),
    }

    return fake_info

def launch_qingtalk_ultimate(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk终极启动方案
    检查设备信息是否已修改 + 完美多开 + 持久化登录
    """
    try:
        print(f"🌟 QingTalk终极启动 (实例: {instance_id[:8]})")

        # 检查管理员权限
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print(f"  ⚠️ 需要管理员权限进行设备信息修改")
            print(f"  🔄 回退到完美方案...")
            from .qingtalk_perfect import launch_qingtalk_perfect
            return launch_qingtalk_perfect(exe_path, instance_id)

        # 检查设备信息是否已经被修改
        try:
            from .device_modifier import device_modifier
            if device_modifier.is_modified():
                print(f"  ✅ 检测到设备信息已被修改，直接使用当前设备信息")
                current_info = device_modifier.get_current_device_info()
                print(f"  🎭 当前设备信息:")
                print(f"    计算机名: {current_info.get('computer_name', 'Unknown')}")
                print(f"    用户名: {current_info.get('user_name', 'Unknown')}")
            else:
                print(f"  💡 设备信息未修改，使用原始设备信息")
                print(f"  💡 提示：可以点击'修改信息'按钮来修改设备信息")
        except ImportError:
            print(f"  ⚠️ 设备修改模块未找到，使用原始设备信息")

        # 注册到全局管理器（但不自动修改设备信息）
        global_device_manager.register_instance(instance_id, None)

        # 使用完美方案启动QingTalk
        print(f"  🚀 启动QingTalk...")
        from .qingtalk_perfect import launch_qingtalk_perfect
        process = launch_qingtalk_perfect(exe_path, instance_id)

        if process:
            # 保存信息到进程中
            if hasattr(process, 'isolation_info'):
                process.isolation_info['method'] = 'qingtalk_ultimate'
                process.isolation_info['device_modifier'] = None  # 不自动管理设备信息
                process.isolation_info['instance_id'] = instance_id

            print(f"  ✅ QingTalk终极启动成功 (PID: {process.pid})")
            print(f"  📊 当前共有 {global_device_manager.get_active_count()} 个活跃实例")

        return process

    except Exception as e:
        print(f"  ❌ QingTalk终极启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_qingtalk_ultimate(process):
    """清理QingTalk终极方案"""
    try:
        # 从全局管理器注销实例
        if hasattr(process, 'isolation_info'):
            instance_id = process.isolation_info.get('instance_id')
            if instance_id:
                global_device_manager.unregister_instance(instance_id)

        # 使用完美方案的清理
        from .qingtalk_perfect import cleanup_qingtalk_perfect
        cleanup_qingtalk_perfect(process)

    except Exception as e:
        print(f"清理QingTalk终极方案失败: {e}")

if __name__ == "__main__":
    # 测试终极方案
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("🧪 测试QingTalk终极方案...")
        
        import uuid
        process = launch_qingtalk_ultimate(qingtalk_exe, str(uuid.uuid4()))
        
        if process:
            print("🎉 终极方案启动成功！")
            print("💡 请检查QingTalk显示的设备信息")
            input("按回车键停止测试...")
            
            try:
                process.terminate()
                cleanup_qingtalk_ultimate(process)
            except:
                pass
        else:
            print("❌ 终极方案启动失败")
    else:
        print("❌ QingTalk程序不存在")
