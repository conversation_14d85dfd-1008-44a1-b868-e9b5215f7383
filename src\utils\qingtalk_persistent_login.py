#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk持久化登录方案
专注解决登录状态保持问题
"""

import os
import subprocess
import shutil
import time
from typing import Optional

def launch_qingtalk_persistent_login(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk持久化登录启动
    专注保持登录状态
    """
    try:
        print(f"💾 QingTalk持久化登录启动 (实例: {instance_id[:8]})")
        
        # 1. 创建持久化环境
        persistent_base = os.path.join(os.getcwd(), "persistent_data")
        os.makedirs(persistent_base, exist_ok=True)
        
        instance_dir = os.path.join(persistent_base, f"instance_{instance_id[:8]}")
        
        # 检查是否是首次启动
        is_first_time = not os.path.exists(instance_dir)
        
        if is_first_time:
            print(f"  🆕 首次启动，创建完整持久化环境...")
            os.makedirs(instance_dir, exist_ok=True)
        else:
            print(f"  🔄 重用现有持久化环境...")
        
        # 2. 创建完整的用户环境
        user_data_dir = os.path.join(instance_dir, 'UserData')
        
        # Windows用户目录结构
        appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
        appdata_locallow = os.path.join(user_data_dir, 'AppData', 'LocalLow')
        temp_dir = os.path.join(user_data_dir, 'Temp')
        documents_dir = os.path.join(user_data_dir, 'Documents')
        
        # 创建目录结构
        for path in [appdata_roaming, appdata_local, appdata_locallow, temp_dir, documents_dir]:
            os.makedirs(path, exist_ok=True)
        
        # 创建QingTalk专用目录
        qingtalk_roaming = os.path.join(appdata_roaming, 'QingTalk')
        qingtalk_local = os.path.join(appdata_local, 'QingTalk')
        qingtalk_locallow = os.path.join(appdata_locallow, 'QingTalk')
        qingtalk_documents = os.path.join(documents_dir, 'QingTalk')
        
        for path in [qingtalk_roaming, qingtalk_local, qingtalk_locallow, qingtalk_documents]:
            os.makedirs(path, exist_ok=True)
        
        # 3. 如果是首次启动，从系统复制现有的QingTalk数据
        if is_first_time:
            print(f"  📋 复制系统QingTalk数据...")
            
            # 复制系统中的QingTalk数据（如果存在）
            system_locations = [
                (os.path.expanduser("~\\AppData\\Roaming\\QingTalk"), qingtalk_roaming),
                (os.path.expanduser("~\\AppData\\Local\\QingTalk"), qingtalk_local),
                (os.path.expanduser("~\\AppData\\LocalLow\\QingTalk"), qingtalk_locallow),
                (os.path.expanduser("~\\Documents\\QingTalk"), qingtalk_documents),
            ]
            
            for src, dst in system_locations:
                if os.path.exists(src):
                    try:
                        # 复制现有数据
                        if os.path.exists(dst):
                            shutil.rmtree(dst)
                        shutil.copytree(src, dst)
                        print(f"    ✅ 复制: {src} -> {dst}")
                    except Exception as e:
                        print(f"    ⚠️ 复制失败: {src} - {e}")
                        # 确保目录存在
                        os.makedirs(dst, exist_ok=True)
        
        # 4. 设置环境变量
        env = os.environ.copy()
        
        # 重定向所有用户数据路径
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': user_data_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
            'HOMEDRIVE': os.path.splitdrive(user_data_dir)[0],
            'HOMEPATH': os.path.splitdrive(user_data_dir)[1],
            'USERPROFILE': user_data_dir,
        })
        
        # 轻量级标识（避免过度隔离导致检测）
        unique_suffix = instance_id[:8]
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_USER_DATA': user_data_dir,
        })
        
        print(f"  📂 持久化目录: {instance_dir}")
        print(f"  💾 QingTalk数据: {qingtalk_roaming}")
        print(f"  🔧 实例标识: {unique_suffix}")
        print(f"  {'🆕 首次创建' if is_first_time else '🔄 重用现有'}")
        
        # 5. 启动QingTalk（使用原始程序）
        process = subprocess.Popen(
            exe_path,
            env=env,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        # 保存信息
        process.isolation_info = {
            'user_data_dir': instance_dir,
            'method': 'qingtalk_persistent_login',
            'instance_id': instance_id,
            'persistent': True,
            'is_first_time': is_first_time,
            'qingtalk_data_dirs': {
                'roaming': qingtalk_roaming,
                'local': qingtalk_local,
                'locallow': qingtalk_locallow,
                'documents': qingtalk_documents,
            }
        }
        
        print(f"  ✅ QingTalk持久化登录启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk持久化登录启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_qingtalk_persistent_login(process):
    """清理持久化登录资源（保持所有数据）"""
    try:
        if hasattr(process, 'isolation_info'):
            isolation_info = process.isolation_info
            instance_id = isolation_info.get('instance_id', 'unknown')
            
            # 不删除任何数据，完全保持登录状态
            print(f"💾 完全保持实例 {instance_id[:8]} 的登录状态和所有数据")
            print(f"📂 数据目录保留: {isolation_info.get('user_data_dir', 'unknown')}")
            
            qingtalk_dirs = isolation_info.get('qingtalk_data_dirs', {})
            for location, path in qingtalk_dirs.items():
                print(f"💾 QingTalk {location} 数据保留: {path}")
            
    except Exception as e:
        print(f"⚠️ 清理持久化登录资源失败: {e}")

def backup_system_qingtalk_data():
    """备份系统中的QingTalk数据"""
    print("📋 备份系统QingTalk数据...")
    
    backup_dir = os.path.join(os.getcwd(), "qingtalk_backup")
    os.makedirs(backup_dir, exist_ok=True)
    
    system_locations = [
        os.path.expanduser("~\\AppData\\Roaming\\QingTalk"),
        os.path.expanduser("~\\AppData\\Local\\QingTalk"),
        os.path.expanduser("~\\AppData\\LocalLow\\QingTalk"),
        os.path.expanduser("~\\Documents\\QingTalk"),
    ]
    
    for src in system_locations:
        if os.path.exists(src):
            dst_name = src.replace("\\", "_").replace(":", "")
            dst = os.path.join(backup_dir, dst_name)
            
            try:
                if os.path.exists(dst):
                    shutil.rmtree(dst)
                shutil.copytree(src, dst)
                print(f"✅ 备份: {src} -> {dst}")
            except Exception as e:
                print(f"❌ 备份失败: {src} - {e}")

if __name__ == "__main__":
    # 测试持久化登录
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("测试QingTalk持久化登录")
        
        # 备份系统数据
        backup_system_qingtalk_data()
        
        # 启动实例
        import uuid
        instance_id = str(uuid.uuid4())
        process = launch_qingtalk_persistent_login(qingtalk_exe, instance_id)
        
        if process:
            print("🎉 启动成功！")
            print("📝 请在QingTalk中登录，然后按回车测试持久化...")
            input("登录完成后按回车...")
            
            # 停止实例
            process.terminate()
            cleanup_qingtalk_persistent_login(process)
            
            print("⏳ 等待5秒后重新启动测试登录状态...")
            time.sleep(5)
            
            # 重新启动测试
            process2 = launch_qingtalk_persistent_login(qingtalk_exe, instance_id)
            
            if process2:
                print("🔄 重新启动成功！")
                print("💾 请检查是否保持了登录状态")
                input("检查完成后按回车...")
                
                process2.terminate()
                cleanup_qingtalk_persistent_login(process2)
        else:
            print("❌ 启动失败")
    else:
        print("❌ QingTalk程序不存在")
