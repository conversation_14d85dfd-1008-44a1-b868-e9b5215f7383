#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大数据分析工具
分析QingTalk的大型监控数据文件
"""

import json
import os
from datetime import datetime
from collections import Counter, defaultdict
from typing import Dict, List, Any

class QingTalkDataAnalyzer:
    def __init__(self, json_file_path: str):
        self.json_file_path = json_file_path
        self.data = None
        self.analysis_results = {}
    
    def load_data(self):
        """加载JSON数据"""
        try:
            print(f"📂 加载数据文件: {self.json_file_path}")
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            file_size = os.path.getsize(self.json_file_path) / 1024 / 1024  # MB
            print(f"✅ 数据加载成功，文件大小: {file_size:.2f} MB")
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def analyze_process_data(self):
        """分析进程数据"""
        print("\n🔍 分析进程数据...")
        
        if not self.data or 'results' not in self.data:
            return {}
        
        process_data = self.data['results'].get('process', {})
        if process_data.get('status') != 'success':
            print(f"❌ 进程数据不可用: {process_data.get('error', 'Unknown error')}")
            return {}
        
        log_data = process_data.get('log_data', [])
        analysis = process_data.get('analysis', {})
        
        print(f"📊 进程监控数据:")
        print(f"  总快照数: {len(log_data)}")
        print(f"  最大进程数: {analysis.get('max_processes', 0)}")
        print(f"  发现的进程: {len(analysis.get('process_creation_times', []))}")
        
        # 分析进程生命周期
        process_lifecycle = self.analyze_process_lifecycle(log_data)
        
        # 分析文件访问模式
        file_patterns = self.analyze_file_access_patterns(analysis)
        
        # 分析网络连接
        network_patterns = self.analyze_network_patterns(analysis)
        
        return {
            'total_snapshots': len(log_data),
            'max_processes': analysis.get('max_processes', 0),
            'process_lifecycle': process_lifecycle,
            'file_patterns': file_patterns,
            'network_patterns': network_patterns
        }
    
    def analyze_process_lifecycle(self, log_data: List[Dict]) -> Dict:
        """分析进程生命周期"""
        print("  🔄 分析进程生命周期...")
        
        process_timeline = defaultdict(list)
        
        for snapshot in log_data:
            timestamp = snapshot.get('timestamp')
            processes = snapshot.get('processes', [])
            
            for proc in processes:
                if 'error' in proc:
                    continue
                
                pid = proc.get('pid')
                if pid:
                    process_timeline[pid].append({
                        'timestamp': timestamp,
                        'status': proc.get('status'),
                        'cpu_percent': proc.get('cpu_percent'),
                        'memory_info': proc.get('memory_info', {}),
                        'threads': proc.get('threads')
                    })
        
        # 分析每个进程的生命周期
        lifecycle_analysis = {}
        for pid, timeline in process_timeline.items():
            if len(timeline) > 1:
                lifecycle_analysis[pid] = {
                    'duration_snapshots': len(timeline),
                    'first_seen': timeline[0]['timestamp'],
                    'last_seen': timeline[-1]['timestamp'],
                    'avg_cpu': sum(t.get('cpu_percent', 0) for t in timeline) / len(timeline),
                    'max_memory': max(t.get('memory_info', {}).get('rss', 0) for t in timeline),
                    'thread_count': timeline[-1].get('threads', 0)
                }
        
        return lifecycle_analysis
    
    def analyze_file_access_patterns(self, analysis: Dict) -> Dict:
        """分析文件访问模式"""
        print("  📁 分析文件访问模式...")
        
        file_patterns = analysis.get('file_access_patterns', {})
        
        if not file_patterns:
            return {'message': '未发现文件访问模式'}
        
        # 按访问频率排序
        sorted_files = sorted(file_patterns.items(), key=lambda x: x[1], reverse=True)
        
        # 分类文件
        categories = {
            'config_files': [],
            'log_files': [],
            'temp_files': [],
            'system_files': [],
            'other_files': []
        }
        
        for file_path, count in sorted_files:
            file_lower = file_path.lower()
            
            if any(keyword in file_lower for keyword in ['config', 'setting', 'preference']):
                categories['config_files'].append((file_path, count))
            elif any(keyword in file_lower for keyword in ['log', 'debug', 'trace']):
                categories['log_files'].append((file_path, count))
            elif any(keyword in file_lower for keyword in ['temp', 'tmp', 'cache']):
                categories['temp_files'].append((file_path, count))
            elif any(keyword in file_lower for keyword in ['system32', 'windows', 'program files']):
                categories['system_files'].append((file_path, count))
            else:
                categories['other_files'].append((file_path, count))
        
        return {
            'total_files': len(sorted_files),
            'most_accessed': sorted_files[:10],
            'categories': categories
        }
    
    def analyze_network_patterns(self, analysis: Dict) -> Dict:
        """分析网络连接模式"""
        print("  🌐 分析网络连接模式...")
        
        network_connections = analysis.get('network_connections', {})
        
        if not network_connections:
            return {'message': '未发现网络连接'}
        
        # 分析连接模式
        local_connections = {}
        remote_connections = {}
        
        for addr, count in network_connections.items():
            if addr.startswith('127.0.0.1') or addr.startswith('localhost'):
                local_connections[addr] = count
            else:
                remote_connections[addr] = count
        
        return {
            'total_connections': len(network_connections),
            'local_connections': local_connections,
            'remote_connections': remote_connections,
            'most_frequent': sorted(network_connections.items(), key=lambda x: x[1], reverse=True)[:5]
        }
    
    def analyze_registry_data(self):
        """分析注册表数据"""
        print("\n🔍 分析注册表数据...")
        
        registry_data = self.data['results'].get('registry', {})
        if registry_data.get('status') != 'success':
            print(f"❌ 注册表数据不可用")
            return {}
        
        scan_results = registry_data.get('scan_results', {})
        common_keys = registry_data.get('common_keys', {})
        
        total_items = 0
        for root_name, items in scan_results.items():
            count = len(items) if isinstance(items, list) else 0
            total_items += count
            if count > 0:
                print(f"  {root_name}: {count} 个相关项")
        
        print(f"  总计: {total_items} 个QingTalk相关注册表项")
        
        if common_keys:
            print(f"  常见键: {len(common_keys)} 个")
            for key_path, values in common_keys.items():
                if isinstance(values, list):
                    print(f"    {key_path}: {len(values)} 个值")
        
        return {
            'total_items': total_items,
            'scan_results': scan_results,
            'common_keys': common_keys
        }
    
    def analyze_mutex_data(self):
        """分析互斥体数据"""
        print("\n🔍 分析互斥体数据...")
        
        mutex_data = self.data['results'].get('mutex', {})
        if mutex_data.get('status') != 'success':
            print(f"❌ 互斥体数据不可用")
            return {}
        
        mutexes = mutex_data.get('mutexes', [])
        analysis = mutex_data.get('analysis', {})
        
        print(f"  发现互斥体: {len(mutexes)} 个")
        print(f"  相关进程: {len(analysis.get('processes', {}))}")
        print(f"  常见模式: {len(analysis.get('common_patterns', []))}")
        
        if mutexes:
            print(f"  互斥体列表:")
            for mutex in mutexes[:5]:  # 显示前5个
                print(f"    - {mutex.get('name', 'Unknown')} (进程: {mutex.get('process', 'Unknown')})")
        
        return {
            'mutex_count': len(mutexes),
            'mutexes': mutexes,
            'analysis': analysis
        }
    
    def find_detection_clues(self):
        """查找检测线索"""
        print("\n🕵️ 查找检测线索...")
        
        clues = []
        
        # 从进程数据中查找线索
        process_analysis = self.analysis_results.get('process', {})
        if process_analysis:
            max_processes = process_analysis.get('max_processes', 0)
            if max_processes > 1:
                clues.append({
                    'type': 'process_behavior',
                    'description': f'检测到最多 {max_processes} 个QingTalk进程同时运行',
                    'significance': 'high' if max_processes > 2 else 'medium'
                })
            
            # 检查文件访问模式
            file_patterns = process_analysis.get('file_patterns', {})
            if file_patterns.get('categories', {}).get('config_files'):
                config_files = file_patterns['categories']['config_files']
                clues.append({
                    'type': 'file_access',
                    'description': f'频繁访问 {len(config_files)} 个配置文件',
                    'details': [f[0] for f in config_files[:3]],
                    'significance': 'medium'
                })
            
            # 检查网络连接
            network_patterns = process_analysis.get('network_patterns', {})
            local_connections = network_patterns.get('local_connections', {})
            if local_connections:
                clues.append({
                    'type': 'network_communication',
                    'description': f'检测到 {len(local_connections)} 个本地网络连接',
                    'details': list(local_connections.keys()),
                    'significance': 'high'
                })
        
        # 从互斥体数据中查找线索
        mutex_analysis = self.analysis_results.get('mutex', {})
        if mutex_analysis and mutex_analysis.get('mutex_count', 0) > 0:
            clues.append({
                'type': 'mutex_detection',
                'description': f'发现 {mutex_analysis["mutex_count"]} 个相关互斥体',
                'significance': 'high'
            })
        
        return clues
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print("\n" + "=" * 60)
        print("QingTalk 60秒监控数据综合分析报告")
        print("=" * 60)
        
        # 基本信息
        analysis_time = self.data.get('analysis_time', 'Unknown')
        print(f"📅 分析时间: {analysis_time}")
        
        # 分析各部分数据
        self.analysis_results['process'] = self.analyze_process_data()
        self.analysis_results['registry'] = self.analyze_registry_data()
        self.analysis_results['mutex'] = self.analyze_mutex_data()
        
        # 查找检测线索
        clues = self.find_detection_clues()
        
        print(f"\n🕵️ 检测线索分析 ({len(clues)} 个线索):")
        if clues:
            for i, clue in enumerate(clues, 1):
                significance_emoji = "🔴" if clue['significance'] == 'high' else "🟡" if clue['significance'] == 'medium' else "🟢"
                print(f"  {i}. {significance_emoji} {clue['type'].upper()}: {clue['description']}")
                if 'details' in clue:
                    for detail in clue['details'][:3]:
                        print(f"     - {detail}")
                    if len(clue.get('details', [])) > 3:
                        print(f"     - ... 还有 {len(clue['details']) - 3} 个")
        else:
            print("  未发现明显的检测线索")
        
        # 生成建议
        self.generate_recommendations(clues)
        
        return self.analysis_results
    
    def generate_recommendations(self, clues: List[Dict]):
        """生成建议"""
        print(f"\n💡 基于分析的建议:")
        
        high_significance_clues = [c for c in clues if c['significance'] == 'high']
        
        if any(c['type'] == 'network_communication' for c in high_significance_clues):
            print("  1. 🌐 检测到本地网络通信，建议:")
            print("     - 使用网络命名空间隔离")
            print("     - 修改本地回环地址绑定")
            print("     - 使用防火墙规则阻断进程间通信")
        
        if any(c['type'] == 'mutex_detection' for c in high_significance_clues):
            print("  2. 🔒 检测到互斥体使用，建议:")
            print("     - 使用API Hook修改互斥体名称")
            print("     - 使用不同的命名空间")
            print("     - 进程注入修改互斥体行为")
        
        if any(c['type'] == 'process_behavior' for c in clues):
            print("  3. 🔄 检测到进程行为模式，建议:")
            print("     - 使用进程隔离技术")
            print("     - 修改进程启动参数")
            print("     - 使用容器或虚拟机隔离")
        
        if any(c['type'] == 'file_access' for c in clues):
            print("  4. 📁 检测到文件访问模式，建议:")
            print("     - 使用文件系统重定向")
            print("     - 创建独立的配置文件副本")
            print("     - 使用符号链接重定向文件访问")
        
        if not high_significance_clues:
            print("  🤔 未发现明显的检测机制，可能的原因:")
            print("     - QingTalk使用了更隐蔽的检测方法")
            print("     - 检测机制在应用层实现")
            print("     - 需要更长时间的监控来发现模式")
            print("     - 建议尝试虚拟机或容器完全隔离")
    
    def save_analysis_summary(self, filename: str = None):
        """保存分析摘要"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"qingtalk_analysis_summary_{timestamp}.json"
        
        summary = {
            'analysis_time': datetime.now().isoformat(),
            'source_file': self.json_file_path,
            'analysis_results': self.analysis_results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 分析摘要已保存到: {filename}")
        return filename

def analyze_qingtalk_data(json_file_path: str):
    """分析QingTalk数据文件"""
    if not os.path.exists(json_file_path):
        print(f"❌ 文件不存在: {json_file_path}")
        return
    
    analyzer = QingTalkDataAnalyzer(json_file_path)
    
    if not analyzer.load_data():
        return
    
    # 生成综合报告
    results = analyzer.generate_comprehensive_report()
    
    # 保存分析摘要
    summary_file = analyzer.save_analysis_summary()
    
    return results, summary_file

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        json_file = sys.argv[1]
    else:
        json_file = "qingtalk_analysis_20250807_015803.json"
    
    print(f"QingTalk大数据分析工具")
    print(f"分析文件: {json_file}")
    
    analyze_qingtalk_data(json_file)
