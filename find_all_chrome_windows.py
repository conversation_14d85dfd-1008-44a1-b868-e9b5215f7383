#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找所有Chrome相关窗口
"""

import win32gui

def find_all_chrome_windows():
    """查找所有Chrome相关窗口"""
    print("🔍 查找所有Chrome相关窗口...")
    
    chrome_windows = []
    
    def enum_windows_callback(hwnd, data):
        try:
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            is_visible = win32gui.IsWindowVisible(hwnd)
            _, window_pid = win32gui.GetWindowThreadProcessId(hwnd)
            
            # 查找Chrome相关窗口
            is_chrome_related = False
            match_reason = ""
            
            if 'Chrome_WidgetWin_1' in class_name:
                is_chrome_related = True
                match_reason = "Chrome主窗口类"
            elif 'Chrome_RenderWidgetHostHWND' in class_name:
                is_chrome_related = True
                match_reason = "Chrome渲染窗口类"
            elif 'chrome' in class_name.lower():
                is_chrome_related = True
                match_reason = "Chrome类名"
            elif 'chrome' in window_text.lower():
                is_chrome_related = True
                match_reason = "Chrome标题"
            elif window_text == 'QingTalk':
                is_chrome_related = True
                match_reason = "QingTalk标题"
            elif 'qingtalk' in window_text.lower():
                is_chrome_related = True
                match_reason = "QingTalk相关标题"
            
            if is_chrome_related:
                try:
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                except:
                    width = height = 0
                
                window_info = {
                    'hwnd': hwnd,
                    'pid': window_pid,
                    'title': window_text,
                    'class': class_name,
                    'visible': is_visible,
                    'width': width,
                    'height': height,
                    'match_reason': match_reason
                }
                
                chrome_windows.append(window_info)
        
        except Exception:
            pass
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    
    print(f"\n📋 找到 {len(chrome_windows)} 个Chrome相关窗口:")
    print("-" * 80)
    
    # 按可见性和大小排序
    chrome_windows.sort(key=lambda w: (w['visible'], w['width'] * w['height']), reverse=True)
    
    for i, window in enumerate(chrome_windows):
        print(f"窗口 {i+1}:")
        print(f"  句柄: {window['hwnd']}")
        print(f"  PID: {window['pid']}")
        print(f"  标题: '{window['title']}'")
        print(f"  类名: '{window['class']}'")
        print(f"  可见: {'✅' if window['visible'] else '❌'}")
        print(f"  大小: {window['width']}x{window['height']}")
        print(f"  匹配原因: {window['match_reason']}")
        print()
    
    # 分析可能的QingTalk窗口
    print("🎯 分析可能的QingTalk窗口:")
    
    # 查找标题为QingTalk的窗口
    qingtalk_title_windows = [w for w in chrome_windows if w['title'] == 'QingTalk']
    if qingtalk_title_windows:
        print(f"  标题为'QingTalk'的窗口: {len(qingtalk_title_windows)} 个")
        for window in qingtalk_title_windows:
            print(f"    PID: {window['pid']}, 可见: {window['visible']}, 大小: {window['width']}x{window['height']}")
    
    # 查找Chrome_WidgetWin_1类的窗口
    chrome_widget_windows = [w for w in chrome_windows if w['class'] == 'Chrome_WidgetWin_1']
    if chrome_widget_windows:
        print(f"  Chrome_WidgetWin_1类窗口: {len(chrome_widget_windows)} 个")
        for window in chrome_widget_windows:
            print(f"    PID: {window['pid']}, 标题: '{window['title']}', 可见: {window['visible']}, 大小: {window['width']}x{window['height']}")
    
    # 查找大的可见窗口
    large_visible_windows = [w for w in chrome_windows if w['visible'] and w['width'] > 300 and w['height'] > 200]
    if large_visible_windows:
        print(f"  大的可见窗口: {len(large_visible_windows)} 个")
        for window in large_visible_windows:
            print(f"    PID: {window['pid']}, 标题: '{window['title']}', 类: '{window['class']}', 大小: {window['width']}x{window['height']}")
    
    # 推荐最佳窗口
    if large_visible_windows:
        best_window = large_visible_windows[0]
        print(f"\n✅ 推荐激活窗口:")
        print(f"  句柄: {best_window['hwnd']}")
        print(f"  PID: {best_window['pid']}")
        print(f"  标题: '{best_window['title']}'")
        print(f"  类名: '{best_window['class']}'")
        print(f"  大小: {best_window['width']}x{best_window['height']}")
        
        # 测试激活
        try:
            import win32con
            hwnd = best_window['hwnd']
            
            print(f"\n🚀 测试激活窗口...")
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            win32gui.SetForegroundWindow(hwnd)
            print("✅ 窗口激活测试成功")
            
        except Exception as e:
            print(f"❌ 窗口激活测试失败: {e}")
    
    else:
        print(f"\n❌ 没有找到合适的激活窗口")
        if chrome_windows:
            print(f"💡 找到了 {len(chrome_windows)} 个Chrome相关窗口，但都不是大的可见窗口")
        else:
            print(f"💡 完全没有找到Chrome相关窗口")

def main():
    print("=" * 60)
    print("Chrome窗口搜索工具")
    print("=" * 60)
    print("💡 搜索所有Chrome相关窗口，包括QingTalk")
    print()
    
    find_all_chrome_windows()
    
    print("\n" + "=" * 60)
    print("搜索完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
