#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试
"""

import subprocess
import time
import os

def main():
    print("简单多开测试")
    
    test_exe = r"C:\Windows\System32\notepad.exe"
    
    if not os.path.exists(test_exe):
        print("测试程序不存在")
        return
    
    print("启动第一个进程...")
    p1 = subprocess.Popen(test_exe)
    print(f"进程1 PID: {p1.pid}")
    
    time.sleep(2)
    
    print("启动第二个进程...")
    p2 = subprocess.Popen(test_exe)
    print(f"进程2 PID: {p2.pid}")
    
    time.sleep(3)
    
    # 检查进程状态
    if p1.poll() is None:
        print("进程1 正在运行")
    else:
        print("进程1 已停止")
    
    if p2.poll() is None:
        print("进程2 正在运行")
    else:
        print("进程2 已停止")
    
    # 关闭进程
    try:
        p1.terminate()
        p2.terminate()
        print("进程已关闭")
    except:
        print("关闭进程失败")

if __name__ == "__main__":
    main()
