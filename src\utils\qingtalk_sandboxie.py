#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk Sandboxie方案
使用Sandboxie-Plus实现真正的应用隔离
"""

import os
import subprocess
import tempfile
import shutil
from typing import Optional

def check_sandboxie_installed() -> tuple[bool, str]:
    """检查Sandboxie是否已安装"""
    
    # 常见的Sandboxie安装路径
    sandboxie_paths = [
        r"C:\Program Files\Sandboxie-Plus\Start.exe",
        r"C:\Program Files (x86)\Sandboxie-Plus\Start.exe",
        r"C:\Program Files\Sandboxie\Start.exe",
        r"C:\Program Files (x86)\Sandboxie\Start.exe",
    ]
    
    for path in sandboxie_paths:
        if os.path.exists(path):
            return True, path
    
    return False, ""

def create_sandboxie_config(instance_id: str, fake_computer: str) -> str:
    """创建Sandboxie配置文件"""
    
    sandbox_name = f"QingTalk_{instance_id[:8]}"
    
    config_content = f"""
[{sandbox_name}]
Enabled=y
AutoRecover=y
RecoverFolder=%Desktop%
RecoverFolder=%Favorites%
RecoverFolder=%Personal%
BlockNetworkFiles=n
Template=OpenFilePath
Template=OpenKeyPath
Template=OpenPipePath
Template=OpenConfPath
Template=LingerPrograms
Template=AutoRecoverIgnore

# 设备信息伪造
InjectDll=y
ComputerName={fake_computer}

# 网络隔离（可选）
BlockNetParam=n

# 文件系统隔离
OpenFilePath={sandbox_name},C:\\Program Files\\QingTalk\\QingTalk\\*
OpenFilePath={sandbox_name},%AppData%\\QingTalk\\*
OpenFilePath={sandbox_name},%LocalAppData%\\QingTalk\\*

# 注册表隔离
OpenKeyPath={sandbox_name},HKEY_CURRENT_USER\\Software\\QingTalk
OpenKeyPath={sandbox_name},HKEY_LOCAL_MACHINE\\Software\\QingTalk

# 进程隔离
OpenWinClass={sandbox_name},QingTalk*
"""
    
    # 保存配置文件
    temp_dir = tempfile.mkdtemp(prefix=f"sandboxie_{instance_id[:8]}_")
    config_file = os.path.join(temp_dir, f"{sandbox_name}.ini")
    
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    return config_file, sandbox_name

def launch_qingtalk_sandboxie(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    使用Sandboxie启动QingTalk
    """
    try:
        print(f"📦 QingTalk Sandboxie启动 (实例: {instance_id[:8]})")
        
        # 检查Sandboxie是否安装
        sandboxie_installed, sandboxie_path = check_sandboxie_installed()
        
        if not sandboxie_installed:
            print(f"  ❌ Sandboxie未安装")
            print(f"  💡 请下载安装 Sandboxie-Plus: https://sandboxie-plus.com/")
            print(f"  🔄 回退到完美方案...")
            
            # 回退到完美方案
            import sys
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from src.utils.qingtalk_perfect import launch_qingtalk_perfect
            return launch_qingtalk_perfect(exe_path, instance_id)
        
        print(f"  ✅ 找到Sandboxie: {sandboxie_path}")
        
        # 生成设备信息
        import random
        import hashlib
        
        unique_suffix = instance_id[:8]
        seed_value = int(hashlib.md5(instance_id.encode()).hexdigest()[:8], 16)
        random.seed(seed_value)
        
        fake_computer = f'SANDBOX-{unique_suffix.upper()}'
        
        # 创建Sandboxie配置
        config_file, sandbox_name = create_sandboxie_config(instance_id, fake_computer)
        
        print(f"  📦 沙箱名称: {sandbox_name}")
        print(f"  🖥️ 伪造计算机名: {fake_computer}")
        print(f"  📝 配置文件: {config_file}")
        
        # 启动Sandboxie
        cmd = [
            sandboxie_path,
            f"/box:{sandbox_name}",
            exe_path
        ]
        
        print(f"  🚀 启动命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(cmd, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        if process:
            print(f"  ✅ Sandboxie启动成功 (PID: {process.pid})")
            
            # 保存隔离信息
            process.isolation_info = {
                'method': 'qingtalk_sandboxie',
                'sandbox_name': sandbox_name,
                'config_file': config_file,
                'sandboxie_path': sandboxie_path,
                'fake_computer': fake_computer,
                'instance_id': instance_id
            }
        
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk Sandboxie启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_sandboxie(process):
    """清理Sandboxie沙箱"""
    try:
        if hasattr(process, 'isolation_info'):
            info = process.isolation_info
            sandbox_name = info.get('sandbox_name')
            config_file = info.get('config_file')
            
            if sandbox_name:
                print(f"🧹 清理沙箱: {sandbox_name}")
                
                # 删除沙箱内容（可选）
                # 注意：这会删除沙箱中的所有数据
                # subprocess.run([info['sandboxie_path'], f"/box:{sandbox_name}", "delete_sandbox"])
            
            if config_file and os.path.exists(config_file):
                config_dir = os.path.dirname(config_file)
                if os.path.exists(config_dir):
                    shutil.rmtree(config_dir, ignore_errors=True)
                    print(f"🗑️ 删除配置文件: {config_file}")
                    
    except Exception as e:
        print(f"清理Sandboxie失败: {e}")

def launch_qingtalk_vm_recommendation(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    虚拟机方案推荐
    """
    print(f"🖥️ QingTalk虚拟机方案推荐 (实例: {instance_id[:8]})")
    print(f"")
    print(f"💡 由于QingTalk使用深层设备检测，建议使用以下方案：")
    print(f"")
    print(f"🥇 最佳方案 - 虚拟机:")
    print(f"   1. VMware Workstation Pro")
    print(f"   2. VirtualBox (免费)")
    print(f"   3. Hyper-V (Windows专业版)")
    print(f"   每个虚拟机运行一个QingTalk实例")
    print(f"")
    print(f"🥈 次佳方案 - 专业沙箱:")
    print(f"   1. Sandboxie-Plus (免费)")
    print(f"   2. Windows Sandbox (Windows专业版)")
    print(f"   3. Docker Desktop (容器化)")
    print(f"")
    print(f"🥉 当前方案 - 数据隔离:")
    print(f"   保持登录状态 + 数据隔离")
    print(f"   无法完全绕过设备检测")
    print(f"")
    
    choice = input("是否继续使用当前数据隔离方案？(y/n): ").lower()
    
    if choice == 'y':
        print(f"🔄 使用完美方案（数据隔离 + 持久化登录）...")
        
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from src.utils.qingtalk_perfect import launch_qingtalk_perfect
        return launch_qingtalk_perfect(exe_path, instance_id)
    else:
        print(f"💡 建议安装虚拟机软件后重试")
        return None

if __name__ == "__main__":
    print("QingTalk Sandboxie和虚拟机方案测试")
    
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("🧪 测试Sandboxie方案...")
        
        import uuid
        process = launch_qingtalk_sandboxie(qingtalk_exe, str(uuid.uuid4()))
        
        if process:
            print("🎉 启动成功！")
            print("💡 请检查QingTalk显示的设备信息是否有变化")
            input("按回车键停止测试...")
            
            try:
                process.terminate()
                cleanup_sandboxie(process)
            except:
                pass
        else:
            print("❌ 启动失败")
            
            # 显示虚拟机方案推荐
            launch_qingtalk_vm_recommendation(qingtalk_exe, str(uuid.uuid4()))
    else:
        print("❌ QingTalk程序不存在")
