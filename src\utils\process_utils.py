#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程工具
处理进程名修改和隔离
"""

import os
import shutil
import tempfile
import uuid
from typing import Optional

def create_isolated_executable(exe_path: str, instance_id: str) -> Optional[str]:
    """
    创建隔离的可执行文件副本
    通过复制exe文件到临时目录并重命名来避免进程名冲突
    """
    try:
        if not os.path.exists(exe_path):
            print(f"原始exe文件不存在: {exe_path}")
            return None
        
        # 获取原始文件信息
        original_name = os.path.basename(exe_path)
        name_without_ext = os.path.splitext(original_name)[0]
        ext = os.path.splitext(original_name)[1]
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix=f"isolated_exe_{instance_id[:8]}_")
        
        # 生成新的文件名（添加实例ID）
        new_name = f"{name_without_ext}_{instance_id[:8]}{ext}"
        new_exe_path = os.path.join(temp_dir, new_name)
        
        # 复制exe文件
        shutil.copy2(exe_path, new_exe_path)
        
        print(f"创建隔离exe: {original_name} -> {new_name}")
        print(f"隔离路径: {new_exe_path}")
        
        return new_exe_path
        
    except Exception as e:
        print(f"创建隔离exe失败: {e}")
        return None

def cleanup_isolated_executable(exe_path: str):
    """清理隔离的可执行文件"""
    try:
        if exe_path and os.path.exists(exe_path):
            # 删除文件
            os.remove(exe_path)
            
            # 删除临时目录
            temp_dir = os.path.dirname(exe_path)
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
                print(f"清理隔离exe: {exe_path}")
        
    except Exception as e:
        print(f"清理隔离exe失败: {e}")

def create_process_wrapper(exe_path: str, instance_id: str) -> Optional[str]:
    """
    创建进程包装器
    通过批处理文件启动程序，增加额外的隔离层
    """
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix=f"wrapper_{instance_id[:8]}_")
        
        # 创建批处理文件
        wrapper_name = f"launcher_{instance_id[:8]}.bat"
        wrapper_path = os.path.join(temp_dir, wrapper_name)
        
        # 批处理文件内容
        batch_content = f'''@echo off
title Instance_{instance_id[:8]}
cd /d "{os.path.dirname(exe_path)}"
"{exe_path}" %*
'''
        
        # 写入批处理文件
        with open(wrapper_path, 'w', encoding='gbk') as f:
            f.write(batch_content)
        
        print(f"创建进程包装器: {wrapper_path}")
        return wrapper_path
        
    except Exception as e:
        print(f"创建进程包装器失败: {e}")
        return None

def get_process_isolation_env(instance_id: str) -> dict:
    """获取进程隔离环境变量"""
    env = {}
    
    # 设置唯一的进程标识
    env['PROCESS_INSTANCE_ID'] = instance_id
    env['PROCESS_UNIQUE_ID'] = str(uuid.uuid4())
    
    # 设置窗口标题
    env['WINDOW_TITLE'] = f"Instance_{instance_id[:8]}"
    
    # 设置互斥体名称（某些程序使用互斥体检测单实例）
    env['MUTEX_NAME'] = f"Global\\Instance_{instance_id}"
    
    return env

def modify_process_command_line(exe_path: str, instance_id: str) -> list:
    """
    修改进程命令行参数
    添加实例特定的参数来区分不同实例
    """
    command = [exe_path]
    
    # 添加实例特定参数（如果程序支持）
    # 注意：这些参数可能不被所有程序识别，但可以帮助区分进程
    command.extend([
        f"--instance-id={instance_id[:8]}",
        f"--sandbox-mode=1",
        f"--unique-session={uuid.uuid4().hex[:8]}"
    ])
    
    return command
