#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复损坏的实例
重新创建程序文件和伪装程序
"""

import os
import shutil

def fix_instances():
    """修复损坏的实例"""
    print("🔧 修复损坏的实例工具")
    print("=" * 40)
    
    persistent_base = os.path.join(os.getcwd(), "persistent_data")
    
    if not os.path.exists(persistent_base):
        print("📂 persistent_data目录不存在")
        return
    
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    if not os.path.exists(qingtalk_exe):
        print(f"❌ QingTalk程序不存在: {qingtalk_exe}")
        return
    
    qingtalk_source_dir = os.path.dirname(qingtalk_exe)
    
    print(f"📂 检查目录: {persistent_base}")
    
    fixed_count = 0
    total_count = 0
    
    try:
        for item in os.listdir(persistent_base):
            if item.startswith('instance_'):
                total_count += 1
                instance_dir = os.path.join(persistent_base, item)
                
                if os.path.isdir(instance_dir):
                    instance_id = item.replace('instance_', '')
                    qingtalk_program_dir = os.path.join(instance_dir, 'QingTalk')
                    
                    print(f"\n🔧 修复实例: {item}")
                    
                    needs_fix = False
                    
                    # 检查程序目录
                    if not os.path.exists(qingtalk_program_dir):
                        print(f"  ❌ 程序目录不存在")
                        needs_fix = True
                    else:
                        # 检查原始程序
                        original_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")
                        if not os.path.exists(original_exe):
                            print(f"  ❌ 原始程序不存在")
                            needs_fix = True
                        
                        # 检查伪装程序
                        disguised_exe = os.path.join(qingtalk_program_dir, f"QingTalk_{instance_id}.exe")
                        if not os.path.exists(disguised_exe):
                            print(f"  ❌ 伪装程序不存在")
                            needs_fix = True
                    
                    if needs_fix:
                        try:
                            # 删除旧的程序目录
                            if os.path.exists(qingtalk_program_dir):
                                shutil.rmtree(qingtalk_program_dir)
                            
                            # 重新复制程序
                            print(f"  📂 重新复制QingTalk程序...")
                            shutil.copytree(qingtalk_source_dir, qingtalk_program_dir, 
                                           ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))
                            
                            # 创建伪装程序
                            original_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")
                            disguised_exe = os.path.join(qingtalk_program_dir, f"QingTalk_{instance_id}.exe")
                            
                            if os.path.exists(original_exe):
                                shutil.copy2(original_exe, disguised_exe)
                                print(f"  🎭 创建伪装程序: QingTalk_{instance_id}.exe")
                            
                            fixed_count += 1
                            print(f"  ✅ 修复成功")
                            
                        except Exception as e:
                            print(f"  ❌ 修复失败: {e}")
                    else:
                        print(f"  ✅ 实例完整，无需修复")
        
        print(f"\n📊 修复统计:")
        print(f"   总实例数: {total_count}")
        print(f"   修复数量: {fixed_count}")
        print(f"   完整数量: {total_count - fixed_count}")
        
        if fixed_count > 0:
            print(f"\n🎉 修复完成！修复了 {fixed_count} 个实例")
        else:
            print(f"\n✅ 所有实例都是完整的，无需修复")
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")

def main():
    print("QingTalk实例修复工具")
    print("=" * 50)
    
    fix_instances()
    
    print("\n修复完成。")

if __name__ == "__main__":
    main()
