#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理损坏的实例工具
删除不完整或损坏的实例目录
"""

import os
import shutil
from datetime import datetime

def clean_damaged_instances():
    """清理损坏的实例"""
    print("🧹 清理损坏的实例工具")
    print("=" * 40)
    
    persistent_base = os.path.join(os.getcwd(), "persistent_data")
    
    if not os.path.exists(persistent_base):
        print("📂 persistent_data目录不存在，无需清理")
        return
    
    print(f"📂 检查目录: {persistent_base}")
    
    cleaned_count = 0
    total_count = 0
    
    try:
        for item in os.listdir(persistent_base):
            if item.startswith('instance_'):
                total_count += 1
                instance_dir = os.path.join(persistent_base, item)
                
                if os.path.isdir(instance_dir):
                    # 检查实例是否完整
                    qingtalk_dir = os.path.join(instance_dir, 'QingTalk')
                    qingtalk_exe = os.path.join(qingtalk_dir, 'QingTalk.exe')
                    resources_dir = os.path.join(qingtalk_dir, 'resources')
                    
                    is_damaged = False
                    damage_reasons = []
                    
                    # 检查程序文件
                    if not os.path.exists(qingtalk_exe):
                        is_damaged = True
                        damage_reasons.append("缺少QingTalk.exe")
                    
                    # 检查资源目录
                    if not os.path.exists(resources_dir):
                        is_damaged = True
                        damage_reasons.append("缺少resources目录")
                    
                    # 检查目录大小（太小可能是复制不完整）
                    try:
                        dir_size = sum(os.path.getsize(os.path.join(dirpath, filename))
                                     for dirpath, dirnames, filenames in os.walk(qingtalk_dir)
                                     for filename in filenames)
                        
                        # QingTalk程序目录应该至少有100MB
                        if dir_size < 100 * 1024 * 1024:  # 100MB
                            is_damaged = True
                            damage_reasons.append(f"目录太小 ({dir_size / 1024 / 1024:.1f}MB)")
                    except:
                        is_damaged = True
                        damage_reasons.append("无法计算目录大小")
                    
                    if is_damaged:
                        print(f"🗑️ 删除损坏的实例: {item}")
                        print(f"   原因: {', '.join(damage_reasons)}")
                        
                        try:
                            shutil.rmtree(instance_dir, ignore_errors=True)
                            cleaned_count += 1
                            print(f"   ✅ 删除成功")
                        except Exception as e:
                            print(f"   ❌ 删除失败: {e}")
                    else:
                        print(f"✅ 实例完整: {item}")
        
        print(f"\n📊 清理统计:")
        print(f"   总实例数: {total_count}")
        print(f"   清理数量: {cleaned_count}")
        print(f"   保留数量: {total_count - cleaned_count}")
        
        if cleaned_count > 0:
            print(f"\n🎉 清理完成！删除了 {cleaned_count} 个损坏的实例")
        else:
            print(f"\n✅ 所有实例都是完整的，无需清理")
            
    except Exception as e:
        print(f"❌ 清理过程出错: {e}")

def clean_all_instances():
    """清理所有实例（慎用）"""
    persistent_base = os.path.join(os.getcwd(), "persistent_data")
    
    if not os.path.exists(persistent_base):
        print("📂 persistent_data目录不存在")
        return
    
    print("⚠️ 警告：这将删除所有实例数据，包括登录状态！")
    confirm = input("确定要继续吗？(输入 'YES' 确认): ")
    
    if confirm == 'YES':
        try:
            shutil.rmtree(persistent_base, ignore_errors=True)
            print("🗑️ 所有实例数据已删除")
        except Exception as e:
            print(f"❌ 删除失败: {e}")
    else:
        print("❌ 操作已取消")

def main():
    print("QingTalk实例清理工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 清理损坏的实例")
        print("2. 清理所有实例（慎用）")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            clean_damaged_instances()
        elif choice == '2':
            clean_all_instances()
        elif choice == '3':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
