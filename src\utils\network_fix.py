#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络接口修复工具
解决QingTalk网络接口错误
"""

import os
import subprocess
import tempfile

def fix_network_interfaces():
    """修复网络接口问题"""
    try:
        print("🔧 修复网络接口...")

        # 1. 刷新DNS缓存
        try:
            result = subprocess.run(['ipconfig', '/flushdns'],
                                  capture_output=True, timeout=10, text=True)
            if result.returncode == 0:
                print("  ✅ DNS缓存已刷新")
            else:
                print("  ⚠️ DNS缓存刷新失败")
        except Exception as e:
            print(f"  ⚠️ DNS刷新异常: {e}")

        # 2. 创建临时网络配置目录
        temp_dir = tempfile.mkdtemp(prefix="network_fix_")

        # 3. 创建临时hosts文件
        hosts_content = """# Temporary hosts file for QingTalk
127.0.0.1 localhost
::1 localhost
127.0.0.1 local
"""

        hosts_file = os.path.join(temp_dir, "hosts")
        with open(hosts_file, 'w') as f:
            f.write(hosts_content)

        print(f"  ✅ 临时网络配置: {temp_dir}")
        return temp_dir

    except Exception as e:
        print(f"  ❌ 网络接口修复失败: {e}")
        return None

def apply_network_environment_fix(env_dict: dict, fake_computer: str):
    """应用网络环境修复"""

    print("🌐 应用网络环境修复...")

    # 添加网络相关环境变量
    network_env = {
        'USERDNSDOMAIN': f'{fake_computer}.local',
        'CLIENTNAME': fake_computer,
        'SESSIONNAME': 'Console',
        'WINDIR': os.environ.get('WINDIR', 'C:\\Windows'),
        'SYSTEMROOT': os.environ.get('SYSTEMROOT', 'C:\\Windows'),
        'PATHEXT': os.environ.get('PATHEXT', '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'),
        # 添加更多网络相关变量
        'HOMEDRIVE': os.environ.get('HOMEDRIVE', 'C:'),
        'HOMEPATH': os.environ.get('HOMEPATH', '\\Users\\Administrator'),
        'LOCALAPPDATA': os.environ.get('LOCALAPPDATA', 'C:\\Users\\<USER>\\AppData\\Local'),
        'APPDATA': os.environ.get('APPDATA', 'C:\\Users\\<USER>\\AppData\\Roaming'),
    }

    env_dict.update(network_env)

    # 确保网络路径存在
    try:
        # 创建临时网络配置
        temp_dir = fix_network_interfaces()
        if temp_dir:
            env_dict['TEMP_NETWORK_DIR'] = temp_dir
    except Exception as e:
        print(f"  ⚠️ 网络环境修复警告: {e}")

    print("  ✅ 网络环境变量已设置")
    return env_dict

def create_qingtalk_network_fix():
    """为QingTalk创建专门的网络修复"""
    try:
        print("🔧 创建QingTalk网络修复...")

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="qingtalk_network_")

        # 创建网络接口模拟文件
        network_config = {
            "interfaces": {
                "Ethernet": {
                    "address": "*************",
                    "netmask": "*************",
                    "family": "IPv4",
                    "mac": "00:15:5D:FF:FF:FF",
                    "internal": False
                },
                "Loopback": {
                    "address": "127.0.0.1",
                    "netmask": "*********",
                    "family": "IPv4",
                    "mac": "00:00:00:00:00:00",
                    "internal": True
                }
            }
        }

        # 保存网络配置
        import json
        config_file = os.path.join(temp_dir, "network_interfaces.json")
        with open(config_file, 'w') as f:
            json.dump(network_config, f, indent=2)

        print(f"  ✅ 网络配置文件: {config_file}")
        return temp_dir

    except Exception as e:
        print(f"  ❌ 创建网络修复失败: {e}")
        return None

def cleanup_network_fix(temp_dir: str):
    """清理网络修复临时文件"""
    try:
        if temp_dir and os.path.exists(temp_dir):
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
    except Exception as e:
        print(f"清理网络修复文件失败: {e}")

if __name__ == "__main__":
    print("网络接口修复工具测试")
    
    temp_dir = fix_network_interfaces()
    if temp_dir:
        print(f"✅ 网络修复成功: {temp_dir}")
        cleanup_network_fix(temp_dir)
        print("✅ 清理完成")
    else:
        print("❌ 网络修复失败")
