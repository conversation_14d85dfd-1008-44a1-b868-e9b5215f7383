#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk启动包装器
专门解决网络接口错误问题
"""

import os
import subprocess
import tempfile
import json

def create_network_safe_environment():
    """创建网络安全的环境变量"""
    
    # 基础环境变量
    safe_env = os.environ.copy()
    
    # 网络修复相关环境变量
    network_fix_env = {
        'NODE_OPTIONS': '--max-old-space-size=4096 --no-deprecation',
        'UV_THREADPOOL_SIZE': '4',
        'ELECTRON_DISABLE_SECURITY_WARNINGS': 'true',
        'ELECTRON_NO_ATTACH_CONSOLE': 'true',
        'USERDNSDOMAIN': 'local',
        'CLIENTNAME': 'QingTalkPC',
        'SESSIONNAME': 'Console',
    }
    
    safe_env.update(network_fix_env)
    
    # 创建临时网络配置
    try:
        temp_dir = tempfile.mkdtemp(prefix="qingtalk_safe_")
        
        # 创建网络接口配置文件
        network_config = {
            "version": "1.0",
            "interfaces": {
                "Ethernet": {
                    "address": "*************",
                    "netmask": "*************",
                    "family": "IPv4",
                    "mac": "00:15:5D:FF:FF:FF",
                    "internal": False,
                    "status": "up"
                },
                "Loopback": {
                    "address": "127.0.0.1",
                    "netmask": "*********",
                    "family": "IPv4",
                    "mac": "00:00:00:00:00:00",
                    "internal": True,
                    "status": "up"
                }
            }
        }
        
        config_file = os.path.join(temp_dir, "network_interfaces.json")
        with open(config_file, 'w') as f:
            json.dump(network_config, f, indent=2)
        
        safe_env['QINGTALK_NETWORK_CONFIG'] = config_file
        safe_env['TEMP_NETWORK_DIR'] = temp_dir
        
    except Exception as e:
        print(f"创建网络配置失败: {e}")
    
    return safe_env

def launch_qingtalk_safe(exe_path, args=None, env=None, working_dir=None):
    """安全启动QingTalk，避免网络接口错误"""
    
    try:
        print("🚀 安全启动QingTalk...")
        
        # 创建安全的环境
        safe_env = create_network_safe_environment()
        
        # 如果提供了额外的环境变量，合并它们
        if env:
            safe_env.update(env)
        
        # 构建启动命令
        cmd = [exe_path]
        if args:
            cmd.extend(args)
        
        print(f"  📂 程序路径: {exe_path}")
        print(f"  🌐 网络修复: 已应用")
        
        # 设置工作目录
        if working_dir and os.path.exists(working_dir):
            work_dir = working_dir
            print(f"  📂 使用指定工作目录: {work_dir}")
        else:
            work_dir = os.path.dirname(exe_path)
            print(f"  📂 使用exe目录作为工作目录: {work_dir}")

        # 启动进程（不重定向输出，避免阻塞）
        process = subprocess.Popen(
            cmd,
            env=safe_env,
            cwd=work_dir,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"  ✅ QingTalk已启动 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk启动失败: {e}")
        return None

def test_network_fix():
    """测试网络修复是否有效"""
    print("🧪 测试网络修复...")
    
    try:
        # 刷新DNS
        subprocess.run(['ipconfig', '/flushdns'], 
                      capture_output=True, timeout=5)
        print("  ✅ DNS刷新成功")
        
        # 测试网络连接
        result = subprocess.run(['ping', '127.0.0.1', '-n', '1'], 
                              capture_output=True, timeout=5)
        if result.returncode == 0:
            print("  ✅ 本地网络正常")
        else:
            print("  ⚠️ 本地网络异常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 网络测试失败: {e}")
        return False

if __name__ == "__main__":
    # 测试网络修复
    test_network_fix()
    
    # 测试安全启动
    qingtalk_path = r"C:\Program Files\QingTalk\QingTalk\QingTalk.exe"
    if os.path.exists(qingtalk_path):
        process = launch_qingtalk_safe(qingtalk_path)
        if process:
            print("测试启动成功！")
            input("按回车键停止测试...")
            process.terminate()
        else:
            print("测试启动失败！")
    else:
        print(f"QingTalk不存在: {qingtalk_path}")
