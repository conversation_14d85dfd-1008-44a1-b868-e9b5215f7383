#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统恢复工具
清理所有可能被修改的系统设置，恢复到原始状态
"""

import os
import sys
import winreg
import ctypes
import subprocess
import json
import glob
from pathlib import Path
from datetime import datetime

class CompleteSystemRestore:
    def __init__(self):
        self.original_computer_name = "PC-20250807RRZC"  # 从之前的脚本中获取的原始名称
        self.restored_items = []
        
    def check_admin(self):
        """检查管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def restore_computer_name_registry(self):
        """恢复计算机名称相关的注册表项"""
        print("🔧 恢复计算机名称注册表项...")
        
        # 所有可能被修改的计算机名注册表项
        registry_items = [
            # 基本计算机名
            (r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
            (r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", "ComputerName"),
            
            # TCP/IP参数（这些可能导致网络问题）
            (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Hostname"),
            (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Hostname"),
            
            # 清理可能的Domain设置
            (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Domain"),
            (r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Domain"),
        ]
        
        success_count = 0
        for reg_path, value_name in registry_items:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path, 0, winreg.KEY_SET_VALUE) as key:
                    if value_name in ["Domain", "NV Domain"]:
                        # 清空域设置
                        winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, "")
                        print(f"   ✅ 清空: {reg_path}\\{value_name}")
                    else:
                        # 恢复原始计算机名
                        winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, self.original_computer_name)
                        print(f"   ✅ 恢复: {reg_path}\\{value_name} = {self.original_computer_name}")
                    
                    success_count += 1
                    self.restored_items.append(f"{reg_path}\\{value_name}")
                    
            except Exception as e:
                print(f"   ❌ 恢复失败: {reg_path}\\{value_name} - {e}")
        
        return success_count
    
    def restore_system_info_registry(self):
        """恢复系统信息注册表项"""
        print("\n🔧 恢复系统信息注册表项...")
        
        # 系统信息注册表项
        system_items = [
            (r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOwner", "User"),
            (r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOrganization", ""),
        ]
        
        success_count = 0
        for reg_path, value_name, default_value in system_items:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path, 0, winreg.KEY_SET_VALUE) as key:
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, default_value)
                    print(f"   ✅ 恢复: {reg_path}\\{value_name} = {default_value}")
                    success_count += 1
                    self.restored_items.append(f"{reg_path}\\{value_name}")
                    
            except Exception as e:
                print(f"   ❌ 恢复失败: {reg_path}\\{value_name} - {e}")
        
        return success_count
    
    def clean_dingtalk_registry(self):
        """清理DingTalk注册表项"""
        print("\n🗑️ 清理DingTalk注册表项...")
        
        registry_paths = [
            (winreg.HKEY_CURRENT_USER, r"Software\DingTalk"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\DingTalk"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\DingTalk"),
            (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall\DingTalk"),
        ]
        
        success_count = 0
        for hkey, path in registry_paths:
            try:
                winreg.DeleteKey(hkey, path)
                print(f"   ✅ 删除: {path}")
                success_count += 1
                self.restored_items.append(f"删除: {path}")
                
            except FileNotFoundError:
                print(f"   📋 不存在: {path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {path} - {e}")
        
        return success_count
    
    def clean_backup_files(self):
        """清理备份文件"""
        print("\n🗑️ 清理备份文件...")
        
        # 查找所有可能的备份文件
        backup_patterns = [
            "computer_name_backup.json",
            "device_backup.json", 
            "qingtalk_registry_backup.json",
            "system_backup_*.json",
            "device_name_backup_*.json",
            "*_backup.json"
        ]
        
        cleaned_count = 0
        for pattern in backup_patterns:
            for file_path in glob.glob(pattern):
                try:
                    os.remove(file_path)
                    print(f"   ✅ 删除备份文件: {file_path}")
                    cleaned_count += 1
                    self.restored_items.append(f"删除备份文件: {file_path}")
                except Exception as e:
                    print(f"   ❌ 删除失败: {file_path} - {e}")
        
        if cleaned_count == 0:
            print("   📋 未找到备份文件")
        
        return cleaned_count
    
    def clean_temp_files(self):
        """清理临时文件"""
        print("\n🗑️ 清理临时文件...")
        
        # 查找临时目录
        temp_patterns = [
            "qingtalk_env_*",
            "fake_system_*",
            "device_spoofer*"
        ]
        
        import tempfile
        temp_dir = Path(tempfile.gettempdir())
        
        cleaned_count = 0
        for pattern in temp_patterns:
            for temp_path in temp_dir.glob(pattern):
                try:
                    if temp_path.is_dir():
                        import shutil
                        shutil.rmtree(temp_path)
                        print(f"   ✅ 删除临时目录: {temp_path}")
                    else:
                        temp_path.unlink()
                        print(f"   ✅ 删除临时文件: {temp_path}")
                    
                    cleaned_count += 1
                    self.restored_items.append(f"删除临时文件: {temp_path}")
                    
                except Exception as e:
                    print(f"   ❌ 删除失败: {temp_path} - {e}")
        
        if cleaned_count == 0:
            print("   📋 未找到临时文件")
        
        return cleaned_count
    
    def reset_network_settings(self):
        """重置网络设置"""
        print("\n🌐 重置网络设置...")
        
        commands = [
            (['ipconfig', '/flushdns'], "刷新DNS缓存"),
            (['ipconfig', '/registerdns'], "重新注册DNS"),
            (['netsh', 'winsock', 'reset'], "重置Winsock"),
            (['netsh', 'int', 'ip', 'reset'], "重置IP配置"),
        ]
        
        success_count = 0
        for cmd, description in commands:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, encoding='gbk')
                if result.returncode == 0:
                    print(f"   ✅ {description}")
                    success_count += 1
                    self.restored_items.append(f"网络重置: {description}")
                else:
                    print(f"   ⚠️ {description} - 警告")
            except Exception as e:
                print(f"   ❌ {description} - 失败: {e}")
        
        return success_count
    
    def verify_system_state(self):
        """验证系统状态"""
        print("\n🔍 验证系统状态...")
        
        # 检查计算机名
        try:
            import socket
            current_hostname = socket.gethostname()
            print(f"   📋 当前主机名: {current_hostname}")
            
            # 检查API调用
            buffer_size = ctypes.wintypes.DWORD(256)
            buffer = ctypes.create_unicode_buffer(256)
            
            if ctypes.windll.kernel32.GetComputerNameW(buffer, ctypes.byref(buffer_size)):
                api_name = buffer.value
                print(f"   📋 API计算机名: {api_name}")
                
                if api_name == self.original_computer_name:
                    print(f"   ✅ 计算机名已恢复到原始值")
                else:
                    print(f"   ⚠️ 计算机名与原始值不匹配")
            
        except Exception as e:
            print(f"   ❌ 验证失败: {e}")
        
        # 检查网络连接
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('www.baidu.com', 80))
            sock.close()
            
            if result == 0:
                print(f"   ✅ 网络连接正常")
            else:
                print(f"   ⚠️ 网络连接可能有问题")
                
        except Exception as e:
            print(f"   ❌ 网络测试失败: {e}")
    
    def generate_restore_report(self):
        """生成恢复报告"""
        print("\n" + "="*60)
        print("📋 系统恢复报告")
        print("="*60)
        
        print(f"\n📊 恢复统计:")
        print(f"   恢复项目: {len(self.restored_items)} 个")
        
        if self.restored_items:
            print(f"\n✅ 已恢复的项目:")
            for i, item in enumerate(self.restored_items, 1):
                print(f"   {i}. {item}")
        
        # 保存报告
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'original_computer_name': self.original_computer_name,
            'restored_items': self.restored_items
        }
        
        report_file = f"system_restore_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    print("完整系统恢复工具")
    print("清理所有可能被修改的系统设置")
    print()
    
    restorer = CompleteSystemRestore()
    
    if not restorer.check_admin():
        print("❌ 需要管理员权限")
        print("请以管理员身份运行此脚本")
        input("按任意键退出...")
        return
    
    print("✅ 管理员权限确认")
    
    print(f"\n💡 此工具将恢复以下内容:")
    print(f"  - 计算机名称注册表项")
    print(f"  - 系统信息注册表项")
    print(f"  - DingTalk注册表项")
    print(f"  - 备份文件")
    print(f"  - 临时文件")
    print(f"  - 网络设置")
    
    print(f"\n⚠️ 注意事项:")
    print(f"  - 将恢复计算机名为: {restorer.original_computer_name}")
    print(f"  - 将清理所有DingTalk注册表项")
    print(f"  - 将重置网络配置")
    print(f"  - 建议重启电脑以确保完全生效")
    
    choice = input(f"\n是否继续完整恢复？(y/n): ").lower()
    
    if choice != 'y':
        print("操作取消")
        return
    
    print(f"\n🚀 开始完整系统恢复...")
    
    # 执行所有恢复操作
    total_restored = 0
    
    total_restored += restorer.restore_computer_name_registry()
    total_restored += restorer.restore_system_info_registry()
    total_restored += restorer.clean_dingtalk_registry()
    total_restored += restorer.clean_backup_files()
    total_restored += restorer.clean_temp_files()
    total_restored += restorer.reset_network_settings()
    
    # 验证系统状态
    restorer.verify_system_state()
    
    # 生成报告
    restorer.generate_restore_report()
    
    print(f"\n🎉 系统恢复完成！")
    print(f"📊 总共恢复/清理了 {total_restored} 个项目")
    
    print(f"\n💡 建议的后续步骤:")
    print(f"1. 重启电脑以确保所有更改生效")
    print(f"2. 重新安装QingTalk（使用官方安装包）")
    print(f"3. 测试QingTalk网络连接")
    
    restart_choice = input(f"\n是否立即重启电脑？(y/n): ").lower()
    
    if restart_choice == 'y':
        print(f"🔄 正在重启电脑...")
        subprocess.run(['shutdown', '/r', '/t', '10', '/c', '系统恢复完成，10秒后重启'])
    else:
        print(f"💡 请手动重启电脑以确保所有更改生效")
    
    input("\n按任意键退出...")

if __name__ == "__main__":
    main()
