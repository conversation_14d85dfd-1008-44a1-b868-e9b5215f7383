('D:\\text\\py\\duokai\\build\\fix_network_before_start\\fix_network_before_start.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('fix_network_before_start',
   'D:\\text\\py\\duokai\\fix_network_before_start.py',
   'PYSOURCE'),
  ('python311.dll',
   'D:\\Software\\develop\\Python311\\python311.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Software\\develop\\Python311\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Software\\develop\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Software\\develop\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Software\\develop\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('icon.ico', 'D:\\text\\py\\duokai\\icon.ico', 'DATA'),
  ('base_library.zip',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
