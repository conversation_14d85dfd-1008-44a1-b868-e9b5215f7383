#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk启动前网络修复工具
解决网络接口错误问题
"""

import os
import sys
import subprocess
import tempfile
import json
import time

def check_network_interfaces():
    """检查网络接口状态"""
    print("🔍 检查网络接口状态...")
    
    try:
        # 检查网络适配器
        result = subprocess.run(['ipconfig', '/all'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("  ✅ 网络适配器正常")
            return True
        else:
            print("  ⚠️ 网络适配器可能有问题")
            return False
            
    except Exception as e:
        print(f"  ❌ 检查网络接口失败: {e}")
        return False

def fix_network_for_qingtalk():
    """为QingTalk修复网络环境"""
    print("🔧 为QingTalk修复网络环境...")
    
    try:
        # 1. 刷新DNS
        print("  🔄 刷新DNS缓存...")
        subprocess.run(['ipconfig', '/flushdns'], 
                      capture_output=True, timeout=10)
        
        # 2. 重置网络栈
        print("  🔄 重置网络栈...")
        subprocess.run(['netsh', 'int', 'ip', 'reset'], 
                      capture_output=True, timeout=15)
        
        # 3. 创建临时网络配置
        print("  📁 创建临时网络配置...")
        temp_dir = tempfile.mkdtemp(prefix="qingtalk_network_")
        
        # 创建模拟的网络接口配置
        network_config = {
            "version": "1.0",
            "interfaces": {
                "Ethernet": {
                    "address": "*************",
                    "netmask": "*************",
                    "gateway": "***********",
                    "dns": ["*******", "*******"],
                    "mac": "00:15:5D:FF:FF:FF",
                    "type": "ethernet",
                    "status": "up"
                },
                "Loopback": {
                    "address": "127.0.0.1",
                    "netmask": "*********",
                    "mac": "00:00:00:00:00:00",
                    "type": "loopback",
                    "status": "up"
                }
            }
        }
        
        config_file = os.path.join(temp_dir, "network_interfaces.json")
        with open(config_file, 'w') as f:
            json.dump(network_config, f, indent=2)
        
        # 创建hosts文件
        hosts_content = """# QingTalk Network Fix
127.0.0.1 localhost
::1 localhost
127.0.0.1 local
"""
        hosts_file = os.path.join(temp_dir, "hosts")
        with open(hosts_file, 'w') as f:
            f.write(hosts_content)
        
        print(f"  ✅ 网络配置已创建: {temp_dir}")
        
        # 4. 设置环境变量
        os.environ['QINGTALK_NETWORK_FIX'] = temp_dir
        os.environ['TEMP_NETWORK_CONFIG'] = config_file
        
        return temp_dir
        
    except Exception as e:
        print(f"  ❌ 网络修复失败: {e}")
        return None

def main():
    print("QingTalk网络修复工具")
    print("=" * 50)
    
    # 检查管理员权限
    import ctypes
    if not ctypes.windll.shell32.IsUserAnAdmin():
        print("⚠️ 建议以管理员权限运行以获得最佳效果")
    
    # 检查网络状态
    network_ok = check_network_interfaces()
    
    if not network_ok:
        print("\n🔧 检测到网络问题，开始修复...")
        fix_result = fix_network_for_qingtalk()
        
        if fix_result:
            print("\n✅ 网络修复完成！")
            print("💡 现在可以启动QingTalk了")
        else:
            print("\n❌ 网络修复失败")
            print("💡 请尝试重启网络适配器或重启计算机")
    else:
        print("\n✅ 网络状态正常")
        print("💡 预防性应用网络修复...")
        fix_network_for_qingtalk()
    
    print("\n" + "=" * 50)
    print("修复完成，按回车键启动QingTalk多开工具...")
    time.sleep(3)
    
    # 启动主程序
    try:
        subprocess.run([sys.executable, 'main.py'])
    except Exception as e:
        print(f"启动主程序失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
