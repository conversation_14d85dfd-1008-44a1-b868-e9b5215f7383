#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析QingTalk登录状态存储位置
找出登录信息保存在哪里
"""

import os
import time
import json
from datetime import datetime

def analyze_qingtalk_login_storage():
    """分析QingTalk登录状态存储"""
    print("=" * 60)
    print("QingTalk登录状态存储分析")
    print("=" * 60)
    
    # 可能的存储位置
    possible_locations = [
        # 用户数据目录
        os.path.expanduser("~\\AppData\\Roaming\\QingTalk"),
        os.path.expanduser("~\\AppData\\Local\\QingTalk"),
        os.path.expanduser("~\\AppData\\LocalLow\\QingTalk"),
        
        # 程序目录
        "C:\\Program Files\\QingTalk\\QingTalk",
        "C:\\Program Files (x86)\\QingTalk\\QingTalk",
        
        # 系统目录
        os.path.expanduser("~\\Documents\\QingTalk"),
        "C:\\ProgramData\\QingTalk",
        
        # 临时目录
        os.path.expanduser("~\\AppData\\Local\\Temp"),
        
        # 注册表相关文件
        os.path.expanduser("~\\ntuser.dat"),
    ]
    
    print("🔍 检查可能的存储位置...")
    existing_locations = []
    
    for location in possible_locations:
        if os.path.exists(location):
            existing_locations.append(location)
            print(f"✅ 存在: {location}")
        else:
            print(f"❌ 不存在: {location}")
    
    if not existing_locations:
        print("\n❌ 没有找到QingTalk相关目录")
        return
    
    print(f"\n📂 找到 {len(existing_locations)} 个相关目录")
    
    # 分析每个目录的内容
    for location in existing_locations:
        print(f"\n" + "="*50)
        print(f"分析目录: {location}")
        print("="*50)
        
        try:
            analyze_directory(location)
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    print(f"\n" + "="*60)
    print("分析建议")
    print("="*60)
    
    print("🔍 登录状态可能存储在以下位置：")
    print("1. AppData\\Roaming\\QingTalk - 用户配置和登录token")
    print("2. AppData\\Local\\QingTalk - 本地缓存和会话数据")
    print("3. 程序目录 - 程序配置和许可证信息")
    print("4. 注册表 - 系统级配置")
    
    print("\n💡 建议的持久化策略：")
    print("1. 完整复制 AppData\\Roaming\\QingTalk")
    print("2. 完整复制 AppData\\Local\\QingTalk")
    print("3. 保持程序目录的完整性")
    print("4. 考虑注册表项的隔离")

def analyze_directory(path):
    """分析目录内容"""
    if not os.path.isdir(path):
        print(f"📄 文件: {os.path.basename(path)}")
        return
    
    try:
        items = os.listdir(path)
        print(f"📁 目录包含 {len(items)} 个项目:")
        
        # 按类型分类
        files = []
        dirs = []
        
        for item in items:
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                dirs.append(item)
            else:
                files.append(item)
        
        # 显示目录
        if dirs:
            print(f"  📁 子目录 ({len(dirs)}):")
            for d in sorted(dirs)[:10]:  # 只显示前10个
                print(f"    📁 {d}")
            if len(dirs) > 10:
                print(f"    ... 还有 {len(dirs) - 10} 个目录")
        
        # 显示文件
        if files:
            print(f"  📄 文件 ({len(files)}):")
            
            # 重点关注的文件类型
            important_files = []
            config_files = []
            data_files = []
            other_files = []
            
            for f in files:
                f_lower = f.lower()
                if any(keyword in f_lower for keyword in ['login', 'token', 'auth', 'session', 'user']):
                    important_files.append(f)
                elif any(ext in f_lower for ext in ['.json', '.xml', '.ini', '.cfg', '.conf']):
                    config_files.append(f)
                elif any(ext in f_lower for ext in ['.db', '.sqlite', '.dat', '.cache']):
                    data_files.append(f)
                else:
                    other_files.append(f)
            
            # 显示重要文件
            if important_files:
                print(f"    🔑 重要文件:")
                for f in sorted(important_files):
                    print(f"      🔑 {f}")
            
            # 显示配置文件
            if config_files:
                print(f"    ⚙️ 配置文件:")
                for f in sorted(config_files)[:5]:
                    print(f"      ⚙️ {f}")
                if len(config_files) > 5:
                    print(f"      ... 还有 {len(config_files) - 5} 个配置文件")
            
            # 显示数据文件
            if data_files:
                print(f"    💾 数据文件:")
                for f in sorted(data_files)[:5]:
                    print(f"      💾 {f}")
                if len(data_files) > 5:
                    print(f"      ... 还有 {len(data_files) - 5} 个数据文件")
            
            # 显示其他文件
            if other_files and len(other_files) <= 10:
                print(f"    📄 其他文件:")
                for f in sorted(other_files):
                    print(f"      📄 {f}")
            elif other_files:
                print(f"    📄 其他文件: {len(other_files)} 个")
        
        # 递归分析重要子目录
        important_subdirs = [d for d in dirs if any(keyword in d.lower() for keyword in ['config', 'data', 'cache', 'user'])]
        
        for subdir in important_subdirs[:3]:  # 只分析前3个重要子目录
            subdir_path = os.path.join(path, subdir)
            print(f"\n  🔍 深入分析: {subdir}")
            try:
                sub_items = os.listdir(subdir_path)
                print(f"    包含 {len(sub_items)} 个项目")
                
                # 显示重要文件
                for item in sub_items[:5]:
                    item_path = os.path.join(subdir_path, item)
                    if os.path.isfile(item_path):
                        print(f"      📄 {item}")
                    else:
                        print(f"      📁 {item}")
                
                if len(sub_items) > 5:
                    print(f"      ... 还有 {len(sub_items) - 5} 个项目")
                    
            except Exception as e:
                print(f"    ❌ 无法访问: {e}")
    
    except Exception as e:
        print(f"❌ 无法读取目录: {e}")

def main():
    print("QingTalk登录状态存储分析工具")
    
    analyze_qingtalk_login_storage()
    
    print("\n" + "="*60)
    print("分析完成")
    print("="*60)
    
    print("\n💡 下一步建议：")
    print("1. 在QingTalk中登录一个账号")
    print("2. 记录登录前后各目录的变化")
    print("3. 找出登录状态相关的文件")
    print("4. 在持久化方案中完整保存这些文件")

if __name__ == "__main__":
    main()
