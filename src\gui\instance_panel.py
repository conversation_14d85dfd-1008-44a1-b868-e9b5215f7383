#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实例管理面板
显示和管理所有实例
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from datetime import datetime

class InstancePanel:
    def __init__(self, parent, instance_manager, config_manager):
        self.parent = parent
        self.instance_manager = instance_manager
        self.config_manager = config_manager
        
        self.create_widgets()
        self.refresh_instances()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        self.toolbar = ttk.Frame(self.main_frame)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        
        # 工具栏按钮
        ttk.Button(
            self.toolbar, 
            text="刷新", 
            command=self.refresh_instances
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="启动选中", 
            command=self.start_selected
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="停止选中", 
            command=self.stop_selected
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="删除选中", 
            command=self.delete_selected
        ).pack(side=tk.LEFT, padx=2)
        
        # 创建实例列表
        self.create_instance_list()
    
    def create_instance_list(self):
        """创建实例列表"""
        # 创建框架
        list_frame = ttk.Frame(self.main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('ID', 'EXE路径', '状态', 'PID', '代理', '创建时间', '启动时间')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
        
        # 设置列宽
        self.tree.column('ID', width=100)
        self.tree.column('EXE路径', width=200)
        self.tree.column('状态', width=80)
        self.tree.column('PID', width=80)
        self.tree.column('代理', width=150)
        self.tree.column('创建时间', width=120)
        self.tree.column('启动时间', width=120)
        
        # 创建滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定事件
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Button-3>', self.on_right_click)
        
        # 创建右键菜单
        self.create_context_menu()
    
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="启动", command=self.start_selected)
        self.context_menu.add_command(label="停止", command=self.stop_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="查看详情", command=self.show_instance_details)
        #self.context_menu.add_command(label="修改设置", command=self.edit_instance)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除", command=self.delete_selected)
    

    
    def refresh_instances(self):
        """刷新实例列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加实例
        instances = self.instance_manager.get_all_instances()
        for instance in instances:
            self.add_instance_to_tree(instance)
    
    def add_instance_to_tree(self, instance):
        """添加实例到树形控件"""
        # 格式化时间
        created_time = instance.created_time.strftime('%Y-%m-%d %H:%M:%S')
        start_time = instance.start_time.strftime('%Y-%m-%d %H:%M:%S') if instance.start_time else '-'

        # 获取代理信息
        proxy_info = '-'
        if instance.proxy_config:
            if 'http_proxy' in instance.proxy_config:
                proxy_info = instance.proxy_config['http_proxy']
            elif 'id' in instance.proxy_config:
                proxy_info = f"代理ID: {instance.proxy_config['id'][:8]}..."
            else:
                proxy_info = "已配置代理"

        # 格式化状态显示
        status_display = instance.status
        if instance.status == 'running':
            status_display = '运行中'
        elif instance.status == 'stopped':
            status_display = '已停止'
        elif instance.status == 'starting':
            status_display = '启动中'
        elif instance.status == 'stopping':
            status_display = '停止中'

        # 添加到树形控件
        item = self.tree.insert('', tk.END, values=(
            instance.id[:8] + '...',  # 显示ID的前8位
            instance.exe_path,
            status_display,
            instance.pid or '-',
            proxy_info,
            created_time,
            start_time
        ), tags=(instance.id,))

        # 根据状态设置颜色标签
        if instance.status == 'running':
            self.tree.item(item, tags=(instance.id, 'running'))
        elif instance.status == 'stopped':
            self.tree.item(item, tags=(instance.id, 'stopped'))
        elif instance.status == 'starting':
            self.tree.item(item, tags=(instance.id, 'starting'))
        elif instance.status == 'stopping':
            self.tree.item(item, tags=(instance.id, 'stopping'))
    
    def get_selected_instance_id(self):
        """获取选中的实例ID"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = selection[0]
        tags = self.tree.item(item, 'tags')
        return tags[0] if tags else None
    
    def start_selected(self):
        """启动选中的实例"""
        instance_id = self.get_selected_instance_id()
        if not instance_id:
            messagebox.showwarning("警告", "请先选择一个实例")
            return
        
        def start_thread():
            try:
                success = self.instance_manager.start_instance(instance_id)
                if success:
                    self.parent.after(0, lambda: messagebox.showinfo("成功", "实例启动成功"))
                else:
                    self.parent.after(0, lambda: messagebox.showerror("错误", "实例启动失败"))
                
                self.parent.after(0, self.refresh_instances)
                
            except Exception as e:
                self.parent.after(0, lambda: messagebox.showerror("错误", f"启动实例失败: {str(e)}"))
        
        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_selected(self):
        """停止选中的实例"""
        instance_id = self.get_selected_instance_id()
        if not instance_id:
            messagebox.showwarning("警告", "请先选择一个实例")
            return
        
        try:
            success = self.instance_manager.stop_instance(instance_id)
            if success:
                messagebox.showinfo("成功", "实例停止成功")
            else:
                messagebox.showerror("错误", "实例停止失败")
            
            self.refresh_instances()
            
        except Exception as e:
            messagebox.showerror("错误", f"停止实例失败: {str(e)}")
    
    def delete_selected(self):
        """删除选中的实例"""
        instance_id = self.get_selected_instance_id()
        if not instance_id:
            messagebox.showwarning("警告", "请先选择一个实例")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的实例吗？"):
            try:
                success = self.instance_manager.remove_instance(instance_id)
                if success:
                    messagebox.showinfo("成功", "实例删除成功")
                    self.refresh_instances()
                else:
                    messagebox.showerror("错误", "实例删除失败")
                    
            except Exception as e:
                messagebox.showerror("错误", f"删除实例失败: {str(e)}")
    
    def show_instance_details(self):
        """显示实例详情"""
        instance_id = self.get_selected_instance_id()
        if not instance_id:
            messagebox.showwarning("警告", "请先选择一个实例")
            return
        
        instance = self.instance_manager.get_instance(instance_id)
        if not instance:
            messagebox.showerror("错误", "实例不存在")
            return
        
        # 创建详情窗口
        detail_window = tk.Toplevel(self.parent)
        detail_window.title(f"实例详情 - {instance_id[:8]}")
        detail_window.geometry("500x400")
        detail_window.resizable(False, False)
        
        # 显示详情信息
        info_text = tk.Text(detail_window, wrap=tk.WORD, padx=10, pady=10)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info = instance.get_info()
        detail_text = f"""
实例ID: {info['id']}
EXE路径: {info['exe_path']}
状态: {info['status']}
进程ID: {info['pid'] or '无'}
创建时间: {info['created_time']}
启动时间: {info['start_time'] or '未启动'}

代理配置:
{self.format_proxy_config(info['proxy_config'])}
        """
        
        info_text.insert(tk.END, detail_text)
        info_text.config(state=tk.DISABLED)
    
    def format_proxy_config(self, proxy_config):
        """格式化代理配置"""
        if not proxy_config:
            return "无代理配置"
        
        text = ""
        for key, value in proxy_config.items():
            text += f"  {key}: {value}\n"
        
        return text
    
    def edit_instance(self):
        """编辑实例设置"""
        # TODO: 实现实例设置编辑
        messagebox.showinfo("提示", "实例设置编辑功能待实现")
    
    def on_double_click(self, event):
        """双击事件"""
        self.show_instance_details()
    
    def on_right_click(self, event):
        """右键点击事件"""
        # 选中右键点击的项目
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
