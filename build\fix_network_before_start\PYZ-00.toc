('D:\\text\\py\\duokai\\build\\fix_network_before_start\\PYZ-00.pyz',
 [('_compat_pickle',
   'D:\\Software\\develop\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Software\\develop\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Software\\develop\\Python311\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Software\\develop\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Software\\develop\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Software\\develop\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'D:\\Software\\develop\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\Software\\develop\\Python311\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\Software\\develop\\Python311\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Software\\develop\\Python311\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Software\\develop\\Python311\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\Software\\develop\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Software\\develop\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Software\\develop\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Software\\develop\\Python311\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Software\\develop\\Python311\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Software\\develop\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Software\\develop\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Software\\develop\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Software\\develop\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal', 'D:\\Software\\develop\\Python311\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\Software\\develop\\Python311\\Lib\\dis.py', 'PYMODULE'),
  ('email',
   'D:\\Software\\develop\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Software\\develop\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Software\\develop\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Software\\develop\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Software\\develop\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Software\\develop\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Software\\develop\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Software\\develop\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Software\\develop\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Software\\develop\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Software\\develop\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Software\\develop\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Software\\develop\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Software\\develop\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Software\\develop\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Software\\develop\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Software\\develop\\Python311\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\Software\\develop\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('getopt', 'D:\\Software\\develop\\Python311\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Software\\develop\\Python311\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\Software\\develop\\Python311\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Software\\develop\\Python311\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Software\\develop\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Software\\develop\\Python311\\Lib\\inspect.py', 'PYMODULE'),
  ('json',
   'D:\\Software\\develop\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Software\\develop\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Software\\develop\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Software\\develop\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Software\\develop\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Software\\develop\\Python311\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\Software\\develop\\Python311\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Software\\develop\\Python311\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\Software\\develop\\Python311\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Software\\develop\\Python311\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Software\\develop\\Python311\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'D:\\Software\\develop\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('quopri', 'D:\\Software\\develop\\Python311\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Software\\develop\\Python311\\Lib\\random.py', 'PYMODULE'),
  ('selectors',
   'D:\\Software\\develop\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('shutil', 'D:\\Software\\develop\\Python311\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Software\\develop\\Python311\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Software\\develop\\Python311\\Lib\\socket.py', 'PYMODULE'),
  ('statistics',
   'D:\\Software\\develop\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Software\\develop\\Python311\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Software\\develop\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Software\\develop\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Software\\develop\\Python311\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile',
   'D:\\Software\\develop\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Software\\develop\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\Software\\develop\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('token', 'D:\\Software\\develop\\Python311\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\Software\\develop\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Software\\develop\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'D:\\Software\\develop\\Python311\\Lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Software\\develop\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Software\\develop\\Python311\\Lib\\zipfile.py', 'PYMODULE')])
