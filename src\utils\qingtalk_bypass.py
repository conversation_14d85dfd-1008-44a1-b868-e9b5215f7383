#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk专用绕过方案
基于60秒监控数据的精确分析结果
"""

import os
import subprocess
import tempfile
import shutil
import socket
import time
from typing import Optional

class QingTalkBypass:
    def __init__(self):
        self.qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
        self.original_userdata = r"C:/Program Files/QingTalk/QingTalk/userData"
    
    def method_1_lockfile_bypass(self, instance_id: str) -> Optional[subprocess.Popen]:
        """
        方法1: 锁文件绕过 - 最有效的方法
        基于发现的 lockfile 检测机制
        """
        print(f"🔓 方法1: 锁文件绕过 (实例: {instance_id})")
        
        try:
            # 1. 创建独立的QingTalk副本
            base_dir = tempfile.mkdtemp(prefix=f"qingtalk_bypass_{instance_id}_")
            qingtalk_dir = os.path.join(base_dir, "QingTalk")
            
            print(f"  📂 复制QingTalk到: {qingtalk_dir}")
            shutil.copytree(os.path.dirname(self.qingtalk_exe), qingtalk_dir)
            
            isolated_exe = os.path.join(qingtalk_dir, "QingTalk.exe")
            
            # 2. 创建独立的userData目录
            isolated_userdata = os.path.join(qingtalk_dir, "userData")
            
            # 如果原始userData存在，复制基础配置
            if os.path.exists(self.original_userdata):
                print(f"  📋 复制基础配置...")
                # 只复制必要的配置文件，不复制lockfile
                for item in os.listdir(self.original_userdata):
                    src_path = os.path.join(self.original_userdata, item)
                    dst_path = os.path.join(isolated_userdata, item)
                    
                    # 跳过锁文件和临时文件
                    if item in ['lockfile', 'SingletonLock', 'SingletonSocket', 'SingletonCookie']:
                        print(f"    ⏭️ 跳过锁文件: {item}")
                        continue
                    
                    try:
                        if os.path.isfile(src_path) and not item.endswith('.tmp'):
                            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                            shutil.copy2(src_path, dst_path)
                        elif os.path.isdir(src_path):
                            shutil.copytree(src_path, dst_path, ignore=shutil.ignore_patterns('*.tmp', '*.lock'), dirs_exist_ok=True)
                    except Exception as e:
                        print(f"    ⚠️ 跳过复制失败的项目 {item}: {e}")
            
            # 3. 设置环境变量
            env = os.environ.copy()
            env.update({
                'QINGTALK_USER_DATA': isolated_userdata,
                'INSTANCE_ID': instance_id,
                'LOCKFILE_BYPASS': '1'
            })
            
            # 4. 启动隔离的QingTalk
            print(f"  🚀 启动隔离的QingTalk...")
            process = subprocess.Popen(
                isolated_exe,
                env=env,
                cwd=qingtalk_dir,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 锁文件绕过启动成功 (PID: {process.pid})")
            
            # 保存清理信息
            process.cleanup_info = {
                'base_dir': base_dir,
                'instance_id': instance_id,
                'method': 'lockfile_bypass'
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 锁文件绕过失败: {e}")
            return None
    
    def method_2_port_isolation(self, instance_id: str) -> Optional[subprocess.Popen]:
        """
        方法2: 端口隔离
        基于发现的 127.0.0.1:34519 通信端口
        """
        print(f"🌐 方法2: 端口隔离 (实例: {instance_id})")
        
        try:
            # 1. 找到可用的端口范围
            # 安全地从instance_id生成端口号
            port_seed = sum(ord(c) for c in instance_id) % 1000
            base_port = 35000 + port_seed
            
            # 2. 设置网络隔离环境
            env = os.environ.copy()
            env.update({
                'QINGTALK_PORT_BASE': str(base_port),
                'NETWORK_ISOLATION': '1',
                'INSTANCE_ID': instance_id,
                'BIND_ADDRESS': f'127.0.0.{(sum(ord(c) for c in instance_id[-2:]) % 254) + 1}'  # 使用不同的回环地址
            })
            
            # 3. 创建独立的用户数据目录
            user_data_dir = tempfile.mkdtemp(prefix=f"qingtalk_port_{instance_id}_")
            
            # 4. 启动QingTalk并指定用户数据目录
            command = [
                self.qingtalk_exe,
                f'--user-data-dir={user_data_dir}',
                '--no-sandbox',
                '--disable-dev-shm-usage'
            ]
            
            print(f"  🚀 启动命令: {' '.join(command)}")
            process = subprocess.Popen(
                command,
                env=env,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 端口隔离启动成功 (PID: {process.pid})")
            
            process.cleanup_info = {
                'user_data_dir': user_data_dir,
                'instance_id': instance_id,
                'method': 'port_isolation'
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 端口隔离失败: {e}")
            return None
    
    def method_3_userdata_redirection(self, instance_id: str) -> Optional[subprocess.Popen]:
        """
        方法3: 用户数据重定向
        基于发现的固定userData目录冲突
        """
        print(f"📁 方法3: 用户数据重定向 (实例: {instance_id})")
        
        try:
            # 1. 创建独立的用户数据目录
            user_data_dir = tempfile.mkdtemp(prefix=f"qingtalk_userdata_{instance_id}_")
            
            # 2. 设置重定向环境变量
            env = os.environ.copy()
            
            # 重定向所有可能的用户数据路径
            env.update({
                'APPDATA': os.path.join(user_data_dir, 'AppData', 'Roaming'),
                'LOCALAPPDATA': os.path.join(user_data_dir, 'AppData', 'Local'),
                'USERPROFILE': user_data_dir,
                'TEMP': os.path.join(user_data_dir, 'Temp'),
                'TMP': os.path.join(user_data_dir, 'Temp'),
                'INSTANCE_ID': instance_id,
                'USERDATA_REDIRECT': '1'
            })
            
            # 创建必要的目录结构
            for path in [env['APPDATA'], env['LOCALAPPDATA'], env['TEMP']]:
                os.makedirs(path, exist_ok=True)
            
            # 3. 启动QingTalk
            process = subprocess.Popen(
                self.qingtalk_exe,
                env=env,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 用户数据重定向启动成功 (PID: {process.pid})")
            
            process.cleanup_info = {
                'user_data_dir': user_data_dir,
                'instance_id': instance_id,
                'method': 'userdata_redirection'
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 用户数据重定向失败: {e}")
            return None
    
    def method_4_combined_approach(self, instance_id: str) -> Optional[subprocess.Popen]:
        """
        方法4: 综合方法 - 结合所有发现的绕过技术
        """
        print(f"🎯 方法4: 综合绕过方法 (实例: {instance_id})")
        
        try:
            # 1. 创建完全隔离的环境
            base_dir = tempfile.mkdtemp(prefix=f"qingtalk_combined_{instance_id}_")
            
            # 2. 复制QingTalk程序
            qingtalk_dir = os.path.join(base_dir, "QingTalk")
            shutil.copytree(os.path.dirname(self.qingtalk_exe), qingtalk_dir)
            isolated_exe = os.path.join(qingtalk_dir, "QingTalk.exe")
            
            # 3. 创建独立的用户数据目录
            user_data_dir = os.path.join(base_dir, "UserData")
            os.makedirs(user_data_dir, exist_ok=True)
            
            # 4. 设置综合环境变量
            env = os.environ.copy()
            env.update({
                # 用户数据重定向
                'APPDATA': os.path.join(user_data_dir, 'AppData', 'Roaming'),
                'LOCALAPPDATA': os.path.join(user_data_dir, 'AppData', 'Local'),
                'USERPROFILE': user_data_dir,
                'TEMP': os.path.join(user_data_dir, 'Temp'),
                'TMP': os.path.join(user_data_dir, 'Temp'),
                
                # 网络隔离
                'QINGTALK_PORT_BASE': str(35000 + sum(ord(c) for c in instance_id) % 1000),
                'BIND_ADDRESS': f'127.0.0.{(sum(ord(c) for c in instance_id[-2:]) % 254) + 1}',
                
                # 进程隔离
                'INSTANCE_ID': instance_id,
                'PROCESS_ISOLATION': '1',
                'LOCKFILE_BYPASS': '1',
                'USERDATA_REDIRECT': '1',
                'NETWORK_ISOLATION': '1',
                
                # 系统标识
                'COMPUTERNAME': f'QINGTALK-{instance_id.upper()[:8]}',
                'USERNAME': f'QTUser_{instance_id[:8]}'
            })
            
            # 创建目录结构
            for path in [env['APPDATA'], env['LOCALAPPDATA'], env['TEMP']]:
                os.makedirs(path, exist_ok=True)
            
            # 5. 启动隔离的QingTalk
            process = subprocess.Popen(
                isolated_exe,
                env=env,
                cwd=qingtalk_dir,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            print(f"  ✅ 综合绕过启动成功 (PID: {process.pid})")
            
            process.cleanup_info = {
                'base_dir': base_dir,
                'instance_id': instance_id,
                'method': 'combined_approach'
            }
            
            return process
            
        except Exception as e:
            print(f"  ❌ 综合绕过失败: {e}")
            return None
    
    def test_all_methods(self):
        """测试所有绕过方法"""
        print("=" * 60)
        print("QingTalk专用绕过方案测试")
        print("基于60秒监控数据的精确分析")
        print("=" * 60)
        
        methods = [
            ("锁文件绕过", self.method_1_lockfile_bypass),
            ("端口隔离", self.method_2_port_isolation),
            ("用户数据重定向", self.method_3_userdata_redirection),
            ("综合方法", self.method_4_combined_approach)
        ]
        
        results = {}
        
        for method_name, method_func in methods:
            print(f"\n🧪 测试方法: {method_name}")
            print("-" * 40)
            
            try:
                # 启动进程
                process = method_func(f"test{len(results)+1}")
                
                if process:
                    # 等待10秒检查状态
                    time.sleep(10)
                    
                    if process.poll() is None:
                        print(f"  ✅ 方法 '{method_name}' 成功 - 进程运行中")
                        results[method_name] = {
                            'success': True,
                            'pid': process.pid,
                            'duration': 10
                        }
                        
                        # 停止进程
                        process.terminate()
                        process.wait(timeout=10)
                        print(f"  🛑 进程已停止")
                    else:
                        print(f"  ❌ 方法 '{method_name}' 失败 - 进程已退出")
                        results[method_name] = {
                            'success': False,
                            'reason': 'Process exited early'
                        }
                    
                    # 清理
                    self.cleanup_method(process)
                else:
                    print(f"  ❌ 方法 '{method_name}' 失败 - 无法启动")
                    results[method_name] = {
                        'success': False,
                        'reason': 'Failed to start'
                    }
                    
            except Exception as e:
                print(f"  ❌ 方法 '{method_name}' 异常: {e}")
                results[method_name] = {
                    'success': False,
                    'reason': str(e)
                }
        
        # 生成测试报告
        self.generate_test_report(results)
        
        return results
    
    def cleanup_method(self, process):
        """清理方法资源"""
        try:
            if hasattr(process, 'cleanup_info'):
                cleanup_info = process.cleanup_info
                
                # 清理基础目录
                base_dir = cleanup_info.get('base_dir')
                if base_dir and os.path.exists(base_dir):
                    shutil.rmtree(base_dir, ignore_errors=True)
                    print(f"  🧹 清理完成: {base_dir}")
                
                # 清理用户数据目录
                user_data_dir = cleanup_info.get('user_data_dir')
                if user_data_dir and os.path.exists(user_data_dir):
                    shutil.rmtree(user_data_dir, ignore_errors=True)
                    print(f"  🧹 清理完成: {user_data_dir}")
                    
        except Exception as e:
            print(f"  ⚠️ 清理失败: {e}")
    
    def generate_test_report(self, results):
        """生成测试报告"""
        print(f"\n" + "=" * 60)
        print("QingTalk绕过方案测试报告")
        print("=" * 60)
        
        successful_methods = [name for name, result in results.items() if result.get('success')]
        failed_methods = [name for name, result in results.items() if not result.get('success')]
        
        print(f"\n📊 测试结果:")
        print(f"  成功方法: {len(successful_methods)}/{len(results)}")
        print(f"  失败方法: {len(failed_methods)}/{len(results)}")
        
        if successful_methods:
            print(f"\n✅ 成功的方法:")
            for method in successful_methods:
                result = results[method]
                print(f"  - {method} (PID: {result.get('pid')}, 运行: {result.get('duration')}秒)")
            
            print(f"\n🎯 推荐使用:")
            print(f"  最佳方法: {successful_methods[0]}")
            if len(successful_methods) > 1:
                print(f"  备选方法: {', '.join(successful_methods[1:])}")
        
        if failed_methods:
            print(f"\n❌ 失败的方法:")
            for method in failed_methods:
                result = results[method]
                print(f"  - {method}: {result.get('reason', 'Unknown')}")
        
        if not successful_methods:
            print(f"\n⚠️ 所有方法都失败了")
            print(f"  可能的原因:")
            print(f"  1. QingTalk有更深层的检测机制")
            print(f"  2. 需要更高级的隔离技术")
            print(f"  3. 建议使用虚拟机完全隔离")

def test_qingtalk_bypass():
    """测试QingTalk绕过方案"""
    bypass = QingTalkBypass()
    
    if not os.path.exists(bypass.qingtalk_exe):
        print(f"❌ QingTalk不存在: {bypass.qingtalk_exe}")
        return
    
    print("QingTalk专用绕过方案测试")
    print("基于60秒监控数据的精确分析结果")
    
    results = bypass.test_all_methods()
    
    return results

if __name__ == "__main__":
    test_qingtalk_bypass()
