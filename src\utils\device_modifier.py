#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的设备信息修改模块
从qingtalk_ultimate.py中提取出来，作为可选功能
"""

import os
import subprocess
import winreg
import tempfile
import shutil
import time
import random
import hashlib
import json
from typing import Dict, Optional

class DeviceModifier:
    """设备信息修改器 - 独立模块"""
    
    _instance = None
    _is_modified = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.backup_values = {}
            self.temp_files = []
            self.backup_file = os.path.join(os.getcwd(), "device_backup.json")
            self._initialized = True
            # 尝试加载之前的备份
            self._load_backup()
    
    def generate_fake_device_info(self) -> Dict[str, str]:
        """生成伪造的设备信息（确保长度与原名称一致，避免缓冲区溢出）"""
        # 获取原始计算机名和用户名
        original_computer = os.environ.get('COMPUTERNAME', 'UNKNOWN')
        original_user = os.environ.get('USERNAME', 'User')

        print(f"    📏 原始计算机名: '{original_computer}' (长度: {len(original_computer)})")
        print(f"    📏 原始用户名: '{original_user}' (长度: {len(original_user)})")

        # 使用当前时间戳作为种子，增加随机性
        timestamp = str(int(time.time() * 1000))
        seed_value = int(hashlib.md5(timestamp.encode()).hexdigest()[:8], 16)
        random.seed(seed_value)

        # 生成与原名称长度完全一致的假名称
        fake_computer = self._generate_same_length_name(original_computer)
        fake_user = self._generate_same_length_name(original_user)

        print(f"    🎯 生成计算机名: '{fake_computer}' (长度: {len(fake_computer)})")
        print(f"    🎯 生成用户名: '{fake_user}' (长度: {len(fake_user)})")

        # 验证长度（关键！避免缓冲区溢出）
        if len(fake_computer) != len(original_computer):
            raise ValueError(f"计算机名长度不匹配: 原始{len(original_computer)} vs 生成{len(fake_computer)}")
        if len(fake_user) != len(original_user):
            raise ValueError(f"用户名长度不匹配: 原始{len(original_user)} vs 生成{len(fake_user)}")

        print(f"    ✅ 长度验证通过，避免缓冲区溢出")

        # 生成其他设备信息
        random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=8))

        manufacturers = ['Dell Inc.', 'HP', 'Lenovo', 'ASUS', 'Acer', 'MSI', 'Gigabyte', 'ASRock']
        processors = [
            'Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz',
            'Intel(R) Core(TM) i7-8700 CPU @ 3.20GHz',
            'Intel(R) Core(TM) i5-10400 CPU @ 2.90GHz',
            'Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz',
            'Intel(R) Core(TM) i5-11400 CPU @ 2.60GHz',
            'Intel(R) Core(TM) i7-11700 CPU @ 2.50GHz',
            'AMD Ryzen 5 3600 6-Core Processor',
            'AMD Ryzen 7 3700X 8-Core Processor',
        ]

        bios_vendors = ['American Megatrends Inc.', 'Phoenix Technologies', 'Insyde Corp.', 'Award Software']

        fake_info = {
            'computer': fake_computer,
            'user': fake_user,
            'manufacturer': random.choice(manufacturers),
            'product': f'Model-{random_suffix}',
            'processor': random.choice(processors),
            'cpu_id': f'x86 Family 6 Model {random.randint(140,200)} Stepping {random.randint(1,15)}',
            'bios_vendor': random.choice(bios_vendors),
        }

        return fake_info

    def _generate_same_length_name(self, original_name: str) -> str:
        """生成与原名称长度完全一致的新名称"""
        if not original_name:
            return original_name

        # 分析原名称的结构
        name_length = len(original_name)

        # 如果名称包含连字符，保持相同的结构
        if '-' in original_name:
            parts = original_name.split('-')
            if len(parts) == 2:
                prefix_len = len(parts[0])
                suffix_len = len(parts[1])

                # 生成相同长度的前缀和后缀
                new_prefix = self._generate_random_string(prefix_len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ')
                new_suffix = self._generate_random_string(suffix_len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')

                return f"{new_prefix}-{new_suffix}"

        # 如果没有连字符，直接生成相同长度的随机字符串
        # 保持原名称的字符类型分布
        char_types = self._analyze_char_types(original_name)
        return self._generate_by_char_types(char_types)

    def _generate_random_string(self, length: int, charset: str) -> str:
        """生成指定长度的随机字符串"""
        if length <= 0:
            return ""
        return ''.join(random.choices(charset, k=length))

    def _analyze_char_types(self, name: str) -> list:
        """分析名称的字符类型分布"""
        char_types = []
        for char in name:
            if char.isupper():
                char_types.append('UPPER')
            elif char.islower():
                char_types.append('LOWER')
            elif char.isdigit():
                char_types.append('DIGIT')
            else:
                char_types.append('SPECIAL')
        return char_types

    def _generate_by_char_types(self, char_types: list) -> str:
        """根据字符类型分布生成新名称"""
        result = []
        for char_type in char_types:
            if char_type == 'UPPER':
                result.append(random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ'))
            elif char_type == 'LOWER':
                result.append(random.choice('abcdefghijklmnopqrstuvwxyz'))
            elif char_type == 'DIGIT':
                result.append(random.choice('0123456789'))
            else:  # SPECIAL
                result.append(random.choice('-_'))
        return ''.join(result)
    
    def modify_device_info(self) -> bool:
        """修改设备信息（完整版本）"""
        try:
            # 检查管理员权限
            import ctypes
            if not ctypes.windll.shell32.IsUserAnAdmin():
                print("❌ 需要管理员权限来修改设备信息")
                return False

            # 如果已经修改过，先恢复
            if self._is_modified:
                print("🔄 检测到已修改过设备信息，先恢复原始信息...")
                self.restore_device_info()

            # 生成新的设备信息
            fake_info = self.generate_fake_device_info()
            fake_computer = fake_info['computer']
            fake_user = fake_info['user']

            print(f"🔧 开始长度安全的完整设备信息修改...")
            print(f"  🎯 目标设备信息（长度安全）:")
            print(f"    计算机名: {fake_computer} (长度: {len(fake_computer)})")
            print(f"    用户名: {fake_user} (长度: {len(fake_user)})")
            print(f"    制造商: {fake_info['manufacturer']}")
            print(f"    产品型号: {fake_info['product']}")
            print(f"  🛡️ 所有名称长度与原名称保持一致，避免缓冲区溢出")

            # 1. 修改所有计算机名信息
            print(f"  🖥️ 修改计算机名信息...")
            computer_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", "ComputerName"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Hostname"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Hostname"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOwner"),
            ]

            success_count = 0
            for hkey, subkey, value_name in computer_keys:
                if self._backup_and_modify_registry(hkey, subkey, value_name, fake_computer):
                    success_count += 1

            print(f"    📊 计算机名修改: {success_count}/{len(computer_keys)} 项成功")

            # 2. 修改硬件信息
            print(f"  💻 修改硬件信息...")
            hardware_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemManufacturer", fake_info['manufacturer']),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "SystemProductName", fake_info['product']),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS", "BIOSVendor", fake_info['bios_vendor']),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0", "ProcessorNameString", fake_info['processor']),
                (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0", "Identifier", fake_info['cpu_id']),
            ]

            hardware_count = 0
            for hkey, subkey, value_name, new_value in hardware_keys:
                if self._backup_and_modify_registry(hkey, subkey, value_name, new_value):
                    hardware_count += 1

            print(f"    📊 硬件信息修改: {hardware_count}/{len(hardware_keys)} 项成功")

            # 3. 修改环境变量
            print(f"  🌍 修改环境变量...")

            # 备份环境变量
            if not hasattr(self, 'env_backup'):
                self.env_backup = {}

            env_vars_to_modify = {
                'COMPUTERNAME': fake_computer,
                'USERNAME': fake_user,
                'USERDOMAIN': fake_computer,
                'LOGONSERVER': f'\\\\{fake_computer}',
                'USERDNSDOMAIN': fake_computer,
                'CLIENTNAME': fake_computer
            }

            env_modified = 0
            for var_name, new_value in env_vars_to_modify.items():
                try:
                    # 备份原值
                    if var_name in os.environ:
                        self.env_backup[var_name] = os.environ[var_name]
                    else:
                        self.env_backup[var_name] = None

                    # 设置新值
                    os.environ[var_name] = new_value
                    env_modified += 1
                    print(f"    ✅ {var_name} = {new_value}")

                except Exception as e:
                    print(f"    ❌ 修改环境变量 {var_name} 失败: {e}")

            print(f"    📊 环境变量修改: {env_modified}/{len(env_vars_to_modify)} 项成功")

            # 4. 创建临时系统文件
            print(f"  📁 创建临时系统文件...")
            temp_dir = tempfile.mkdtemp(prefix="qingtalk_device_")

            try:
                # 创建伪造的系统文件
                computer_file = os.path.join(temp_dir, "computername")
                with open(computer_file, 'w', encoding='utf-8') as f:
                    f.write(fake_computer)

                user_file = os.path.join(temp_dir, "username")
                with open(user_file, 'w', encoding='utf-8') as f:
                    f.write(fake_user)

                # 设置环境变量指向临时文件
                os.environ['QINGTALK_COMPUTER_NAME'] = fake_computer
                os.environ['QINGTALK_USER_NAME'] = fake_user
                os.environ['TEMP_COMPUTER_FILE'] = computer_file
                os.environ['TEMP_USER_FILE'] = user_file

            except Exception as e:
                print(f"    ❌ 创建临时文件失败: {e}")

            self.temp_files.append(temp_dir)
            print(f"    ✅ 创建临时系统文件: {temp_dir}")

            self._is_modified = True
            total_success = success_count + hardware_count + env_modified + 1  # +1 for system files
            print(f"  ✅ 修改完成！成功修改 {total_success} 项")
            print(f"    📊 详细统计:")
            print(f"      🖥️ 计算机名: {success_count} 项")
            print(f"      💻 硬件信息: {hardware_count} 项")
            print(f"      🌍 环境变量: {env_modified} 项")
            print(f"      📁 系统文件: 1 项")

            # 保存备份数据
            self._save_backup()

            # 5. 等待修改生效
            print(f"  ⏳ 等待5秒让修改生效...")
            time.sleep(5)

            # 6. 验证修改结果
            print(f"  🔍 验证修改结果:")
            print(f"    环境变量 COMPUTERNAME: {os.environ.get('COMPUTERNAME')}")
            print(f"    Python platform.node(): {__import__('platform').node()}")

            try:
                result = subprocess.run(['hostname'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"    命令行 hostname: {result.stdout.strip()}")
            except:
                pass

            print(f"  🎯 设备信息修改成功！现在启动的QingTalk将使用新设备信息")
            return True

        except Exception as e:
            print(f"❌ 设备信息修改失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def modify_computer_name_only(self) -> bool:
        """只修改GetComputerNameEx相关的计算机名（最安全的轻量级版本）"""
        try:
            # 检查管理员权限
            import ctypes
            if not ctypes.windll.shell32.IsUserAnAdmin():
                print("❌ 需要管理员权限来修改计算机名")
                return False

            # 如果已经修改过，先恢复
            if self._is_modified:
                print("🔄 检测到已修改过设备信息，先恢复原始信息...")
                self.restore_device_info()

            # 生成新的设备信息（长度安全）
            fake_info = self.generate_fake_device_info()
            fake_computer = fake_info['computer']
            fake_user = fake_info['user']

            print(f"🔧 开始长度安全的轻量级修改（包含UI立即生效的关键项）...")
            print(f"  🎯 目标计算机名: {fake_computer}")
            print(f"  🎯 目标用户名: {fake_user}")
            print(f"  💡 修改8个关键注册表项，覆盖所有可能的获取方式")
            print(f"  🛡️ 保持长度一致，避免缓冲区溢出")

            # 修改所有可能被软件读取的计算机名注册表项
            print(f"  🖥️ 修改所有计算机名相关注册表项...")
            computer_keys = [
                # 核心计算机名（GetComputerNameEx主要读取位置）
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName", "ComputerName"),
                # 活动计算机名（修改后UI立即生效，这是关键！）
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName", "ComputerName"),
                # TCP/IP参数（影响系统UI显示和网络查询）
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "Hostname"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", "NV Hostname"),
                # 注册所有者（影响系统设置显示）
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion", "RegisteredOwner"),
                # 系统环境变量（可能被直接读取）
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment", "COMPUTERNAME"),
                # 网络服务相关（可能被WMI查询）
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\lanmanserver\parameters", "srvcomment"),
                # 工作站服务（影响网络标识）
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters", "ComputerName"),
            ]

            success_count = 0
            for hkey, subkey, value_name in computer_keys:
                if self._backup_and_modify_registry(hkey, subkey, value_name, fake_computer):
                    success_count += 1

            print(f"    📊 注册表项修改: {success_count}/{len(computer_keys)} 项成功")
            print(f"      ✅ ComputerName: GetComputerNameEx数据源")
            print(f"      ✅ ActiveComputerName: UI立即生效的关键")
            print(f"      ✅ Tcpip\\Parameters: 网络和WMI查询")
            print(f"      ✅ RegisteredOwner: 系统设置显示")
            print(f"      ✅ Environment: 系统环境变量")
            print(f"      ✅ LanmanServer/Workstation: 网络服务")

            # 修改关键环境变量（包含UI显示相关的变量）
            print(f"  🌍 修改关键环境变量...")

            # 备份环境变量
            if not hasattr(self, 'env_backup'):
                self.env_backup = {}

            # 修改影响UI显示的关键环境变量
            env_vars_to_modify = {
                'COMPUTERNAME': fake_computer,
                'USERNAME': fake_user,
                'USERDOMAIN': fake_computer,
                'LOGONSERVER': f'\\\\{fake_computer}',
                'CLIENTNAME': fake_computer,
            }

            env_modified = 0
            for var_name, new_value in env_vars_to_modify.items():
                try:
                    # 备份原值
                    if var_name in os.environ:
                        self.env_backup[var_name] = os.environ[var_name]
                    else:
                        self.env_backup[var_name] = None

                    # 设置新值
                    os.environ[var_name] = new_value
                    env_modified += 1
                    print(f"    ✅ {var_name} = {new_value}")

                except Exception as e:
                    print(f"    ❌ 修改环境变量 {var_name} 失败: {e}")

            print(f"    📊 环境变量修改: {env_modified}/{len(env_vars_to_modify)} 项成功")

            # 4. 创建临时系统文件（最小化）
            print(f"  📁 创建临时系统文件...")
            temp_dir = tempfile.mkdtemp(prefix="qingtalk_light_")

            # 只创建基本的计算机名文件
            try:
                computer_file = os.path.join(temp_dir, "computername")
                with open(computer_file, 'w', encoding='utf-8') as f:
                    f.write(fake_computer)

                # 设置环境变量指向临时文件
                os.environ['QINGTALK_COMPUTER_NAME'] = fake_computer
                os.environ['TEMP_COMPUTER_FILE'] = computer_file

            except Exception as e:
                print(f"    ❌ 创建临时文件失败: {e}")

            self.temp_files.append(temp_dir)
            print(f"    ✅ 创建临时系统文件: {temp_dir}")

            self._is_modified = True
            total_success = success_count + env_modified + 1  # +1 for system files
            print(f"  ✅ 轻量级修改完成！成功修改 {total_success} 项")
            print(f"    📊 详细统计:")
            print(f"      🖥️ 计算机名: {success_count} 项")
            print(f"      🌍 环境变量: {env_modified} 项")
            print(f"      📁 系统文件: 1 项")
            print(f"    💡 影响范围：主要影响GetComputerNameEx函数和基本环境变量")

            # 保存备份数据
            self._save_backup()

            # 5. 立即生效机制
            print(f"  ⚡ 应用强力立即生效机制...")
            self._apply_immediate_effect(fake_computer)

            # 6. 额外的系统刷新
            print(f"  🔄 执行额外的系统刷新...")
            self._force_system_refresh()

            # 7. 等待修改完全生效（服务重启需要更多时间）
            print(f"  ⏳ 等待15秒让修改和服务重启完全生效...")
            time.sleep(5)

            # 6. 验证修改结果
            print(f"  🔍 验证修改结果:")
            print(f"    环境变量 COMPUTERNAME: {os.environ.get('COMPUTERNAME')}")
            print(f"    Python platform.node(): {__import__('platform').node()}")

            try:
                result = subprocess.run(['hostname'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"    命令行 hostname: {result.stdout.strip()}")
            except:
                pass

            print(f"  🎯 长度安全的轻量级修改成功！")
            print(f"  💡 现在启动的QingTalk将使用新计算机名")
            print(f"  🖥️ 系统UI（设置->关于->设备名称）应该立即显示新名称")
            print(f"  🛡️ 长度一致，避免缓冲区溢出和网络错误")
            return True

        except Exception as e:
            print(f"❌ 轻量级设备信息修改失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _apply_immediate_effect(self, fake_computer):
        """应用超强力的立即生效机制，强制刷新所有缓存"""
        try:
            print(f"    🔄 应用强力立即生效机制...")

            # 1. 强制刷新系统计算机名缓存（多种API调用）
            try:
                import ctypes
                from ctypes import wintypes

                # 方式1: SetComputerNameExW API (多种类型)
                name_types = [0, 1, 4, 5]  # NetBIOS, DnsHostname, PhysicalNetBIOS, PhysicalDnsHostname
                for name_type in name_types:
                    success = ctypes.windll.kernel32.SetComputerNameExW(name_type, fake_computer)
                    if success:
                        print(f"      ✅ SetComputerNameExW({name_type}) API调用成功")

                # 方式2: SetComputerNameW强制刷新
                success2 = ctypes.windll.kernel32.SetComputerNameW(fake_computer)
                if success2:
                    print(f"      ✅ SetComputerNameW API调用成功")

            except Exception as e:
                print(f"      ⚠️ API调用失败: {e}")

            # 2. 重启关键系统服务（解决缓存问题的关键！）
            try:
                print(f"      🔄 重启关键系统服务（解决WMI和API缓存）...")

                services_to_restart = [
                    'Winmgmt',      # WMI服务 - 解决WMI缓存问题的关键！
                    'Workstation',  # 工作站服务 - 影响网络计算机名显示
                ]

                for service in services_to_restart:
                    try:
                        print(f"        🔄 重启服务: {service}")
                        # 停止服务（加/y参数自动确认）
                        result = subprocess.run(['net', 'stop', service, '/y'],
                                              capture_output=True, timeout=25, check=False)
                        # 等待服务完全停止
                        time.sleep(2)
                        # 启动服务
                        result = subprocess.run(['net', 'start', service],
                                              capture_output=True, timeout=25, check=False)
                        if result.returncode == 0:
                            print(f"        ✅ 服务 {service} 重启成功")
                        else:
                            print(f"        ⚠️ 服务 {service} 重启可能失败")
                    except Exception as e:
                        print(f"        ⚠️ 重启服务 {service} 失败: {e}")

                print(f"      💡 服务重启完成，这是解决'有时候可以有时候不行'的关键！")

            except Exception as e:
                print(f"      ⚠️ 服务重启失败: {e}")

            # 3. 发送系统通知
            try:
                print(f"      🔄 发送系统通知...")
                # 发送WM_SETTINGCHANGE消息通知系统环境变量更改
                HWND_BROADCAST = 0xFFFF
                WM_SETTINGCHANGE = 0x001A
                SMTO_ABORTIFHUNG = 0x0002

                # 通知环境变量更改
                result1 = ctypes.windll.user32.SendMessageTimeoutW(
                    HWND_BROADCAST,
                    WM_SETTINGCHANGE,
                    0,
                    "Environment",
                    SMTO_ABORTIFHUNG,
                    5000,
                    None
                )

                # 通知系统参数更改
                result2 = ctypes.windll.user32.SendMessageTimeoutW(
                    HWND_BROADCAST,
                    WM_SETTINGCHANGE,
                    0,
                    "intl",
                    SMTO_ABORTIFHUNG,
                    5000,
                    None
                )

                if result1:
                    print(f"      ✅ 环境变量更改通知已发送")
                if result2:
                    print(f"      ✅ 系统参数更改通知已发送")

            except Exception as e:
                print(f"      ⚠️ 发送系统通知失败: {e}")

            # 3. 强制刷新系统缓存
            try:
                # 刷新DNS缓存（安全操作）
                subprocess.run(['ipconfig', '/flushdns'],
                              capture_output=True, timeout=10, check=False)
                print(f"      ✅ DNS缓存已刷新")

                # 刷新NetBIOS名称缓存
                subprocess.run(['nbtstat', '-R'],
                              capture_output=True, timeout=10, check=False)
                print(f"      ✅ NetBIOS缓存已刷新")

            except Exception as e:
                print(f"      ⚠️ 缓存刷新失败: {e}")

            # 4. 更新当前进程的环境变量
            try:
                os.environ['COMPUTERNAME'] = fake_computer
                print(f"      ✅ 当前进程环境变量已更新")
            except Exception as e:
                print(f"      ⚠️ 更新当前进程环境变量失败: {e}")

            # 5. 强制触发系统刷新
            try:
                # 发送WM_SYSCOLORCHANGE消息强制系统刷新
                WM_SYSCOLORCHANGE = 0x0015
                ctypes.windll.user32.SendMessageW(HWND_BROADCAST, WM_SYSCOLORCHANGE, 0, 0)
                print(f"      ✅ 系统刷新信号已发送")

                # 发送WM_DISPLAYCHANGE消息
                WM_DISPLAYCHANGE = 0x007E
                ctypes.windll.user32.SendMessageW(HWND_BROADCAST, WM_DISPLAYCHANGE, 0, 0)
                print(f"      ✅ 显示更改信号已发送")

            except Exception as e:
                print(f"      ⚠️ 系统刷新信号发送失败: {e}")

            print(f"    💡 强力刷新完成，系统UI应该立即显示新计算机名")
            print(f"    🔍 请检查：设置 -> 系统 -> 关于 -> 设备名称")

        except Exception as e:
            print(f"    ❌ 立即生效机制应用失败: {e}")

    def _force_system_refresh(self):
        """强制系统刷新，确保UI立即更新"""
        try:
            print(f"      🔄 强制系统刷新...")

            # 1. 刷新资源管理器
            try:
                import ctypes

                # 发送刷新消息
                HWND_BROADCAST = 0xFFFF
                WM_WININICHANGE = 0x001A

                # 刷新系统配置
                ctypes.windll.user32.SendMessageW(HWND_BROADCAST, WM_WININICHANGE, 0, 0)
                print(f"        ✅ 系统配置刷新信号已发送")

            except Exception as e:
                print(f"        ⚠️ 系统配置刷新失败: {e}")

            # 2. 强制刷新注册表缓存
            try:
                # 刷新注册表缓存
                result = ctypes.windll.advapi32.RegFlushKey(0x80000002)  # HKEY_LOCAL_MACHINE
                if result == 0:
                    print(f"        ✅ 注册表缓存已刷新")

            except Exception as e:
                print(f"        ⚠️ 注册表缓存刷新失败: {e}")

            print(f"      ✅ 系统刷新完成")

        except Exception as e:
            print(f"      ❌ 系统刷新失败: {e}")

    def _backup_and_modify_registry(self, hkey, subkey: str, value_name: str, new_value: str) -> bool:
        """备份并修改注册表值"""
        try:
            # 备份原值
            with winreg.OpenKey(hkey, subkey) as key:
                original_value, value_type = winreg.QueryValueEx(key, value_name)

                # 创建更清晰的备份键格式
                if hkey == winreg.HKEY_LOCAL_MACHINE:
                    hkey_name = "HKEY_LOCAL_MACHINE"
                elif hkey == winreg.HKEY_CURRENT_USER:
                    hkey_name = "HKEY_CURRENT_USER"
                else:
                    hkey_name = str(hkey)

                backup_key = f"{hkey_name}\\{subkey}\\{value_name}"
                self.backup_values[backup_key] = (original_value, value_type)
                print(f"    📋 备份: {value_name} = {original_value}")

            # 修改值
            with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, new_value)
                print(f"    ✅ 修改: {value_name} = {new_value}")

            return True

        except Exception as e:
            print(f"    ❌ 修改失败: {subkey}\\{value_name} - {e}")
            return False
    
    def restore_device_info(self):
        """恢复设备信息"""
        if not self._is_modified:
            print("💡 设备信息未被修改，无需恢复")
            return

        try:
            print(f"🔄 恢复原始设备信息...")
            print(f"  📋 备份数据项数: {len(self.backup_values)}")

            # 恢复注册表
            restored_count = 0
            failed_count = 0

            for backup_key, (original_value, value_type) in self.backup_values.items():
                value_name = "Unknown"  # 默认值，避免未定义错误
                try:
                    parts = backup_key.split('\\')
                    if len(parts) < 3:
                        print(f"  ⚠️ 无效的备份键格式: {backup_key}")
                        failed_count += 1
                        continue

                    hkey_str = parts[0]
                    subkey = '\\'.join(parts[1:-1])
                    value_name = parts[-1]

                    # 解析注册表根键
                    if 'HKEY_LOCAL_MACHINE' in hkey_str:
                        hkey = winreg.HKEY_LOCAL_MACHINE
                    elif 'HKEY_CURRENT_USER' in hkey_str:
                        hkey = winreg.HKEY_CURRENT_USER
                    else:
                        print(f"  ⚠️ 不支持的注册表根键: {hkey_str}")
                        failed_count += 1
                        continue

                    # 恢复注册表值
                    with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                        winreg.SetValueEx(key, value_name, 0, value_type, original_value)
                        print(f"  ✅ 恢复: {value_name} = {original_value}")
                        restored_count += 1

                except PermissionError:
                    print(f"  ❌ 权限不足，无法恢复: {value_name}")
                    failed_count += 1
                except FileNotFoundError:
                    print(f"  ❌ 注册表项不存在: {value_name}")
                    failed_count += 1
                except Exception as e:
                    print(f"  ❌ 恢复失败: {value_name} - {e}")
                    failed_count += 1

            # 恢复环境变量（如果有备份）
            env_restored = 0
            if hasattr(self, 'env_backup'):
                for env_name, original_value in self.env_backup.items():
                    try:
                        if original_value is None:
                            # 原来没有这个环境变量，删除它
                            if env_name in os.environ:
                                del os.environ[env_name]
                        else:
                            # 恢复原始值
                            os.environ[env_name] = original_value
                        print(f"  ✅ 恢复环境变量: {env_name}")
                        env_restored += 1
                    except Exception as e:
                        print(f"  ❌ 恢复环境变量失败: {env_name} - {e}")

            # 清理临时文件
            temp_cleaned = 0
            for temp_dir in self.temp_files:
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                        print(f"  🗑️ 清理临时文件: {temp_dir}")
                        temp_cleaned += 1
                except Exception as e:
                    print(f"  ⚠️ 清理临时文件失败: {temp_dir} - {e}")

            # 清空备份数据
            self.backup_values.clear()
            self.temp_files.clear()
            if hasattr(self, 'env_backup'):
                self.env_backup.clear()
            self._is_modified = False

            # 清理备份文件
            self._clear_backup_file()

            total_restored = restored_count + env_restored
            print(f"  ✅ 设备信息恢复完成:")
            print(f"    📊 注册表项: {restored_count} 成功, {failed_count} 失败")
            print(f"    🌍 环境变量: {env_restored} 项")
            print(f"    📁 临时文件: {temp_cleaned} 项")
            print(f"    🎯 总计恢复: {total_restored} 项")

            if failed_count > 0:
                print(f"  ⚠️ 有 {failed_count} 项恢复失败，可能需要重启系统")

        except Exception as e:
            print(f"❌ 恢复设备信息失败: {e}")
            import traceback
            traceback.print_exc()
    
    def is_modified(self) -> bool:
        """检查设备信息是否已被修改"""
        return self._is_modified
    
    def get_current_device_info(self) -> Dict[str, str]:
        """获取当前设备信息"""
        try:
            return {
                'computer_name': os.environ.get('COMPUTERNAME', 'Unknown'),
                'user_name': os.environ.get('USERNAME', 'Unknown'),
                'user_domain': os.environ.get('USERDOMAIN', 'Unknown'),
                'platform_node': __import__('platform').node()
            }
        except:
            return {}

    def _save_backup(self):
        """保存备份数据到文件"""
        try:
            backup_data = {
                'backup_values': self.backup_values,
                'temp_files': self.temp_files,
                'env_backup': getattr(self, 'env_backup', {}),
                'is_modified': self._is_modified,
                'timestamp': time.time()
            }

            with open(self.backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            print(f"  💾 备份数据已保存: {self.backup_file}")

        except Exception as e:
            print(f"  ⚠️ 保存备份数据失败: {e}")

    def _load_backup(self):
        """从文件加载备份数据"""
        try:
            if os.path.exists(self.backup_file):
                with open(self.backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)

                self.backup_values = backup_data.get('backup_values', {})
                self.temp_files = backup_data.get('temp_files', [])
                self.env_backup = backup_data.get('env_backup', {})
                self._is_modified = backup_data.get('is_modified', False)

                if self._is_modified:
                    timestamp = backup_data.get('timestamp', 0)
                    backup_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                    print(f"  📋 检测到之前的备份数据 (时间: {backup_time})")
                    print(f"    📊 注册表备份: {len(self.backup_values)} 项")
                    print(f"    🌍 环境变量备份: {len(self.env_backup)} 项")
                    print(f"    📁 临时文件: {len(self.temp_files)} 项")

        except Exception as e:
            print(f"  ⚠️ 加载备份数据失败: {e}")
            # 如果加载失败，重置为默认状态
            self.backup_values = {}
            self.temp_files = []
            self.env_backup = {}
            self._is_modified = False

    def _clear_backup_file(self):
        """清理备份文件"""
        try:
            if os.path.exists(self.backup_file):
                os.remove(self.backup_file)
                print(f"  🗑️ 备份文件已清理: {self.backup_file}")
        except Exception as e:
            print(f"  ⚠️ 清理备份文件失败: {e}")

# 全局设备修改器实例
device_modifier = DeviceModifier()

def modify_device_info() -> bool:
    """修改设备信息的便捷函数"""
    return device_modifier.modify_device_info()

def restore_device_info():
    """恢复设备信息的便捷函数"""
    device_modifier.restore_device_info()

def is_device_modified() -> bool:
    """检查设备信息是否已被修改的便捷函数"""
    return device_modifier.is_modified()

if __name__ == "__main__":
    # 测试设备修改功能
    print("🧪 测试设备信息修改功能...")
    
    # 检查管理员权限
    import ctypes
    if not ctypes.windll.shell32.IsUserAnAdmin():
        print("❌ 需要管理员权限来测试设备修改功能")
        print("💡 请以管理员身份运行此脚本")
        exit(1)
    
    modifier = DeviceModifier()
    
    print("📋 当前设备信息:")
    current_info = modifier.get_current_device_info()
    for key, value in current_info.items():
        print(f"  {key}: {value}")
    
    print("\n🔧 开始修改设备信息...")
    if modifier.modify_device_info():
        print("\n📋 修改后设备信息:")
        new_info = modifier.get_current_device_info()
        for key, value in new_info.items():
            print(f"  {key}: {value}")
        
        input("\n按回车键恢复原始设备信息...")
        modifier.restore_device_info()
        
        print("\n📋 恢复后设备信息:")
        restored_info = modifier.get_current_device_info()
        for key, value in restored_info.items():
            print(f"  {key}: {value}")
    else:
        print("❌ 设备信息修改失败")
