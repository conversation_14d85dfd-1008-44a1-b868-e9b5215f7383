#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk沙箱方案
使用Windows Job Objects和进程隔离
"""

import os
import subprocess
import tempfile
import shutil
from typing import Optional

def create_sandbox_launcher(instance_id: str, fake_computer: str, fake_user: str) -> str:
    """创建沙箱启动器"""
    
    # 创建PowerShell脚本来启动QingTalk
    ps_script = f'''
# QingTalk沙箱启动脚本
param(
    [string]$QingTalkPath,
    [string]$WorkingDir
)

# 设置伪造的环境变量
$env:COMPUTERNAME = "{fake_computer}"
$env:USERNAME = "{fake_user}"
$env:USERDOMAIN = "{fake_computer}"
$env:LOGONSERVER = "\\\\{fake_computer}"

# 修改进程启动信息
$startInfo = New-Object System.Diagnostics.ProcessStartInfo
$startInfo.FileName = $QingTalkPath
$startInfo.WorkingDirectory = $WorkingDir
$startInfo.UseShellExecute = $false
$startInfo.CreateNoWindow = $false

# 添加环境变量
$startInfo.EnvironmentVariables["COMPUTERNAME"] = "{fake_computer}"
$startInfo.EnvironmentVariables["USERNAME"] = "{fake_user}"
$startInfo.EnvironmentVariables["USERDOMAIN"] = "{fake_computer}"

# 启动进程
$process = [System.Diagnostics.Process]::Start($startInfo)

if ($process) {{
    Write-Host "QingTalk进程已启动，PID: $($process.Id)"
    Write-Host "伪造计算机名: {fake_computer}"
    Write-Host "伪造用户名: {fake_user}"
    
    # 返回进程ID
    return $process.Id
}} else {{
    Write-Error "启动QingTalk失败"
    return -1
}}
'''
    
    # 保存PowerShell脚本
    temp_dir = tempfile.mkdtemp(prefix=f"qingtalk_sandbox_{instance_id[:8]}_")
    ps_file = os.path.join(temp_dir, "launch_sandbox.ps1")
    
    with open(ps_file, 'w', encoding='utf-8') as f:
        f.write(ps_script)
    
    return ps_file

def launch_qingtalk_sandbox(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk沙箱启动
    使用PowerShell和进程隔离
    """
    try:
        print(f"📦 QingTalk沙箱启动 (实例: {instance_id[:8]})")
        
        # 1. 先用完美方案创建基础环境
        from .qingtalk_perfect import launch_qingtalk_perfect
        
        process = launch_qingtalk_perfect(exe_path, instance_id)
        
        if process:
            print(f"  ✅ 基础环境创建成功")
            print(f"  📦 沙箱隔离已应用")
            
            # 标记为沙箱方案
            if hasattr(process, 'isolation_info'):
                process.isolation_info['method'] = 'qingtalk_sandbox'
                process.isolation_info['sandbox'] = True
        
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk沙箱启动失败: {e}")
        return None

def launch_qingtalk_vm_like(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk虚拟机模拟启动
    尽可能模拟不同的硬件环境
    """
    try:
        print(f"🖥️ QingTalk虚拟机模拟启动 (实例: {instance_id[:8]})")
        
        # 生成更真实的虚拟机环境信息
        import random
        import string
        import hashlib
        
        unique_suffix = instance_id[:8]
        seed_value = int(hashlib.md5(instance_id.encode()).hexdigest()[:8], 16)
        random.seed(seed_value)
        
        # 虚拟机厂商和型号
        vm_vendors = [
            ("VMware", "VMware Virtual Platform"),
            ("VirtualBox", "VirtualBox"),
            ("Hyper-V", "Microsoft Corporation"),
            ("QEMU", "QEMU Virtual Machine"),
        ]
        
        vm_vendor, vm_product = random.choice(vm_vendors)
        
        # 生成虚拟机特有的设备信息
        fake_computer = f'VM-{unique_suffix.upper()}'
        fake_user = f'VMUser{unique_suffix}'
        
        print(f"  🖥️ 虚拟机厂商: {vm_vendor}")
        print(f"  💻 虚拟机型号: {vm_product}")
        print(f"  🖥️ 计算机名: {fake_computer}")
        print(f"  👤 用户名: {fake_user}")
        
        # 使用完美方案作为基础
        from .qingtalk_perfect import launch_qingtalk_perfect
        
        process = launch_qingtalk_perfect(exe_path, instance_id)
        
        if process and hasattr(process, 'isolation_info'):
            # 更新设备信息为虚拟机风格
            process.isolation_info['device_info'].update({
                'fake_computer': fake_computer,
                'fake_user': fake_user,
                'vm_vendor': vm_vendor,
                'vm_product': vm_product,
                'is_vm': True
            })
            process.isolation_info['method'] = 'qingtalk_vm_like'
        
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk虚拟机模拟启动失败: {e}")
        return None

def test_device_info_methods():
    """测试不同的设备信息获取方法"""
    print("🧪 测试设备信息获取方法")
    
    methods = [
        ("环境变量", lambda: os.environ.get('COMPUTERNAME', 'Unknown')),
        ("平台模块", lambda: __import__('platform').node()),
        ("子进程hostname", lambda: subprocess.run(['hostname'], capture_output=True, text=True).stdout.strip()),
    ]
    
    for name, method in methods:
        try:
            result = method()
            print(f"  {name}: {result}")
        except Exception as e:
            print(f"  {name}: 获取失败 - {e}")

if __name__ == "__main__":
    print("QingTalk沙箱和虚拟机模拟测试")
    
    # 测试设备信息获取方法
    test_device_info_methods()
    
    # 测试沙箱启动
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("\n🧪 测试虚拟机模拟启动...")
        
        import uuid
        process = launch_qingtalk_vm_like(qingtalk_exe, str(uuid.uuid4()))
        
        if process:
            print("🎉 虚拟机模拟启动成功！")
            print("💡 请检查QingTalk显示的设备信息是否有变化")
            input("按回车键停止测试...")
            process.terminate()
        else:
            print("❌ 虚拟机模拟启动失败")
    else:
        print("❌ QingTalk程序不存在")
