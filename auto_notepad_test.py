#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动记事本测试
"""

import subprocess
import time
import os

def main():
    print("自动记事本测试")
    print("=" * 50)
    
    test_exe = r"C:\Windows\System32\notepad.exe"
    
    if not os.path.exists(test_exe):
        print(f"❌ 记事本不存在: {test_exe}")
        return
    
    try:
        processes = []
        
        # 启动3个记事本
        for i in range(3):
            print(f"📝 启动记事本 {i+1}...")
            
            env = os.environ.copy()
            env['INSTANCE_ID'] = f'test-instance-{i}'
            
            process = subprocess.Popen(
                [test_exe],
                env=env,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            processes.append(process)
            print(f"✅ 记事本 {i+1} 启动成功 (PID: {process.pid})")
            
            time.sleep(2)  # 延迟启动
        
        # 等待并检查
        print("\n⏳ 等待10秒检查状态...")
        time.sleep(10)
        
        running_count = 0
        for i, process in enumerate(processes):
            if process.poll() is None:
                print(f"✅ 记事本 {i+1} 正在运行 (PID: {process.pid})")
                running_count += 1
            else:
                print(f"❌ 记事本 {i+1} 已停止")
        
        print(f"\n📊 运行统计: {running_count}/3 个记事本正在运行")
        
        if running_count > 1:
            print("🎉 多开成功！")
        elif running_count == 1:
            print("⚠️ 只有一个记事本运行")
        else:
            print("❌ 没有记事本运行")
        
        # 关闭所有记事本
        print("\n🛑 关闭所有记事本...")
        for i, process in enumerate(processes):
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
                print(f"✅ 记事本 {i+1} 已关闭")
            except:
                print(f"⚠️ 记事本 {i+1} 关闭失败")
        
        print("\n测试完成。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
