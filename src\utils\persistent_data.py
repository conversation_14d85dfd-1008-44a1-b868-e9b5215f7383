#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持久化用户数据管理
确保QingTalk登录状态保持
"""

import os
import shutil
import tempfile
from typing import Optional

class PersistentDataManager:
    def __init__(self):
        self.base_dir = os.path.join(os.getcwd(), "persistent_data")
        os.makedirs(self.base_dir, exist_ok=True)
    
    def get_persistent_user_data_dir(self, instance_id: str) -> str:
        """获取持久化的用户数据目录"""
        instance_dir = os.path.join(self.base_dir, f"instance_{instance_id[:8]}")
        os.makedirs(instance_dir, exist_ok=True)
        return instance_dir
    
    def create_persistent_qingtalk_env(self, exe_path: str, instance_id: str) -> dict:
        """创建持久化的QingTalk环境"""
        try:
            print(f"🔄 创建持久化QingTalk环境 (实例: {instance_id[:8]})")
            
            # 获取持久化目录
            persistent_dir = self.get_persistent_user_data_dir(instance_id)
            
            # 创建用户数据目录结构
            user_data_dir = os.path.join(persistent_dir, 'UserData')
            appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
            appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
            temp_dir = os.path.join(user_data_dir, 'Temp')
            
            # 创建目录
            for path in [appdata_roaming, appdata_local, temp_dir]:
                os.makedirs(path, exist_ok=True)
            
            # 创建QingTalk专用目录
            qingtalk_roaming = os.path.join(appdata_roaming, 'QingTalk')
            qingtalk_local = os.path.join(appdata_local, 'QingTalk')
            os.makedirs(qingtalk_roaming, exist_ok=True)
            os.makedirs(qingtalk_local, exist_ok=True)
            
            print(f"  📂 持久化目录: {persistent_dir}")
            print(f"  💾 用户数据: {user_data_dir}")
            
            return {
                'persistent_dir': persistent_dir,
                'user_data_dir': user_data_dir,
                'appdata_roaming': appdata_roaming,
                'appdata_local': appdata_local,
                'temp_dir': temp_dir,
                'qingtalk_roaming': qingtalk_roaming,
                'qingtalk_local': qingtalk_local
            }
            
        except Exception as e:
            print(f"❌ 创建持久化环境失败: {e}")
            return None
    
    def cleanup_instance_data(self, instance_id: str):
        """清理指定实例的数据"""
        try:
            instance_dir = os.path.join(self.base_dir, f"instance_{instance_id[:8]}")
            if os.path.exists(instance_dir):
                shutil.rmtree(instance_dir, ignore_errors=True)
                print(f"🧹 已清理实例数据: {instance_id[:8]}")
        except Exception as e:
            print(f"⚠️ 清理实例数据失败: {e}")
    
    def list_persistent_instances(self) -> list:
        """列出所有持久化实例"""
        instances = []
        try:
            if os.path.exists(self.base_dir):
                for item in os.listdir(self.base_dir):
                    if item.startswith('instance_') and os.path.isdir(os.path.join(self.base_dir, item)):
                        instance_id = item.replace('instance_', '')
                        instances.append(instance_id)
        except Exception as e:
            print(f"⚠️ 列出持久化实例失败: {e}")
        
        return instances
    
    def get_instance_data_size(self, instance_id: str) -> int:
        """获取实例数据大小（字节）"""
        try:
            instance_dir = os.path.join(self.base_dir, f"instance_{instance_id[:8]}")
            if os.path.exists(instance_dir):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(instance_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except:
                            pass
                return total_size
        except:
            pass
        return 0
    
    def cleanup_all_data(self):
        """清理所有持久化数据"""
        try:
            if os.path.exists(self.base_dir):
                shutil.rmtree(self.base_dir, ignore_errors=True)
                os.makedirs(self.base_dir, exist_ok=True)
                print("🧹 已清理所有持久化数据")
        except Exception as e:
            print(f"⚠️ 清理所有数据失败: {e}")

# 全局实例
persistent_manager = PersistentDataManager()

def launch_qingtalk_persistent(exe_path: str, instance_id: str) -> Optional:
    """使用持久化数据启动QingTalk - 改进版"""
    try:
        print(f"🔄 QingTalk持久化启动 (实例: {instance_id[:8]})")

        # 创建持久化环境
        env_info = persistent_manager.create_persistent_qingtalk_env(exe_path, instance_id)
        if not env_info:
            return None

        # 1. 创建隔离的QingTalk程序副本（避免程序文件冲突）
        import tempfile
        import shutil
        temp_dir = tempfile.mkdtemp(prefix=f"qingtalk_persistent_exe_{instance_id[:8]}_")
        qingtalk_source_dir = os.path.dirname(exe_path)
        qingtalk_isolated_dir = os.path.join(temp_dir, "QingTalk")

        print(f"  📂 复制QingTalk程序到临时目录...")
        shutil.copytree(qingtalk_source_dir, qingtalk_isolated_dir,
                       ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))

        isolated_exe = os.path.join(qingtalk_isolated_dir, "QingTalk.exe")

        # 2. 设置环境变量
        env = os.environ.copy()

        # 使用持久化目录
        env.update({
            'APPDATA': env_info['appdata_roaming'],
            'LOCALAPPDATA': env_info['appdata_local'],
            'USERPROFILE': env_info['user_data_dir'],
            'TEMP': env_info['temp_dir'],
            'TMP': env_info['temp_dir'],
        })

        # 进程隔离标识
        unique_suffix = instance_id[:8]
        env.update({
            'COMPUTERNAME': f'QINGTALK-{unique_suffix.upper()}',
            'USERNAME': f'QTUser_{unique_suffix}',
            'USERDOMAIN': f'QTDOMAIN_{unique_suffix.upper()}',
            'SESSIONNAME': f'QTSession_{unique_suffix}',
        })

        # 网络隔离
        port_base = 40000 + (sum(ord(c) for c in instance_id) % 10000)
        env.update({
            'QINGTALK_PORT_BASE': str(port_base),
            'QINGTALK_IPC_PORT': str(port_base + 1),
            'QINGTALK_SYNC_PORT': str(port_base + 2),
            'QINGTALK_UPDATE_PORT': str(port_base + 3),
        })

        # 进程间通信隔离
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_ISOLATION_MODE': '1',
            'QINGTALK_MUTEX_PREFIX': f'QT_{unique_suffix}_',
            'QINGTALK_PIPE_PREFIX': f'QTPipe_{unique_suffix}_',
            'QINGTALK_SHARED_MEM_PREFIX': f'QTMem_{unique_suffix}_',
        })

        print(f"  🔧 端口基数: {port_base}")
        print(f"  🏷️ 计算机名: QINGTALK-{unique_suffix.upper()}")

        # 3. 启动隔离的QingTalk
        import subprocess
        process = subprocess.Popen(
            isolated_exe,
            env=env,
            cwd=qingtalk_isolated_dir,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NEW_CONSOLE
        )

        # 保存持久化信息
        process.isolation_info = {
            'user_data_dir': env_info['persistent_dir'],
            'temp_exe_dir': temp_dir,  # 临时程序目录
            'method': 'qingtalk_persistent',
            'instance_id': instance_id,
            'port_base': port_base,
            'persistent': True,
            'env_info': env_info
        }

        print(f"  ✅ QingTalk持久化启动成功 (PID: {process.pid})")
        return process

    except Exception as e:
        print(f"  ❌ QingTalk持久化启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_qingtalk_persistent(process):
    """清理QingTalk持久化资源（不删除用户数据）"""
    try:
        if hasattr(process, 'isolation_info'):
            isolation_info = process.isolation_info
            instance_id = isolation_info.get('instance_id')
            temp_exe_dir = isolation_info.get('temp_exe_dir')

            # 清理临时程序目录
            if temp_exe_dir and os.path.exists(temp_exe_dir):
                import shutil
                shutil.rmtree(temp_exe_dir, ignore_errors=True)
                print(f"🧹 清理临时程序目录: {temp_exe_dir}")

            # 注意：这里不删除持久化数据，保持登录状态
            print(f"💾 保持实例 {instance_id[:8]} 的持久化数据（登录状态保持）")

    except Exception as e:
        print(f"⚠️ 清理持久化资源失败: {e}")

if __name__ == "__main__":
    # 测试持久化管理器
    manager = PersistentDataManager()
    
    print("持久化数据管理器测试")
    print("=" * 30)
    
    # 列出现有实例
    instances = manager.list_persistent_instances()
    print(f"现有持久化实例: {len(instances)} 个")
    
    for instance_id in instances:
        size = manager.get_instance_data_size(instance_id)
        size_mb = size / 1024 / 1024
        print(f"  实例 {instance_id}: {size_mb:.2f} MB")
    
    print("\n测试完成")
