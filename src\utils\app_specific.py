#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特定应用程序支持
为不同的应用程序提供特定的启动参数和配置
"""

import os
from typing import List, Dict, Optional

def get_app_specific_args(exe_path: str, instance_id: str, user_data_dir: str) -> List[str]:
    """
    获取特定应用程序的启动参数
    """
    exe_name = os.path.basename(exe_path).lower()
    args = []
    
    # Chrome/Chromium 系列应用
    if any(name in exe_name for name in ['chrome', 'chromium', 'edge']):
        args.extend([
            f'--user-data-dir={user_data_dir}',
            f'--profile-directory=Profile_{instance_id[:8]}',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-extensions-file-access-check'
        ])
    
    # QingTalk 特殊处理 - 不使用参数，依赖环境变量隔离
    elif 'qingtalk' in exe_name:
        # QingTalk对参数很敏感，不添加任何启动参数
        # 完全依赖环境变量和工作目录隔离
        pass

    # 其他 Electron 应用 (钉钉、微信等)
    elif any(name in exe_name for name in ['dingtalk', 'wechat', 'weixin']):
        args.extend([
            f'--user-data-dir={user_data_dir}',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu-sandbox',
            f'--app-user-model-id=Instance_{instance_id[:8]}'
        ])
    
    # Firefox
    elif 'firefox' in exe_name:
        profile_dir = os.path.join(user_data_dir, f'firefox_profile_{instance_id[:8]}')
        os.makedirs(profile_dir, exist_ok=True)
        args.extend([
            '-profile', profile_dir,
            '-no-remote'
        ])
    
    # QQ
    elif 'qq' in exe_name:
        args.extend([
            f'/uin:{instance_id[:8]}',
            '/background'
        ])
    
    # 通用 Electron 应用检测
    elif is_electron_app(exe_path):
        args.extend([
            f'--user-data-dir={user_data_dir}',
            '--no-sandbox',
            '--disable-dev-shm-usage'
        ])
    
    return args

def get_app_specific_env(exe_path: str, instance_id: str, user_data_dir: str) -> Dict[str, str]:
    """
    获取特定应用程序的环境变量
    """
    exe_name = os.path.basename(exe_path).lower()
    env = {}
    
    # 钉钉/QingTalk 特殊处理
    if any(name in exe_name for name in ['dingtalk', 'qingtalk']):
        env.update({
            'DINGTALK_USER_DATA': user_data_dir,
            'QINGTALK_USER_DATA': user_data_dir,
            'ELECTRON_USER_DATA': user_data_dir,
            'CHROME_USER_DATA_DIR': user_data_dir
        })
    
    # 微信特殊处理
    elif any(name in exe_name for name in ['wechat', 'weixin']):
        env.update({
            'WECHAT_USER_DATA': user_data_dir,
            'WEIXIN_USER_DATA': user_data_dir
        })
    
    # Chrome 系列
    elif any(name in exe_name for name in ['chrome', 'chromium', 'edge']):
        env.update({
            'CHROME_USER_DATA_DIR': user_data_dir,
            'GOOGLE_CHROME_USER_DATA_DIR': user_data_dir
        })
    
    return env

def is_electron_app(exe_path: str) -> bool:
    """
    检测是否为 Electron 应用
    """
    try:
        exe_dir = os.path.dirname(exe_path)
        
        # 检查是否存在 Electron 相关文件
        electron_indicators = [
            'resources/app.asar',
            'resources/electron.asar',
            'locales',
            'swiftshader'
        ]
        
        for indicator in electron_indicators:
            if os.path.exists(os.path.join(exe_dir, indicator)):
                return True
        
        return False
        
    except:
        return False

def get_mutex_name(exe_path: str, instance_id: str) -> Optional[str]:
    """
    获取应用程序的互斥体名称
    用于避免单实例检测
    """
    exe_name = os.path.basename(exe_path).lower()
    
    # 常见应用的互斥体名称模式
    mutex_patterns = {
        'dingtalk': f'DingTalk_Mutex_{instance_id[:8]}',
        'qingtalk': f'QingTalk_Mutex_{instance_id[:8]}',
        'wechat': f'WeChat_Mutex_{instance_id[:8]}',
        'qq': f'QQ_Mutex_{instance_id[:8]}',
        'chrome': f'Chrome_Mutex_{instance_id[:8]}',
        'firefox': f'Firefox_Mutex_{instance_id[:8]}'
    }
    
    for app_name, mutex_name in mutex_patterns.items():
        if app_name in exe_name:
            return mutex_name
    
    return None

def should_use_isolated_exe(exe_path: str) -> bool:
    """
    判断是否应该使用隔离的exe文件
    """
    exe_name = os.path.basename(exe_path).lower()
    
    # 这些应用通常需要exe隔离
    need_isolation = [
        'dingtalk', 'qingtalk', 'wechat', 'weixin', 'qq',
        'chrome', 'firefox', 'edge'
    ]
    
    return any(name in exe_name for name in need_isolation)

def get_app_display_name(exe_path: str) -> str:
    """
    获取应用程序的显示名称
    """
    exe_name = os.path.basename(exe_path).lower()
    
    display_names = {
        'dingtalk.exe': '钉钉',
        'qingtalk.exe': 'QingTalk',
        'wechat.exe': '微信',
        'weixin.exe': '微信',
        'qq.exe': 'QQ',
        'chrome.exe': 'Chrome',
        'firefox.exe': 'Firefox',
        'msedge.exe': 'Edge',
        'notepad.exe': '记事本',
        'calc.exe': '计算器'
    }
    
    return display_names.get(exe_name, os.path.splitext(exe_name)[0])
