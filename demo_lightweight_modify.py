#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级设备信息修改演示脚本
展示只修改GetComputerNameEx函数的效果
"""

import ctypes
from ctypes import wintypes
import os
import sys
import platform

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_computer_name_ex(name_type=0):
    """调用GetComputerNameEx API获取计算机名"""
    size = wintypes.DWORD(0)
    ctypes.windll.kernel32.GetComputerNameExW(name_type, None, ctypes.byref(size))
    buffer = ctypes.create_unicode_buffer(size.value)
    success = ctypes.windll.kernel32.GetComputerNameExW(name_type, buffer, ctypes.byref(size))
    if success:
        return buffer.value
    return None

def show_current_info():
    """显示当前系统信息"""
    print("📋 当前系统信息:")
    print(f"  GetComputerNameEx(0): {get_computer_name_ex(0)}")
    print(f"  环境变量 COMPUTERNAME: {os.environ.get('COMPUTERNAME')}")
    print(f"  Python platform.node(): {platform.node()}")

def main():
    """主函数"""
    print("🎯 轻量级设备信息修改演示")
    print("=" * 50)
    
    # 检查管理员权限
    if not ctypes.windll.shell32.IsUserAnAdmin():
        print("❌ 此演示需要管理员权限")
        print("请以管理员身份重新运行此脚本")
        return
    
    # 显示当前信息
    print("\n🔍 修改前:")
    show_current_info()
    
    # 导入设备修改器
    try:
        from src.utils.device_modifier import DeviceModifier
        
        modifier = DeviceModifier()
        
        print(f"\n🔧 执行轻量级修改...")
        success = modifier.modify_computer_name_only()
        
        if success:
            print(f"\n✅ 轻量级修改成功！")
            print(f"\n🔍 修改后:")
            show_current_info()
            
            print(f"\n💡 说明:")
            print(f"  - 只修改了GetComputerNameEx相关的注册表项")
            print(f"  - 只修改了COMPUTERNAME环境变量")
            print(f"  - 对系统其他部分没有影响")
            print(f"  - 这种修改不会导致系统崩溃")
            
            input(f"\n按回车键恢复原始信息...")
            
            print(f"\n🔄 恢复原始信息...")
            modifier.restore_device_info()
            
            print(f"\n🔍 恢复后:")
            show_current_info()
            
            print(f"\n✅ 演示完成！")
            
        else:
            print(f"\n❌ 轻量级修改失败")
            
    except ImportError as e:
        print(f"\n❌ 无法导入设备修改模块: {e}")
    except Exception as e:
        print(f"\n❌ 演示过程出错: {e}")

if __name__ == "__main__":
    main()
