#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的替代方案：不修改系统计算机名，而是通过进程级别的环境变量隔离
这种方式不会影响系统稳定性，但仍能实现多开的设备区分
"""

import os
import sys
import json
import tempfile
import subprocess
import random
import string
import hashlib

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SafeDeviceIsolation:
    """安全的设备隔离方案"""
    
    def __init__(self):
        self.instance_id = None
        self.fake_device_info = {}
        self.original_env = {}
        self.temp_files = []
    
    def generate_fake_device_info(self, instance_id):
        """生成伪造的设备信息"""
        # 使用实例ID作为种子，确保每次生成相同的设备信息
        seed_value = int(hashlib.md5(instance_id.encode()).hexdigest()[:8], 16)
        random.seed(seed_value)
        
        # 生成固定的设备信息
        fake_computer = f"DESKTOP-{''.join(random.choices(string.ascii_uppercase + string.digits, k=8))}"
        fake_user = f"User{''.join(random.choices(string.ascii_lowercase + string.digits, k=6))}"
        
        return {
            'computer': fake_computer,
            'user': fake_user,
            'instance_id': instance_id
        }
    
    def create_isolated_environment(self, instance_id):
        """创建隔离的环境变量"""
        print(f"🔧 创建安全的设备隔离环境...")
        
        self.instance_id = instance_id
        self.fake_device_info = self.generate_fake_device_info(instance_id)
        
        print(f"  🎯 实例ID: {instance_id}")
        print(f"  🖥️ 伪造计算机名: {self.fake_device_info['computer']}")
        print(f"  👤 伪造用户名: {self.fake_device_info['user']}")
        
        # 备份原始环境变量
        env_vars_to_modify = [
            'COMPUTERNAME',
            'USERNAME', 
            'USERDOMAIN',
            'LOGONSERVER',
            'CLIENTNAME'
        ]
        
        for var in env_vars_to_modify:
            if var in os.environ:
                self.original_env[var] = os.environ[var]
        
        # 创建新的环境变量字典（不修改系统，只用于启动进程）
        new_env = os.environ.copy()
        new_env.update({
            'COMPUTERNAME': self.fake_device_info['computer'],
            'USERNAME': self.fake_device_info['user'],
            'USERDOMAIN': self.fake_device_info['computer'],
            'LOGONSERVER': f"\\\\{self.fake_device_info['computer']}",
            'CLIENTNAME': self.fake_device_info['computer'],
            # 添加自定义标识
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_FAKE_COMPUTER': self.fake_device_info['computer'],
            'QINGTALK_FAKE_USER': self.fake_device_info['user'],
        })
        
        # 创建临时配置文件
        temp_dir = tempfile.mkdtemp(prefix=f"qingtalk_safe_{instance_id[:8]}_")
        
        # 创建设备信息文件
        device_info_file = os.path.join(temp_dir, "device_info.json")
        with open(device_info_file, 'w', encoding='utf-8') as f:
            json.dump(self.fake_device_info, f, indent=2, ensure_ascii=False)
        
        # 创建环境变量文件
        env_file = os.path.join(temp_dir, "environment.json")
        with open(env_file, 'w', encoding='utf-8') as f:
            json.dump({k: v for k, v in new_env.items() if k.startswith('QINGTALK_')}, f, indent=2)
        
        self.temp_files.append(temp_dir)
        
        print(f"  ✅ 隔离环境创建完成")
        print(f"  📁 临时目录: {temp_dir}")
        
        return new_env
    
    def launch_isolated_process(self, executable_path, instance_id, args=None):
        """启动隔离的进程"""
        print(f"🚀 启动隔离进程...")
        
        # 创建隔离环境
        isolated_env = self.create_isolated_environment(instance_id)
        
        # 准备启动参数
        if args is None:
            args = []
        
        cmd = [executable_path] + args
        
        try:
            # 使用隔离的环境变量启动进程
            process = subprocess.Popen(
                cmd,
                env=isolated_env,
                cwd=os.path.dirname(executable_path) if os.path.dirname(executable_path) else None,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            print(f"  ✅ 进程启动成功 (PID: {process.pid})")
            print(f"  🛡️ 使用隔离环境变量，不影响系统")
            
            return process
            
        except Exception as e:
            print(f"  ❌ 进程启动失败: {e}")
            return None
    
    def cleanup(self):
        """清理临时文件"""
        print(f"🧹 清理临时文件...")
        
        for temp_dir in self.temp_files:
            try:
                import shutil
                shutil.rmtree(temp_dir)
                print(f"  ✅ 删除: {temp_dir}")
            except Exception as e:
                print(f"  ⚠️ 删除失败: {temp_dir} - {e}")
        
        self.temp_files.clear()

def test_safe_isolation():
    """测试安全隔离方案"""
    print("🧪 测试安全设备隔离方案")
    print("=" * 50)
    print("此方案不修改系统设置，只在进程级别隔离")
    print("=" * 50)
    
    isolator = SafeDeviceIsolation()
    
    try:
        # 生成测试实例ID
        instance_id = f"test_{random.randint(10000, 99999)}"
        
        # 创建隔离环境
        isolated_env = isolator.create_isolated_environment(instance_id)
        
        print(f"\n📋 隔离环境变量:")
        for key, value in isolated_env.items():
            if key.startswith('QINGTALK_') or key in ['COMPUTERNAME', 'USERNAME']:
                print(f"  {key}: {value}")
        
        print(f"\n💡 使用方法:")
        print(f"  1. 这些环境变量只在启动的进程中生效")
        print(f"  2. 不会修改系统的实际计算机名")
        print(f"  3. 每个QingTalk实例看到不同的设备信息")
        print(f"  4. 系统稳定性不受影响")
        
        # 演示启动记事本（作为测试）
        print(f"\n🚀 演示启动隔离进程（记事本）...")
        process = isolator.launch_isolated_process("notepad.exe", instance_id)
        
        if process:
            print(f"  ✅ 记事本已启动，PID: {process.pid}")
            print(f"  💡 在记事本进程中，环境变量已被隔离")
            
            input("  按回车键终止记事本进程...")
            process.terminate()
            print(f"  ✅ 记事本进程已终止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        isolator.cleanup()

def show_comparison():
    """显示方案对比"""
    print("\n📊 方案对比:")
    print("=" * 70)
    
    print("❌ 修改系统计算机名方案:")
    print("  优点: UI立即显示变化，检测绕过效果好")
    print("  缺点: 影响系统稳定性，可能导致网络错误、缓冲区溢出")
    print("  风险: 高 - 可能导致系统崩溃或网络异常")
    
    print("\n✅ 进程级环境变量隔离方案:")
    print("  优点: 不影响系统稳定性，安全可靠")
    print("  缺点: 只在进程级别生效，系统UI不会显示变化")
    print("  风险: 低 - 不修改系统设置")
    
    print("\n🎯 推荐使用进程级隔离方案，因为:")
    print("  • 系统稳定性最重要")
    print("  • 避免网络接口错误")
    print("  • 避免缓冲区溢出")
    print("  • 仍能实现多开的设备区分")

def main():
    """主函数"""
    print("🛡️ 安全的设备隔离替代方案")
    print("=" * 50)
    print("基于你遇到的系统错误，提供更安全的替代方案")
    print("不修改系统计算机名，避免网络和缓冲区问题")
    print("=" * 50)
    
    # 显示对比
    show_comparison()
    
    # 询问是否测试
    print(f"\n❓ 是否要测试安全的设备隔离方案？")
    print(f"   这不会修改任何系统设置")
    print(f"   只在进程级别创建隔离环境")
    
    choice = input(f"\n输入 'y' 或 'yes' 开始测试，其他任意键退出: ").lower().strip()
    
    if choice in ['y', 'yes']:
        success = test_safe_isolation()
        if success:
            print(f"\n🎉 测试成功完成！")
            print(f"💡 安全隔离方案工作正常")
            print(f"🛡️ 系统稳定性得到保障")
        else:
            print(f"\n❌ 测试失败")
    else:
        print(f"\n👋 测试已取消")

if __name__ == "__main__":
    main()
