#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理模块
负责代理IP的配置、分配和管理
"""

import requests
import threading
import time
import uuid
from typing import Dict, List, Optional
from datetime import datetime, timedelta

class ProxyConfig:
    def __init__(self, proxy_id: str, proxy_type: str, host: str, port: int, 
                 username: str = None, password: str = None):
        self.id = proxy_id
        self.type = proxy_type  # http, https, socks4, socks5
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.is_active = True
        self.last_check = None
        self.response_time = None
        self.error_count = 0
        self.created_time = datetime.now()
        
    def get_proxy_url(self) -> str:
        """获取代理URL"""
        if self.username and self.password:
            return f"{self.type}://{self.username}:{self.password}@{self.host}:{self.port}"
        else:
            return f"{self.type}://{self.host}:{self.port}"
    
    def get_proxy_dict(self) -> Dict:
        """获取代理字典格式"""
        proxy_url = self.get_proxy_url()
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'id': self.id,
            'type': self.type,
            'host': self.host,
            'port': self.port,
            'username': self.username,
            'password': self.password,
            'is_active': self.is_active,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'response_time': self.response_time,
            'error_count': self.error_count,
            'created_time': self.created_time.isoformat()
        }

class ProxyManager:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.proxies: Dict[str, ProxyConfig] = {}
        self.assigned_proxies: Dict[str, str] = {}  # instance_id -> proxy_id
        self.check_thread = None
        self.checking = False
        
        # 加载代理配置
        self.load_proxies()
        
        # 启动代理检查线程
        self.start_proxy_checking()
    
    def load_proxies(self):
        """加载代理配置"""
        try:
            proxy_configs = self.config_manager.get_proxy_configs()
            for config in proxy_configs:
                proxy = ProxyConfig(
                    config['id'],
                    config['type'],
                    config['host'],
                    config['port'],
                    config.get('username'),
                    config.get('password')
                )
                proxy.is_active = config.get('is_active', True)
                proxy.error_count = config.get('error_count', 0)
                if config.get('last_check'):
                    proxy.last_check = datetime.fromisoformat(config['last_check'])
                
                self.proxies[proxy.id] = proxy
                
        except Exception as e:
            print(f"加载代理配置失败: {e}")
    
    def add_proxy(self, proxy_type: str, host: str, port: int, 
                  username: str = None, password: str = None) -> str:
        """添加代理"""
        proxy_id = str(uuid.uuid4())
        proxy = ProxyConfig(proxy_id, proxy_type, host, port, username, password)
        
        self.proxies[proxy_id] = proxy
        
        # 保存到配置
        self.config_manager.add_proxy_config(proxy.to_dict())
        
        return proxy_id
    
    def remove_proxy(self, proxy_id: str) -> bool:
        """删除代理"""
        if proxy_id not in self.proxies:
            return False
        
        # 检查是否有实例在使用
        for instance_id, assigned_proxy_id in self.assigned_proxies.items():
            if assigned_proxy_id == proxy_id:
                print(f"代理 {proxy_id} 正在被实例 {instance_id} 使用，无法删除")
                return False
        
        del self.proxies[proxy_id]
        self.config_manager.remove_proxy_config(proxy_id)
        
        return True
    
    def get_available_proxy(self) -> Optional[Dict]:
        """获取可用的代理"""
        available_proxies = [
            proxy for proxy in self.proxies.values()
            if proxy.is_active and proxy.id not in self.assigned_proxies.values()
        ]
        
        if not available_proxies:
            return None
        
        # 选择错误次数最少的代理
        best_proxy = min(available_proxies, key=lambda p: p.error_count)
        
        return {
            'id': best_proxy.id,
            'http_proxy': best_proxy.get_proxy_url(),
            'proxy_dict': best_proxy.get_proxy_dict()
        }
    
    def assign_proxy(self, instance_id: str, proxy_id: str = None) -> Optional[Dict]:
        """为实例分配代理"""
        if proxy_id is None:
            # 自动分配
            proxy_config = self.get_available_proxy()
            if proxy_config:
                proxy_id = proxy_config['id']
            else:
                return None
        
        if proxy_id not in self.proxies:
            return None
        
        if proxy_id in self.assigned_proxies.values():
            print(f"代理 {proxy_id} 已被分配")
            return None
        
        self.assigned_proxies[instance_id] = proxy_id
        proxy = self.proxies[proxy_id]
        
        return {
            'id': proxy.id,
            'http_proxy': proxy.get_proxy_url(),
            'proxy_dict': proxy.get_proxy_dict()
        }
    
    def release_proxy(self, instance_id: str):
        """释放实例的代理"""
        if instance_id in self.assigned_proxies:
            del self.assigned_proxies[instance_id]
    
    def check_proxy(self, proxy: ProxyConfig) -> bool:
        """检查代理是否可用"""
        try:
            test_url = "http://httpbin.org/ip"
            proxies = proxy.get_proxy_dict()
            
            start_time = time.time()
            response = requests.get(
                test_url, 
                proxies=proxies, 
                timeout=10,
                verify=False
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                proxy.last_check = datetime.now()
                proxy.response_time = response_time
                proxy.error_count = 0
                proxy.is_active = True
                return True
            else:
                proxy.error_count += 1
                proxy.is_active = False
                return False
                
        except Exception as e:
            proxy.error_count += 1
            proxy.is_active = False
            print(f"检查代理 {proxy.host}:{proxy.port} 失败: {e}")
            return False
    
    def start_proxy_checking(self):
        """启动代理检查线程"""
        if not self.checking:
            self.checking = True
            self.check_thread = threading.Thread(target=self._check_proxies_loop, daemon=True)
            self.check_thread.start()
    
    def stop_proxy_checking(self):
        """停止代理检查线程"""
        self.checking = False
        if self.check_thread:
            self.check_thread.join(timeout=5)
    
    def _check_proxies_loop(self):
        """代理检查循环"""
        while self.checking:
            try:
                for proxy in self.proxies.values():
                    # 每5分钟检查一次代理
                    if (proxy.last_check is None or 
                        datetime.now() - proxy.last_check > timedelta(minutes=5)):
                        self.check_proxy(proxy)
                
                time.sleep(60)  # 每分钟检查一次是否需要更新
                
            except Exception as e:
                print(f"代理检查循环出错: {e}")
                time.sleep(60)
    
    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息"""
        total = len(self.proxies)
        active = len([p for p in self.proxies.values() if p.is_active])
        assigned = len(self.assigned_proxies)
        
        return {
            'total': total,
            'active': active,
            'assigned': assigned,
            'available': active - assigned
        }
    
    def get_all_proxies(self) -> List[ProxyConfig]:
        """获取所有代理"""
        return list(self.proxies.values())
