#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合分析工具
整合所有分析功能，提供完整的QingTalk多开检测分析
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.analysis.process_monitor import ProcessMonitor
from src.analysis.registry_monitor import RegistryMonitor
from src.analysis.mutex_detector import MutexDetector

class ComprehensiveAnalyzer:
    def __init__(self):
        self.results = {}
        self.analysis_time = datetime.now()
    
    def run_full_analysis(self, monitor_duration: int = 30):
        """运行完整分析"""
        print("=" * 60)
        print("QingTalk多开检测机制 - 综合分析")
        print("=" * 60)
        
        # 1. 注册表分析
        print("\n🔍 步骤 1: 注册表分析")
        print("-" * 40)
        self.results['registry'] = self._analyze_registry()
        
        # 2. 互斥体分析
        print("\n🔍 步骤 2: 互斥体分析")
        print("-" * 40)
        self.results['mutex'] = self._analyze_mutexes()
        
        # 3. 进程监控分析
        print("\n🔍 步骤 3: 进程监控分析")
        print("-" * 40)
        print(f"即将开始 {monitor_duration} 秒的进程监控...")
        print("请准备启动QingTalk进行测试...")
        input("按回车键开始监控...")
        
        self.results['process'] = self._analyze_processes(monitor_duration)
        
        # 4. 综合分析
        print("\n🔍 步骤 4: 综合分析")
        print("-" * 40)
        self.results['comprehensive'] = self._comprehensive_analysis()
        
        # 5. 保存结果
        filename = self._save_results()
        
        # 6. 生成报告
        self._generate_report()
        
        return filename, self.results
    
    def _analyze_registry(self):
        """分析注册表"""
        try:
            monitor = RegistryMonitor()
            
            # 扫描QingTalk相关注册表项
            registry_results = monitor.scan_registry_for_qingtalk()
            
            # 检查常见单实例检测键
            common_keys = monitor.check_common_single_instance_keys()
            
            return {
                'scan_results': registry_results,
                'common_keys': common_keys,
                'status': 'success'
            }
        except Exception as e:
            return {
                'error': str(e),
                'status': 'failed'
            }
    
    def _analyze_mutexes(self):
        """分析互斥体"""
        try:
            detector = MutexDetector()
            
            # 查找QingTalk相关互斥体
            mutexes = detector.find_qingtalk_mutexes()
            
            # 分析模式
            analysis = detector.analyze_mutex_patterns(mutexes)
            
            return {
                'mutexes': mutexes,
                'analysis': analysis,
                'status': 'success'
            }
        except Exception as e:
            return {
                'error': str(e),
                'status': 'failed'
            }
    
    def _analyze_processes(self, duration: int):
        """分析进程"""
        try:
            monitor = ProcessMonitor("QingTalk.exe")
            
            # 监控进程
            log_data = monitor.monitor_processes(duration=duration, interval=0.5)
            
            # 分析日志
            analysis = monitor.analyze_log()
            
            return {
                'log_data': log_data,
                'analysis': analysis,
                'status': 'success'
            }
        except Exception as e:
            return {
                'error': str(e),
                'status': 'failed'
            }
    
    def _comprehensive_analysis(self):
        """综合分析"""
        analysis = {
            'detection_mechanisms': [],
            'recommendations': [],
            'bypass_strategies': []
        }
        
        # 分析注册表检测
        if self.results.get('registry', {}).get('status') == 'success':
            reg_data = self.results['registry']
            if reg_data.get('common_keys'):
                analysis['detection_mechanisms'].append({
                    'type': 'registry',
                    'description': '使用注册表键检测单实例',
                    'evidence': list(reg_data['common_keys'].keys())
                })
        
        # 分析互斥体检测
        if self.results.get('mutex', {}).get('status') == 'success':
            mutex_data = self.results['mutex']
            if mutex_data.get('mutexes'):
                analysis['detection_mechanisms'].append({
                    'type': 'mutex',
                    'description': '使用互斥体检测单实例',
                    'evidence': [m.get('name', '') for m in mutex_data['mutexes'] if m.get('name')]
                })
        
        # 分析进程检测
        if self.results.get('process', {}).get('status') == 'success':
            proc_data = self.results['process']
            if proc_data.get('analysis', {}).get('max_processes', 0) > 1:
                analysis['detection_mechanisms'].append({
                    'type': 'process_communication',
                    'description': '进程间通信检测多实例',
                    'evidence': f"最大进程数: {proc_data['analysis']['max_processes']}"
                })
        
        # 生成建议
        if any(mech['type'] == 'mutex' for mech in analysis['detection_mechanisms']):
            analysis['recommendations'].append(
                "检测到互斥体使用，建议尝试修改互斥体名称或使用不同的命名空间"
            )
        
        if any(mech['type'] == 'registry' for mech in analysis['detection_mechanisms']):
            analysis['recommendations'].append(
                "检测到注册表检测，建议使用注册表重定向或虚拟化"
            )
        
        if any(mech['type'] == 'process_communication' for mech in analysis['detection_mechanisms']):
            analysis['recommendations'].append(
                "检测到进程间通信，建议使用进程隔离或网络命名空间隔离"
            )
        
        # 生成绕过策略
        analysis['bypass_strategies'] = [
            {
                'strategy': 'Virtual Machine',
                'description': '使用虚拟机完全隔离',
                'difficulty': 'Easy',
                'effectiveness': 'High'
            },
            {
                'strategy': 'Container Isolation',
                'description': '使用容器技术隔离',
                'difficulty': 'Medium',
                'effectiveness': 'High'
            },
            {
                'strategy': 'User Session Isolation',
                'description': '使用不同用户会话',
                'difficulty': 'Medium',
                'effectiveness': 'Medium'
            },
            {
                'strategy': 'Registry Virtualization',
                'description': '注册表虚拟化',
                'difficulty': 'Hard',
                'effectiveness': 'Medium'
            },
            {
                'strategy': 'Mutex Hooking',
                'description': 'API Hook修改互斥体行为',
                'difficulty': 'Very Hard',
                'effectiveness': 'Medium'
            }
        ]
        
        return analysis
    
    def _save_results(self):
        """保存分析结果"""
        filename = f"qingtalk_analysis_{self.analysis_time.strftime('%Y%m%d_%H%M%S')}.json"
        
        output_data = {
            'analysis_time': self.analysis_time.isoformat(),
            'results': self.results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 完整分析结果已保存到: {filename}")
        return filename
    
    def _generate_report(self):
        """生成分析报告"""
        print("\n" + "=" * 60)
        print("QingTalk多开检测机制分析报告")
        print("=" * 60)
        
        comp_analysis = self.results.get('comprehensive', {})
        
        # 检测机制
        mechanisms = comp_analysis.get('detection_mechanisms', [])
        if mechanisms:
            print(f"\n🔍 发现的检测机制 ({len(mechanisms)} 个):")
            for i, mech in enumerate(mechanisms, 1):
                print(f"  {i}. {mech['type'].upper()}: {mech['description']}")
                if mech.get('evidence'):
                    if isinstance(mech['evidence'], list):
                        for evidence in mech['evidence'][:3]:  # 显示前3个证据
                            print(f"     - {evidence}")
                        if len(mech['evidence']) > 3:
                            print(f"     - ... 还有 {len(mech['evidence']) - 3} 个")
                    else:
                        print(f"     - {mech['evidence']}")
        else:
            print(f"\n🔍 未发现明确的检测机制")
        
        # 建议
        recommendations = comp_analysis.get('recommendations', [])
        if recommendations:
            print(f"\n💡 建议 ({len(recommendations)} 个):")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        # 绕过策略
        strategies = comp_analysis.get('bypass_strategies', [])
        if strategies:
            print(f"\n🛠️ 可能的绕过策略:")
            print(f"{'策略':<20} {'难度':<10} {'有效性':<10} {'描述'}")
            print("-" * 60)
            for strategy in strategies:
                print(f"{strategy['strategy']:<20} {strategy['difficulty']:<10} {strategy['effectiveness']:<10} {strategy['description']}")
        
        # 数据统计
        print(f"\n📊 数据统计:")
        
        if self.results.get('registry', {}).get('status') == 'success':
            reg_data = self.results['registry']
            total_reg_items = sum(len(items) if isinstance(items, list) else 0 
                                for items in reg_data.get('scan_results', {}).values())
            print(f"  注册表项: {total_reg_items} 个相关项")
        
        if self.results.get('mutex', {}).get('status') == 'success':
            mutex_count = len(self.results['mutex'].get('mutexes', []))
            print(f"  互斥体: {mutex_count} 个相关互斥体")
        
        if self.results.get('process', {}).get('status') == 'success':
            proc_analysis = self.results['process'].get('analysis', {})
            max_processes = proc_analysis.get('max_processes', 0)
            total_snapshots = proc_analysis.get('total_snapshots', 0)
            print(f"  进程监控: {total_snapshots} 个快照, 最大进程数: {max_processes}")

def run_comprehensive_analysis():
    """运行综合分析"""
    analyzer = ComprehensiveAnalyzer()
    
    print("QingTalk多开检测机制综合分析工具")
    print("=" * 60)
    print("此工具将分析QingTalk的多开检测机制，包括：")
    print("1. 注册表检测")
    print("2. 互斥体检测") 
    print("3. 进程监控")
    print("4. 综合分析和建议")
    print()
    
    duration = input("请输入进程监控持续时间（秒，默认30）: ").strip()
    if not duration.isdigit():
        duration = 30
    else:
        duration = int(duration)
    
    print(f"\n开始分析，进程监控将持续 {duration} 秒...")
    
    filename, results = analyzer.run_full_analysis(duration)
    
    print(f"\n✅ 分析完成！")
    print(f"📄 详细结果已保存到: {filename}")
    print(f"📋 请将此文件发送给开发者进行进一步分析")
    
    return filename, results

if __name__ == "__main__":
    run_comprehensive_analysis()
