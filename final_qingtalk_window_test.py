#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的QingTalk窗口测试
"""

import psutil
import win32gui

def final_qingtalk_window_test():
    """最终的QingTalk窗口测试"""
    print("🔍 最终QingTalk窗口搜索测试...")
    
    # 1. 查找QingTalk进程
    qingtalk_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if proc.info['name'] and 'qingtalk' in proc.info['name'].lower():
                qingtalk_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    print(f"📊 找到 {len(qingtalk_processes)} 个QingTalk进程:")
    qingtalk_pids = []
    for proc in qingtalk_processes:
        print(f"  PID: {proc['pid']} - {proc['name']}")
        qingtalk_pids.append(proc['pid'])
    
    if not qingtalk_processes:
        print("❌ 没有QingTalk进程，请先启动多开程序")
        return
    
    # 2. 枚举所有窗口
    all_windows = []
    qingtalk_windows = []
    chrome_windows = []
    
    def enum_windows_callback(hwnd, data):
        try:
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            is_visible = win32gui.IsWindowVisible(hwnd)
            _, window_pid = win32gui.GetWindowThreadProcessId(hwnd)

            try:
                rect = win32gui.GetWindowRect(hwnd)
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]
            except:
                width = height = 0

            window_info = {
                'hwnd': hwnd,
                'pid': window_pid,
                'title': window_text,
                'class': class_name,
                'visible': is_visible,
                'width': width,
                'height': height
            }

            all_windows.append(window_info)
            
            # 检查是否是QingTalk窗口
            is_qingtalk = False
            match_reason = ""
            
            # 精确匹配：标题为QingTalk
            if window_text == 'QingTalk':
                is_qingtalk = True
                match_reason = "精确标题匹配"
            
            # QingTalk进程的窗口
            elif window_pid in qingtalk_pids:
                is_qingtalk = True
                match_reason = f"QingTalk进程{window_pid}的窗口"
            
            # 其他QingTalk相关
            elif ('qingtalk' in window_text.lower() or 
                  'qingtalk' in class_name.lower() or
                  '钉钉' in window_text):
                is_qingtalk = True
                match_reason = "QingTalk关键词匹配"
            
            if is_qingtalk:
                window_info['match_reason'] = match_reason
                qingtalk_windows.append(window_info)
            
            # 检查Chrome窗口
            if class_name == 'Chrome_WidgetWin_1':
                chrome_windows.append(window_info)
        
        except Exception as e:
            # 调试：打印异常信息
            print(f"  枚举窗口异常: {e}")
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    
    print(f"\n📊 窗口统计:")
    print(f"  总窗口数: {len(all_windows)}")
    print(f"  QingTalk相关窗口: {len(qingtalk_windows)}")
    print(f"  Chrome_WidgetWin_1窗口: {len(chrome_windows)}")
    
    # 3. 显示QingTalk窗口
    if qingtalk_windows:
        print(f"\n📋 QingTalk相关窗口:")
        print("-" * 80)
        
        for i, window in enumerate(qingtalk_windows):
            print(f"窗口 {i+1}:")
            print(f"  句柄: {window['hwnd']}")
            print(f"  PID: {window['pid']}")
            print(f"  标题: '{window['title']}'")
            print(f"  类名: '{window['class']}'")
            print(f"  可见: {'✅' if window['visible'] else '❌'}")
            print(f"  大小: {window['width']}x{window['height']}")
            print(f"  匹配原因: {window['match_reason']}")
            print()
    else:
        print(f"\n❌ 没有找到QingTalk相关窗口")
    
    # 4. 显示Chrome窗口（可能包含QingTalk）
    print(f"\n📋 Chrome_WidgetWin_1窗口 (可能包含QingTalk):")
    print("-" * 80)
    
    # 过滤大的可见Chrome窗口
    large_chrome_windows = [w for w in chrome_windows if w['visible'] and w['width'] > 300 and w['height'] > 200]
    
    for i, window in enumerate(large_chrome_windows[:10]):  # 只显示前10个
        print(f"Chrome窗口 {i+1}:")
        print(f"  句柄: {window['hwnd']}")
        print(f"  PID: {window['pid']}")
        print(f"  标题: '{window['title']}'")
        print(f"  可见: {'✅' if window['visible'] else '❌'}")
        print(f"  大小: {window['width']}x{window['height']}")
        print(f"  是QingTalk进程: {'✅' if window['pid'] in qingtalk_pids else '❌'}")
        print()
    
    # 5. 分析最佳激活窗口
    print("🎯 分析最佳激活窗口:")
    
    # 优先级1: QingTalk标题的大窗口
    qingtalk_title_windows = [w for w in qingtalk_windows if w['title'] == 'QingTalk' and w['visible'] and w['width'] > 300]
    if qingtalk_title_windows:
        best_window = qingtalk_title_windows[0]
        print(f"  ✅ 找到QingTalk标题窗口: PID {best_window['pid']}, 大小 {best_window['width']}x{best_window['height']}")
        return test_activate_window(best_window)
    
    # 优先级2: QingTalk进程的大Chrome窗口
    qingtalk_process_chrome = [w for w in large_chrome_windows if w['pid'] in qingtalk_pids]
    if qingtalk_process_chrome:
        best_window = qingtalk_process_chrome[0]
        print(f"  ✅ 找到QingTalk进程的Chrome窗口: PID {best_window['pid']}, 标题 '{best_window['title']}', 大小 {best_window['width']}x{best_window['height']}")
        return test_activate_window(best_window)
    
    # 优先级3: 任何QingTalk相关的可见窗口
    visible_qingtalk = [w for w in qingtalk_windows if w['visible']]
    if visible_qingtalk:
        best_window = visible_qingtalk[0]
        print(f"  ⚠️ 找到QingTalk相关窗口: PID {best_window['pid']}, 标题 '{best_window['title']}', 大小 {best_window['width']}x{best_window['height']}")
        return test_activate_window(best_window)
    
    print(f"  ❌ 没有找到合适的激活窗口")
    
    if qingtalk_windows:
        print(f"  💡 找到了 {len(qingtalk_windows)} 个QingTalk相关窗口，但都不可见或太小")
    else:
        print(f"  💡 完全没有找到QingTalk窗口，可能还在启动中")
    
    return False

def test_activate_window(window):
    """测试激活窗口"""
    try:
        import win32con
        hwnd = window['hwnd']
        
        print(f"\n🚀 测试激活窗口:")
        print(f"  句柄: {hwnd}")
        print(f"  标题: '{window['title']}'")
        print(f"  类名: '{window['class']}'")
        
        # 尝试激活
        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
        win32gui.SetForegroundWindow(hwnd)
        print("✅ 窗口激活测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 窗口激活测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("最终QingTalk窗口测试")
    print("=" * 60)
    print("💡 基于用户提供的窗口信息进行精确搜索")
    print("💡 窗口标题: 'QingTalk'")
    print("💡 窗口类名: 'Chrome_WidgetWin_1'")
    print()
    
    success = final_qingtalk_window_test()
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    if success:
        print("✅ 找到并成功激活QingTalk窗口")
        print("💡 窗口激活功能应该可以正常工作")
    else:
        print("❌ 没有找到合适的QingTalk窗口")
        print("💡 可能的原因:")
        print("   1. QingTalk还在启动中")
        print("   2. QingTalk窗口被最小化到系统托盘")
        print("   3. 需要等待更长时间")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
