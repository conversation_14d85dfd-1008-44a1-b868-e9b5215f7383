#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程监控工具
监控QingTalk的进程行为、文件访问、注册表访问等
"""

import psutil
import time
import os
import json
from datetime import datetime
from typing import List, Dict

class ProcessMonitor:
    def __init__(self, target_exe_name: str = "QingTalk.exe"):
        self.target_exe_name = target_exe_name.lower()
        self.monitoring = False
        self.log_data = []
        
    def find_target_processes(self) -> List[psutil.Process]:
        """查找目标进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 'create_time']):
            try:
                if proc.info['name'] and self.target_exe_name in proc.info['name'].lower():
                    processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def get_process_details(self, proc: psutil.Process) -> Dict:
        """获取进程详细信息"""
        try:
            info = {
                'pid': proc.pid,
                'name': proc.name(),
                'exe': proc.exe(),
                'cmdline': proc.cmdline(),
                'create_time': datetime.fromtimestamp(proc.create_time()).isoformat(),
                'status': proc.status(),
                'cpu_percent': proc.cpu_percent(),
                'memory_info': proc.memory_info()._asdict(),
                'connections': [],
                'open_files': [],
                'threads': proc.num_threads(),
                'children': []
            }
            
            # 获取网络连接
            try:
                for conn in proc.connections():
                    info['connections'].append({
                        'fd': conn.fd,
                        'family': str(conn.family),
                        'type': str(conn.type),
                        'laddr': conn.laddr._asdict() if conn.laddr else None,
                        'raddr': conn.raddr._asdict() if conn.raddr else None,
                        'status': conn.status
                    })
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
            
            # 获取打开的文件
            try:
                for f in proc.open_files():
                    file_info = {
                        'path': f.path,
                        'fd': f.fd,
                        'mode': getattr(f, 'mode', 'unknown'),
                        'flags': getattr(f, 'flags', 'unknown')
                    }
                    # position属性可能不存在，安全地获取
                    if hasattr(f, 'position'):
                        file_info['position'] = f.position
                    info['open_files'].append(file_info)
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
            
            # 获取子进程
            try:
                for child in proc.children():
                    info['children'].append({
                        'pid': child.pid,
                        'name': child.name(),
                        'create_time': datetime.fromtimestamp(child.create_time()).isoformat()
                    })
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
            
            return info
            
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            return {'error': str(e), 'pid': proc.pid}
    
    def monitor_processes(self, duration: int = 30, interval: float = 1.0):
        """监控进程指定时间"""
        print(f"开始监控 {self.target_exe_name} 进程，持续 {duration} 秒...")
        
        self.monitoring = True
        start_time = time.time()
        
        while self.monitoring and (time.time() - start_time) < duration:
            timestamp = datetime.now().isoformat()
            processes = self.find_target_processes()
            
            snapshot = {
                'timestamp': timestamp,
                'process_count': len(processes),
                'processes': []
            }
            
            for proc in processes:
                proc_info = self.get_process_details(proc)
                snapshot['processes'].append(proc_info)
            
            self.log_data.append(snapshot)
            
            if len(processes) > 0:
                print(f"[{timestamp}] 发现 {len(processes)} 个 {self.target_exe_name} 进程")
                for proc in processes:
                    try:
                        print(f"  PID: {proc.pid}, 状态: {proc.status()}")
                    except:
                        print(f"  PID: {proc.pid}, 状态: 未知")
            
            time.sleep(interval)
        
        print("监控完成")
        return self.log_data
    
    def save_log(self, filename: str = None):
        """保存监控日志"""
        if not filename:
            filename = f"process_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.log_data, f, indent=2, ensure_ascii=False)
        
        print(f"监控日志已保存到: {filename}")
        return filename
    
    def analyze_log(self) -> Dict:
        """分析监控日志"""
        if not self.log_data:
            return {}
        
        analysis = {
            'total_snapshots': len(self.log_data),
            'max_processes': 0,
            'process_lifecycle': [],
            'file_access_patterns': {},
            'network_connections': {},
            'process_creation_times': []
        }
        
        seen_pids = set()
        
        for snapshot in self.log_data:
            # 统计最大进程数
            analysis['max_processes'] = max(analysis['max_processes'], snapshot['process_count'])
            
            for proc in snapshot['processes']:
                if 'error' in proc:
                    continue
                
                pid = proc['pid']
                
                # 记录新进程
                if pid not in seen_pids:
                    seen_pids.add(pid)
                    analysis['process_creation_times'].append({
                        'pid': pid,
                        'create_time': proc.get('create_time'),
                        'cmdline': proc.get('cmdline', [])
                    })
                
                # 分析文件访问模式
                for file_info in proc.get('open_files', []):
                    file_path = file_info['path']
                    if file_path not in analysis['file_access_patterns']:
                        analysis['file_access_patterns'][file_path] = 0
                    analysis['file_access_patterns'][file_path] += 1
                
                # 分析网络连接
                for conn in proc.get('connections', []):
                    if conn['laddr']:
                        addr_key = f"{conn['laddr']['ip']}:{conn['laddr']['port']}"
                        if addr_key not in analysis['network_connections']:
                            analysis['network_connections'][addr_key] = 0
                        analysis['network_connections'][addr_key] += 1
        
        return analysis

def monitor_qingtalk_startup():
    """监控QingTalk启动过程"""
    print("=" * 50)
    print("QingTalk启动监控")
    print("=" * 50)
    
    monitor = ProcessMonitor("QingTalk.exe")
    
    print("请在另一个终端启动QingTalk，监控将持续30秒...")
    input("按回车键开始监控...")
    
    # 监控30秒
    log_data = monitor.monitor_processes(duration=30, interval=0.5)
    
    # 保存日志
    log_file = monitor.save_log()
    
    # 分析结果
    analysis = monitor.analyze_log()
    
    print("\n" + "=" * 50)
    print("监控分析结果")
    print("=" * 50)
    
    print(f"总快照数: {analysis['total_snapshots']}")
    print(f"最大进程数: {analysis['max_processes']}")
    print(f"发现的进程数: {len(analysis['process_creation_times'])}")
    
    if analysis['process_creation_times']:
        print("\n进程创建时间:")
        for proc_info in analysis['process_creation_times']:
            print(f"  PID {proc_info['pid']}: {proc_info['create_time']}")
            if proc_info['cmdline']:
                print(f"    命令行: {' '.join(proc_info['cmdline'])}")
    
    if analysis['file_access_patterns']:
        print(f"\n文件访问模式 (前10个):")
        sorted_files = sorted(analysis['file_access_patterns'].items(), 
                            key=lambda x: x[1], reverse=True)[:10]
        for file_path, count in sorted_files:
            print(f"  {count:3d}x {file_path}")
    
    if analysis['network_connections']:
        print(f"\n网络连接:")
        for addr, count in analysis['network_connections'].items():
            print(f"  {count:3d}x {addr}")
    
    return log_file, analysis

if __name__ == "__main__":
    monitor_qingtalk_startup()
