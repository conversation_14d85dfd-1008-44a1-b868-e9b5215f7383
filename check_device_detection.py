#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查QingTalk如何获取设备信息
分析可能的检测方法
"""

import os
import platform
import subprocess
import winreg

def check_system_info():
    """检查系统信息获取方法"""
    print("=" * 60)
    print("系统设备信息检查")
    print("=" * 60)
    
    # 1. 环境变量方式
    print("\n📋 环境变量方式:")
    env_vars = ['COMPUTERNAME', 'USERNAME', 'USERDOMAIN', 'PROCESSOR_IDENTIFIER']
    for var in env_vars:
        value = os.environ.get(var, 'Not Found')
        print(f"  {var}: {value}")
    
    # 2. Python platform模块
    print("\n🐍 Python platform模块:")
    try:
        print(f"  node(): {platform.node()}")
        print(f"  system(): {platform.system()}")
        print(f"  processor(): {platform.processor()}")
        print(f"  machine(): {platform.machine()}")
    except Exception as e:
        print(f"  获取失败: {e}")
    
    # 3. Windows API (通过subprocess)
    print("\n🪟 Windows命令行:")
    try:
        # 获取计算机名
        result = subprocess.run(['hostname'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  hostname: {result.stdout.strip()}")
        
        # 获取用户名
        result = subprocess.run(['whoami'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  whoami: {result.stdout.strip()}")
            
        # 获取系统信息
        result = subprocess.run(['systeminfo'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines[:10]:  # 只显示前10行
                if line.strip():
                    print(f"  {line.strip()}")
                    
    except Exception as e:
        print(f"  获取失败: {e}")
    
    # 4. 注册表方式
    print("\n📝 注册表方式:")
    try:
        # 计算机名
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                          r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName") as key:
            computer_name, _ = winreg.QueryValueEx(key, "ComputerName")
            print(f"  注册表计算机名: {computer_name}")
            
        # 处理器信息
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                          r"HARDWARE\DESCRIPTION\System\CentralProcessor\0") as key:
            processor_name, _ = winreg.QueryValueEx(key, "ProcessorNameString")
            print(f"  注册表处理器: {processor_name}")
            
    except Exception as e:
        print(f"  注册表读取失败: {e}")
    
    # 5. WMI方式 (如果有pywin32)
    print("\n🔍 WMI方式:")
    try:
        import wmi
        c = wmi.WMI()
        
        # 计算机系统信息
        for computer in c.Win32_ComputerSystem():
            print(f"  WMI计算机名: {computer.Name}")
            print(f"  WMI用户名: {computer.UserName}")
            break
            
        # 处理器信息
        for processor in c.Win32_Processor():
            print(f"  WMI处理器: {processor.Name}")
            break
            
    except ImportError:
        print("  WMI模块未安装 (pip install pywin32)")
    except Exception as e:
        print(f"  WMI获取失败: {e}")

def test_environment_isolation():
    """测试环境变量隔离效果"""
    print("\n" + "=" * 60)
    print("环境变量隔离测试")
    print("=" * 60)
    
    # 创建修改后的环境变量
    fake_env = os.environ.copy()
    fake_env.update({
        'COMPUTERNAME': 'FAKE-COMPUTER-123',
        'USERNAME': 'FakeUser123',
        'USERDOMAIN': 'FAKE-DOMAIN',
    })
    
    print("🧪 启动子进程测试环境变量隔离...")
    
    # 创建测试脚本
    test_script = '''
import os
print("子进程环境变量:")
print(f"COMPUTERNAME: {os.environ.get('COMPUTERNAME', 'Not Found')}")
print(f"USERNAME: {os.environ.get('USERNAME', 'Not Found')}")
print(f"USERDOMAIN: {os.environ.get('USERDOMAIN', 'Not Found')}")
'''
    
    try:
        result = subprocess.run(['python', '-c', test_script], 
                              env=fake_env, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 子进程输出:")
            for line in result.stdout.strip().split('\n'):
                print(f"  {line}")
        else:
            print(f"❌ 子进程执行失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def analyze_qingtalk_detection():
    """分析QingTalk可能的检测方法"""
    print("\n" + "=" * 60)
    print("QingTalk检测方法分析")
    print("=" * 60)
    
    print("🔍 QingTalk可能使用的检测方法:")
    print("  1. Windows API调用:")
    print("     - GetComputerNameA/W")
    print("     - GetUserNameA/W") 
    print("     - GetSystemInfo")
    print("  2. 注册表读取:")
    print("     - HKLM\\SYSTEM\\CurrentControlSet\\Control\\ComputerName")
    print("     - HKLM\\HARDWARE\\DESCRIPTION\\System")
    print("  3. WMI查询:")
    print("     - Win32_ComputerSystem")
    print("     - Win32_Processor")
    print("     - Win32_BaseBoard")
    print("  4. 硬件指纹:")
    print("     - CPU序列号")
    print("     - 主板序列号")
    print("     - 硬盘序列号")
    print("     - MAC地址")
    
    print("\n💡 可能的解决方案:")
    print("  1. API Hook (需要DLL注入)")
    print("  2. 虚拟化容器 (Docker/VM)")
    print("  3. 进程沙箱")
    print("  4. 系统级伪造 (修改注册表)")
    
    print("\n⚠️ 当前限制:")
    print("  - 环境变量隔离对Windows API无效")
    print("  - 注册表修改需要管理员权限")
    print("  - API Hook需要复杂的DLL注入技术")

def main():
    print("QingTalk设备信息检测分析工具")
    
    # 检查系统信息获取方法
    check_system_info()
    
    # 测试环境变量隔离
    test_environment_isolation()
    
    # 分析QingTalk检测方法
    analyze_qingtalk_detection()
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print("💡 建议:")
    print("  1. 当前的环境变量方法对QingTalk可能无效")
    print("  2. 需要更深层的API Hook或虚拟化方案")
    print("  3. 可以尝试使用专业的进程隔离工具")
    print("  4. 或者考虑使用虚拟机方案")

if __name__ == "__main__":
    main()
