#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复设置文件工具
清理可能损坏的配置文件
"""

import os
import json
import shutil
from datetime import datetime

def fix_settings():
    """修复设置文件"""
    settings_file = "settings.json"
    
    print("🔧 设置文件修复工具")
    print("=" * 40)
    
    if os.path.exists(settings_file):
        # 备份原文件
        backup_file = f"settings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        shutil.copy2(settings_file, backup_file)
        print(f"📄 已备份原设置文件到: {backup_file}")
        
        try:
            # 尝试读取现有设置
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            print(f"✅ 设置文件格式正确")
            
            # 检查实例数据格式
            instances = settings.get('instances', [])
            fixed_instances = []
            
            for i, instance_data in enumerate(instances):
                if isinstance(instance_data, dict):
                    # 确保所有必需字段存在
                    fixed_instance = {
                        'id': instance_data.get('id', f'fixed_instance_{i}'),
                        'name': instance_data.get('name', f'Instance_{i}'),
                        'exe_path': instance_data.get('exe_path', ''),
                        'proxy_ip': instance_data.get('proxy_ip', ''),
                        'proxy_port': instance_data.get('proxy_port', ''),
                        'proxy_username': instance_data.get('proxy_username', ''),
                        'proxy_password': instance_data.get('proxy_password', '')
                    }
                    fixed_instances.append(fixed_instance)
                    print(f"✅ 实例 {i+1}: {fixed_instance['name']} - 格式正确")
                else:
                    print(f"⚠️ 实例 {i+1}: 格式错误，已跳过")
            
            # 更新设置
            settings['instances'] = fixed_instances
            
            # 保存修复后的设置
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 设置文件已修复，保留 {len(fixed_instances)} 个有效实例")
            
        except json.JSONDecodeError as e:
            print(f"❌ 设置文件JSON格式错误: {e}")
            print("🔧 创建新的设置文件...")
            create_new_settings(settings_file)
            
        except Exception as e:
            print(f"❌ 修复设置文件时出错: {e}")
            print("🔧 创建新的设置文件...")
            create_new_settings(settings_file)
    else:
        print("📄 设置文件不存在，创建新文件...")
        create_new_settings(settings_file)

def create_new_settings(settings_file):
    """创建新的设置文件"""
    new_settings = {
        'instances': [],
        'window_geometry': '900x500',
        'saved_time': datetime.now().isoformat(),
        'version': '1.2'
    }
    
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(new_settings, f, indent=2, ensure_ascii=False)
        print(f"✅ 已创建新的设置文件: {settings_file}")
    except Exception as e:
        print(f"❌ 创建设置文件失败: {e}")

def clean_temp_files():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    
    import tempfile
    temp_dir = tempfile.gettempdir()
    
    patterns = [
        'qingtalk_optimized_',
        'qingtalk_enhanced_',
        'qingtalk_isolated_',
        'userdata_',
        'isolated_exe_'
    ]
    
    cleaned_count = 0
    
    try:
        for item in os.listdir(temp_dir):
            item_path = os.path.join(temp_dir, item)
            
            if os.path.isdir(item_path):
                for pattern in patterns:
                    if item.startswith(pattern):
                        try:
                            shutil.rmtree(item_path, ignore_errors=True)
                            print(f"🗑️ 已清理: {item}")
                            cleaned_count += 1
                            break
                        except:
                            pass
        
        print(f"✅ 已清理 {cleaned_count} 个临时目录")
        
    except Exception as e:
        print(f"⚠️ 清理临时文件时出错: {e}")

def main():
    print("多开管理器 - 设置修复工具")
    print("=" * 50)
    
    # 修复设置文件
    fix_settings()
    
    # 清理临时文件
    clean_temp_files()
    
    print("\n" + "=" * 50)
    print("✅ 修复完成！")
    print("💡 现在可以重新启动多开管理器了")
    print("=" * 50)

if __name__ == "__main__":
    main()
