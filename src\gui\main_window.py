#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
类似雷电模拟器的中控界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from typing import Dict, List

from .instance_panel import InstancePanel
from .proxy_panel import ProxyPanel
from .settings_panel import SettingsPanel

class MainWindow:
    def __init__(self, root, instance_manager, config_manager):
        self.root = root
        self.instance_manager = instance_manager
        self.config_manager = config_manager
        
        self.setup_window()
        self.create_widgets()
        self.bind_events()
        
        # 启动定时更新
        self.update_status()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("多开管理器")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主菜单
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_main_content()
        
        # 创建状态栏
        self.create_statusbar()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建实例", command=self.new_instance)
        file_menu.add_separator()
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 实例菜单
        instance_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="实例", menu=instance_menu)
        instance_menu.add_command(label="启动所有", command=self.start_all_instances)
        instance_menu.add_command(label="停止所有", command=self.stop_all_instances)
        instance_menu.add_separator()
        instance_menu.add_command(label="删除所有", command=self.delete_all_instances)
        
        # 代理菜单
        proxy_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="代理", menu=proxy_menu)
        proxy_menu.add_command(label="添加代理", command=self.add_proxy)
        proxy_menu.add_command(label="测试代理", command=self.test_proxies)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        #menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # 新建实例按钮
        ttk.Button(
            self.toolbar,
            text="新建实例",
            command=self.new_instance
        ).pack(side=tk.LEFT, padx=2)

        # 批量新建按钮
        ttk.Button(
            self.toolbar,
            text="批量新建",
            command=self.batch_new_instances
        ).pack(side=tk.LEFT, padx=2)
        
        # 启动所有按钮
        ttk.Button(
            self.toolbar, 
            text="启动所有", 
            command=self.start_all_instances
        ).pack(side=tk.LEFT, padx=2)
        
        # 停止所有按钮
        ttk.Button(
            self.toolbar, 
            text="停止所有", 
            command=self.stop_all_instances
        ).pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 添加代理按钮
        ttk.Button(
            self.toolbar, 
            text="添加代理", 
            command=self.add_proxy
        ).pack(side=tk.LEFT, padx=2)
        
        ## 设置按钮
        # ttk.Button(
        #     self.toolbar,
        #     text="设置",
        #     command=self.show_settings
        # ).pack(side=tk.RIGHT, padx=2)
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 实例管理标签页
        self.instance_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.instance_frame, text="实例管理")
        self.instance_panel = InstancePanel(
            self.instance_frame, 
            self.instance_manager, 
            self.config_manager
        )
        
        # 代理管理标签页
        self.proxy_frame = ttk.Frame(self.notebook)
        #self.notebook.add(self.proxy_frame, text="代理管理")
        self.proxy_panel = ProxyPanel(
            self.proxy_frame, 
            self.instance_manager.proxy_manager, 
            self.config_manager
        )
        
        # 设置标签页
        #self.settings_frame = ttk.Frame(self.notebook)
        #self.notebook.add(self.settings_frame, text="设置")
        # self.settings_panel = SettingsPanel(
        #     self.settings_frame,
        #     self.config_manager
        # )
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = ttk.Frame(self.root)
        self.statusbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态标签
        self.status_label = ttk.Label(self.statusbar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 实例统计
        self.instance_stats_label = ttk.Label(self.statusbar, text="实例: 0/0")
        self.instance_stats_label.pack(side=tk.RIGHT, padx=5)
        
        # 代理统计
        self.proxy_stats_label = ttk.Label(self.statusbar, text="代理: 0/0")
        self.proxy_stats_label.pack(side=tk.RIGHT, padx=5)
    
    def bind_events(self):
        """绑定事件"""
        # 窗口关闭事件已在main.py中处理
        pass
    
    def new_instance(self):
        """新建实例"""
        # 选择exe文件
        exe_path = filedialog.askopenfilename(
            title="选择要多开的exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        
        if exe_path:
            try:
                instance_id = self.instance_manager.create_instance(exe_path)
                self.update_status()
                self.instance_panel.refresh_instances()
                messagebox.showinfo("成功", f"实例创建成功！\nID: {instance_id}")
            except Exception as e:
                messagebox.showerror("错误", f"创建实例失败: {str(e)}")

    def batch_new_instances(self):
        """批量新建实例"""
        self.show_batch_create_dialog()

    def start_all_instances(self):
        """启动所有实例"""
        def start_thread():
            try:
                instances = self.instance_manager.get_all_instances()
                for instance in instances:
                    if instance.status == "stopped":
                        self.instance_manager.start_instance(instance.id)
                
                self.root.after(0, lambda: self.update_status())
                self.root.after(0, lambda: self.instance_panel.refresh_instances())
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"启动实例失败: {str(e)}"))
        
        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_all_instances(self):
        """停止所有实例"""
        try:
            self.instance_manager.stop_all_instances()
            self.update_status()
            self.instance_panel.refresh_instances()
        except Exception as e:
            messagebox.showerror("错误", f"停止实例失败: {str(e)}")
    
    def delete_all_instances(self):
        """删除所有实例"""
        if messagebox.askyesno("确认", "确定要删除所有实例吗？"):
            try:
                instances = self.instance_manager.get_all_instances()
                for instance in instances:
                    self.instance_manager.remove_instance(instance.id)
                
                self.update_status()
                self.instance_panel.refresh_instances()
                
            except Exception as e:
                messagebox.showerror("错误", f"删除实例失败: {str(e)}")
    
    def add_proxy(self):
        """添加代理"""
        self.proxy_panel.add_proxy_dialog()
    
    def test_proxies(self):
        """测试代理"""
        self.proxy_panel.test_all_proxies()
    
    def import_config(self):
        """导入配置"""
        # TODO: 实现配置导入
        messagebox.showinfo("提示", "配置导入功能待实现")
    
    def export_config(self):
        """导出配置"""
        # TODO: 实现配置导出
        messagebox.showinfo("提示", "配置导出功能待实现")
    
    def show_settings(self):
        """显示设置"""
        #self.notebook.select(self.settings_frame)
        pass
    
    def show_help(self):
        """显示帮助"""
        help_text = """
多开管理器使用说明：

1. 新建实例：点击"新建实例"按钮，选择要多开的exe文件
2. 启动实例：在实例列表中点击"启动"按钮
3. 代理设置：在"代理管理"标签页中添加和管理代理
4. 系统隔离：每个实例都有独立的系统标识和运行环境

注意事项：
- 某些功能需要管理员权限
- 建议使用稳定的代理服务器
- 请遵守相关法律法规
        """
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于"""
        about_text = """
多开管理器 v1.0

一个类似雷电模拟器的多开管理工具
支持exe程序的沙盒多开、代理IP、系统标识修改等功能

开发语言：Python
界面框架：Tkinter
        """
        messagebox.showinfo("关于", about_text)
    
    def update_status(self):
        """更新状态栏"""
        try:
            # 更新实例统计
            instances = self.instance_manager.get_all_instances()
            running_count = len([i for i in instances if i.status == "running"])
            total_count = len(instances)
            self.instance_stats_label.config(text=f"实例: {running_count}/{total_count}")
            
            # 更新代理统计
            proxy_stats = self.instance_manager.proxy_manager.get_proxy_stats()
            self.proxy_stats_label.config(
                text=f"代理: {proxy_stats['active']}/{proxy_stats['total']}"
            )
            
            # 定时更新
            self.root.after(2000, self.update_status)

        except Exception as e:
            print(f"更新状态栏失败: {e}")
            self.root.after(5000, self.update_status)

    def show_batch_create_dialog(self):
        """显示批量创建对话框"""
        dialog = BatchCreateDialog(self.root, self.instance_manager, self.config_manager)
        dialog.show()

        # 刷新界面
        if dialog.created_count > 0:
            self.update_status()
            self.instance_panel.refresh_instances()


class BatchCreateDialog:
    """批量创建实例对话框"""

    def __init__(self, parent, instance_manager, config_manager):
        self.parent = parent
        self.instance_manager = instance_manager
        self.config_manager = config_manager
        self.created_count = 0

        self.dialog = None
        self.exe_path_var = tk.StringVar()
        self.count_var = tk.StringVar(value="2")
        self.prefix_var = tk.StringVar(value="实例")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="准备就绪")

    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("批量新建实例")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_dialog()

        self.create_widgets()

        # 等待对话框关闭
        self.dialog.wait_window()

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="批量新建实例", font=("", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 选择exe文件
        exe_frame = ttk.LabelFrame(main_frame, text="选择程序", padding="10")
        exe_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(exe_frame, text="程序路径:").pack(anchor=tk.W)

        path_frame = ttk.Frame(exe_frame)
        path_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Entry(path_frame, textvariable=self.exe_path_var, state="readonly").pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_frame, text="浏览", command=self.browse_exe).pack(side=tk.RIGHT, padx=(5, 0))

        # 创建设置
        settings_frame = ttk.LabelFrame(main_frame, text="创建设置", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 15))

        # 实例数量
        count_frame = ttk.Frame(settings_frame)
        count_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(count_frame, text="创建数量:").pack(side=tk.LEFT)
        count_spinbox = ttk.Spinbox(count_frame, from_=1, to=20, width=10, textvariable=self.count_var)
        count_spinbox.pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(count_frame, text="个实例").pack(side=tk.LEFT, padx=(5, 0))

        # 实例前缀
        prefix_frame = ttk.Frame(settings_frame)
        prefix_frame.pack(fill=tk.X)

        ttk.Label(prefix_frame, text="名称前缀:").pack(side=tk.LEFT)
        ttk.Entry(prefix_frame, textvariable=self.prefix_var, width=15).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(prefix_frame, text="(将自动添加序号)").pack(side=tk.LEFT, padx=(5, 0))

        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="创建进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))

        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.pack(anchor=tk.W)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        self.create_button = ttk.Button(button_frame, text="开始创建", command=self.start_create)
        self.create_button.pack(side=tk.RIGHT)

    def browse_exe(self):
        """浏览exe文件"""
        exe_path = filedialog.askopenfilename(
            title="选择要多开的exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )

        if exe_path:
            self.exe_path_var.set(exe_path)

    def start_create(self):
        """开始创建实例"""
        exe_path = self.exe_path_var.get()
        if not exe_path:
            messagebox.showerror("错误", "请先选择exe文件")
            return

        try:
            count = int(self.count_var.get())
            if count < 1 or count > 20:
                messagebox.showerror("错误", "创建数量必须在1-20之间")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数量")
            return

        prefix = self.prefix_var.get().strip()
        if not prefix:
            messagebox.showerror("错误", "请输入名称前缀")
            return

        # 禁用按钮
        self.create_button.config(state="disabled")

        # 在后台线程中创建实例
        threading.Thread(target=self.create_instances_thread, args=(exe_path, count, prefix), daemon=True).start()

    def create_instances_thread(self, exe_path, count, prefix):
        """在后台线程中创建实例"""
        try:
            for i in range(count):
                # 更新进度
                progress = (i / count) * 100
                self.dialog.after(0, lambda p=progress: self.progress_var.set(p))
                self.dialog.after(0, lambda i=i: self.status_var.set(f"正在创建第 {i+1} 个实例..."))

                # 创建实例
                instance_id = self.instance_manager.create_instance(exe_path, f"{prefix}{i+1:02d}")

                # 立即创建完整环境（这是关键改进）
                self.dialog.after(0, lambda i=i: self.status_var.set(f"正在为第 {i+1} 个实例创建环境..."))
                self.create_complete_environment(instance_id, exe_path)

                self.created_count += 1

            # 完成
            self.dialog.after(0, lambda: self.progress_var.set(100))
            self.dialog.after(0, lambda: self.status_var.set(f"成功创建 {count} 个实例"))
            self.dialog.after(0, lambda: messagebox.showinfo("成功", f"成功创建 {count} 个实例！"))
            self.dialog.after(0, self.close)

        except Exception as e:
            self.dialog.after(0, lambda: messagebox.showerror("错误", f"创建实例失败: {str(e)}"))
            self.dialog.after(0, lambda: self.create_button.config(state="normal"))

    def create_complete_environment(self, instance_id, exe_path):
        """立即创建完整环境"""
        try:
            # 这里调用实例管理器的方法来立即创建完整环境
            # 我们需要在instance_manager中添加这个方法
            if hasattr(self.instance_manager, 'create_complete_environment'):
                self.instance_manager.create_complete_environment(instance_id, exe_path)
        except Exception as e:
            print(f"创建完整环境失败: {e}")

    def cancel(self):
        """取消创建"""
        self.close()

    def close(self):
        """关闭对话框"""
        if self.dialog:
            self.dialog.destroy()
