('D:\\text\\py\\duokai\\dist\\fix_network_before_start.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON><PERSON>,
 <PERSON>als<PERSON>,
 ['D:\\text\\py\\duokai\\icon.ico'],
 None,
 <PERSON>alse,
 <PERSON>alse,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\text\\py\\duokai\\build\\fix_network_before_start\\fix_network_before_start.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('fix_network_before_start',
   'D:\\text\\py\\duokai\\fix_network_before_start.py',
   'PYSOURCE'),
  ('python311.dll',
   'D:\\Software\\develop\\Python311\\python311.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Software\\develop\\Python311\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Software\\develop\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Software\\develop\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Software\\develop\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Software\\develop\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('icon.ico', 'D:\\text\\py\\duokai\\icon.ico', 'DATA'),
  ('base_library.zip',
   'D:\\text\\py\\duokai\\build\\fix_network_before_start\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1754566237,
 [('runw.exe',
   'D:\\Software\\develop\\Python311\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'D:\\Software\\develop\\Python311\\python311.dll')
