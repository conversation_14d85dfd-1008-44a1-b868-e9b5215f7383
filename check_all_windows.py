#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有窗口
"""

import win32gui

def check_all_windows():
    """检查所有窗口"""
    print("🔍 检查所有系统窗口...")
    
    all_windows = []
    
    def enum_windows_callback(hwnd, data):
        try:
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            is_visible = win32gui.IsWindowVisible(hwnd)
            _, window_pid = win32gui.GetWindowThreadProcessId(hwnd)
            
            try:
                rect = win32gui.GetWindowRect(hwnd)
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]
            except:
                width = height = 0
            
            window_info = {
                'hwnd': hwnd,
                'pid': window_pid,
                'title': window_text,
                'class': class_name,
                'visible': is_visible,
                'width': width,
                'height': height
            }
            
            all_windows.append(window_info)
        
        except Exception:
            pass
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    
    print(f"📊 系统窗口统计:")
    print(f"  总窗口数: {len(all_windows)}")
    
    # 统计可见窗口
    visible_windows = [w for w in all_windows if w['visible']]
    print(f"  可见窗口: {len(visible_windows)}")
    
    # 统计有标题的窗口
    titled_windows = [w for w in all_windows if w['title'].strip()]
    print(f"  有标题窗口: {len(titled_windows)}")
    
    # 统计大窗口
    large_windows = [w for w in all_windows if w['width'] > 300 and w['height'] > 200]
    print(f"  大窗口: {len(large_windows)}")
    
    # 显示一些可见的大窗口
    visible_large_windows = [w for w in all_windows if w['visible'] and w['width'] > 300 and w['height'] > 200]
    print(f"  可见大窗口: {len(visible_large_windows)}")
    
    print(f"\n📋 显示前10个可见大窗口:")
    print("-" * 80)
    
    for i, window in enumerate(visible_large_windows[:10]):
        print(f"窗口 {i+1}:")
        print(f"  PID: {window['pid']}")
        print(f"  标题: '{window['title']}'")
        print(f"  类名: '{window['class']}'")
        print(f"  大小: {window['width']}x{window['height']}")
        print()
    
    # 搜索可能的QingTalk相关窗口
    print("🔍 搜索可能的QingTalk相关窗口:")
    
    qingtalk_related = []
    for window in all_windows:
        if (window['title'] and 
            ('qingtalk' in window['title'].lower() or 
             '钉钉' in window['title'] or
             window['title'] == 'QingTalk')):
            qingtalk_related.append(window)
        elif (window['class'] and 
              ('qingtalk' in window['class'].lower() or
               'chrome' in window['class'].lower())):
            qingtalk_related.append(window)
    
    if qingtalk_related:
        print(f"  找到 {len(qingtalk_related)} 个可能相关的窗口:")
        for window in qingtalk_related:
            print(f"    PID: {window['pid']}, 标题: '{window['title']}', 类: '{window['class']}', 可见: {window['visible']}")
    else:
        print(f"  ❌ 没有找到任何QingTalk相关窗口")
    
    # 搜索Chrome相关窗口
    print(f"\n🔍 搜索Chrome相关窗口:")
    
    chrome_related = []
    for window in all_windows:
        if (window['class'] and 'chrome' in window['class'].lower()):
            chrome_related.append(window)
        elif (window['title'] and 'chrome' in window['title'].lower()):
            chrome_related.append(window)
    
    if chrome_related:
        print(f"  找到 {len(chrome_related)} 个Chrome相关窗口:")
        for window in chrome_related:
            print(f"    PID: {window['pid']}, 标题: '{window['title']}', 类: '{window['class']}', 可见: {window['visible']}")
    else:
        print(f"  ❌ 没有找到任何Chrome相关窗口")

def main():
    print("=" * 60)
    print("系统窗口检查工具")
    print("=" * 60)
    
    check_all_windows()
    
    print("\n" + "=" * 60)
    print("检查完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
