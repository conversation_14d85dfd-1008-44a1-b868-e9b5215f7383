#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理面板
管理代理IP配置
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
from datetime import datetime

class ProxyPanel:
    def __init__(self, parent, proxy_manager, config_manager):
        self.parent = parent
        self.proxy_manager = proxy_manager
        self.config_manager = config_manager
        
        self.create_widgets()
        self.refresh_proxies()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        self.toolbar = ttk.Frame(self.main_frame)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        
        # 工具栏按钮
        ttk.Button(
            self.toolbar, 
            text="添加代理", 
            command=self.add_proxy_dialog
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="测试选中", 
            command=self.test_selected_proxy
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="测试所有", 
            command=self.test_all_proxies
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="删除选中", 
            command=self.delete_selected_proxy
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="刷新", 
            command=self.refresh_proxies
        ).pack(side=tk.LEFT, padx=2)
        
        # 创建代理列表
        self.create_proxy_list()
        
        # 创建统计信息
        self.create_stats_panel()
    
    def create_proxy_list(self):
        """创建代理列表"""
        # 创建框架
        list_frame = ttk.Frame(self.main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('ID', '类型', '地址', '端口', '用户名', '状态', '响应时间', '错误次数', '最后检查')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
        
        # 设置列宽
        self.tree.column('ID', width=80)
        self.tree.column('类型', width=60)
        self.tree.column('地址', width=120)
        self.tree.column('端口', width=60)
        self.tree.column('用户名', width=80)
        self.tree.column('状态', width=60)
        self.tree.column('响应时间', width=80)
        self.tree.column('错误次数', width=80)
        self.tree.column('最后检查', width=120)
        
        # 创建滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定事件
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Button-3>', self.on_right_click)
        
        # 创建右键菜单
        self.create_context_menu()
    
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="测试代理", command=self.test_selected_proxy)
        self.context_menu.add_command(label="编辑代理", command=self.edit_selected_proxy)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除代理", command=self.delete_selected_proxy)
    
    def create_stats_panel(self):
        """创建统计信息面板"""
        stats_frame = ttk.LabelFrame(self.main_frame, text="代理统计", padding=5)
        stats_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        
        # 统计标签
        self.stats_labels = {}
        
        # 第一行
        row1 = ttk.Frame(stats_frame)
        row1.pack(fill=tk.X)
        
        ttk.Label(row1, text="总数:").pack(side=tk.LEFT)
        self.stats_labels['total'] = ttk.Label(row1, text="0")
        self.stats_labels['total'].pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="可用:").pack(side=tk.LEFT)
        self.stats_labels['active'] = ttk.Label(row1, text="0")
        self.stats_labels['active'].pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="已分配:").pack(side=tk.LEFT)
        self.stats_labels['assigned'] = ttk.Label(row1, text="0")
        self.stats_labels['assigned'].pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="空闲:").pack(side=tk.LEFT)
        self.stats_labels['available'] = ttk.Label(row1, text="0")
        self.stats_labels['available'].pack(side=tk.LEFT, padx=(5, 20))
    

    
    def refresh_proxies(self):
        """刷新代理列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加代理
        proxies = self.proxy_manager.get_all_proxies()
        for proxy in proxies:
            self.add_proxy_to_tree(proxy)
        
        # 更新统计信息
        self.update_stats()
    
    def add_proxy_to_tree(self, proxy):
        """添加代理到树形控件"""
        # 格式化时间
        last_check = proxy.last_check.strftime('%Y-%m-%d %H:%M:%S') if proxy.last_check else '-'
        
        # 格式化响应时间
        response_time = f"{proxy.response_time:.2f}s" if proxy.response_time else '-'
        
        # 状态显示
        status = '可用' if proxy.is_active else '不可用'
        
        # 添加到树形控件
        item = self.tree.insert('', tk.END, values=(
            proxy.id[:8] + '...',  # 显示ID的前8位
            proxy.type,
            proxy.host,
            proxy.port,
            proxy.username or '-',
            status,
            response_time,
            proxy.error_count,
            last_check
        ), tags=(proxy.id,))
        
        # 根据状态设置颜色
        if proxy.is_active:
            self.tree.item(item, tags=('active',))
        else:
            self.tree.item(item, tags=('inactive',))
    
    def update_stats(self):
        """更新统计信息"""
        stats = self.proxy_manager.get_proxy_stats()
        
        self.stats_labels['total'].config(text=str(stats['total']))
        self.stats_labels['active'].config(text=str(stats['active']))
        self.stats_labels['assigned'].config(text=str(stats['assigned']))
        self.stats_labels['available'].config(text=str(stats['available']))
    
    def get_selected_proxy_id(self):
        """获取选中的代理ID"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = selection[0]
        tags = self.tree.item(item, 'tags')
        return tags[0] if tags else None
    
    def add_proxy_dialog(self):
        """添加代理对话框"""
        dialog = ProxyAddDialog(self.parent, self.proxy_manager)
        if dialog.result:
            self.refresh_proxies()
    
    def test_selected_proxy(self):
        """测试选中的代理"""
        proxy_id = self.get_selected_proxy_id()
        if not proxy_id:
            messagebox.showwarning("警告", "请先选择一个代理")
            return
        
        def test_thread():
            try:
                proxy = self.proxy_manager.proxies.get(proxy_id)
                if proxy:
                    success = self.proxy_manager.check_proxy(proxy)
                    result = "测试成功" if success else "测试失败"
                    self.parent.after(0, lambda: messagebox.showinfo("测试结果", result))
                    self.parent.after(0, self.refresh_proxies)
                else:
                    self.parent.after(0, lambda: messagebox.showerror("错误", "代理不存在"))
                    
            except Exception as e:
                self.parent.after(0, lambda: messagebox.showerror("错误", f"测试代理失败: {str(e)}"))
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def test_all_proxies(self):
        """测试所有代理"""
        def test_thread():
            try:
                proxies = self.proxy_manager.get_all_proxies()
                success_count = 0
                
                for proxy in proxies:
                    if self.proxy_manager.check_proxy(proxy):
                        success_count += 1
                
                result = f"测试完成\n成功: {success_count}\n失败: {len(proxies) - success_count}"
                self.parent.after(0, lambda: messagebox.showinfo("测试结果", result))
                self.parent.after(0, self.refresh_proxies)
                
            except Exception as e:
                self.parent.after(0, lambda: messagebox.showerror("错误", f"测试代理失败: {str(e)}"))
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def delete_selected_proxy(self):
        """删除选中的代理"""
        proxy_id = self.get_selected_proxy_id()
        if not proxy_id:
            messagebox.showwarning("警告", "请先选择一个代理")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的代理吗？"):
            try:
                success = self.proxy_manager.remove_proxy(proxy_id)
                if success:
                    messagebox.showinfo("成功", "代理删除成功")
                    self.refresh_proxies()
                else:
                    messagebox.showerror("错误", "代理删除失败（可能正在使用中）")
                    
            except Exception as e:
                messagebox.showerror("错误", f"删除代理失败: {str(e)}")
    
    def edit_selected_proxy(self):
        """编辑选中的代理"""
        # TODO: 实现代理编辑功能
        messagebox.showinfo("提示", "代理编辑功能待实现")
    
    def on_double_click(self, event):
        """双击事件"""
        self.test_selected_proxy()
    
    def on_right_click(self, event):
        """右键点击事件"""
        # 选中右键点击的项目
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

class ProxyAddDialog:
    def __init__(self, parent, proxy_manager):
        self.parent = parent
        self.proxy_manager = proxy_manager
        self.result = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("添加代理")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        # 创建表单
        self.create_form()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def create_form(self):
        """创建表单"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 代理类型
        ttk.Label(main_frame, text="代理类型:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.proxy_type = ttk.Combobox(main_frame, values=['http', 'https', 'socks4', 'socks5'])
        self.proxy_type.grid(row=0, column=1, sticky=tk.EW, pady=2, padx=(5, 0))
        self.proxy_type.set('http')
        
        # 主机地址
        ttk.Label(main_frame, text="主机地址:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.host_entry = ttk.Entry(main_frame)
        self.host_entry.grid(row=1, column=1, sticky=tk.EW, pady=2, padx=(5, 0))
        
        # 端口
        ttk.Label(main_frame, text="端口:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.port_entry = ttk.Entry(main_frame)
        self.port_entry.grid(row=2, column=1, sticky=tk.EW, pady=2, padx=(5, 0))
        
        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.username_entry = ttk.Entry(main_frame)
        self.username_entry.grid(row=3, column=1, sticky=tk.EW, pady=2, padx=(5, 0))
        
        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.password_entry = ttk.Entry(main_frame, show='*')
        self.password_entry.grid(row=4, column=1, sticky=tk.EW, pady=2, padx=(5, 0))
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="确定", command=self.on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.LEFT, padx=5)
        
        # 设置列权重
        main_frame.columnconfigure(1, weight=1)
    
    def on_ok(self):
        """确定按钮"""
        try:
            # 验证输入
            host = self.host_entry.get().strip()
            port_str = self.port_entry.get().strip()
            
            if not host:
                messagebox.showerror("错误", "请输入主机地址")
                return
            
            if not port_str:
                messagebox.showerror("错误", "请输入端口")
                return
            
            try:
                port = int(port_str)
                if port < 1 or port > 65535:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("错误", "端口必须是1-65535之间的数字")
                return
            
            # 添加代理
            proxy_id = self.proxy_manager.add_proxy(
                self.proxy_type.get(),
                host,
                port,
                self.username_entry.get().strip() or None,
                self.password_entry.get().strip() or None
            )
            
            self.result = proxy_id
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"添加代理失败: {str(e)}")
    
    def on_cancel(self):
        """取消按钮"""
        self.dialog.destroy()
