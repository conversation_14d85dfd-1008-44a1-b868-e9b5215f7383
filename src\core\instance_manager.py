#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实例管理模块
负责管理多个exe实例的启动、停止、监控等
"""

import os
import subprocess
import threading
import time
import uuid
import psutil
from typing import Dict, List, Optional, Union
from datetime import datetime

from .sandbox_manager import SandboxManager
from .proxy_manager import ProxyManager
from .system_modifier import SystemModifier
from ..utils.process_utils import create_isolated_executable, cleanup_isolated_executable, get_process_isolation_env
from ..utils.app_specific import get_app_specific_args, get_app_specific_env, should_use_isolated_exe, get_app_display_name
from ..utils.mutex_bypass import launch_with_full_isolation, cleanup_isolation, should_use_full_isolation
from ..utils.qingtalk_optimized import launch_qingtalk_optimized, cleanup_qingtalk_optimized, is_qingtalk_app

class Instance:
    def __init__(self, instance_id: str, exe_path: str, proxy_config: Dict = None, custom_name: str = None):
        self.id = instance_id
        self.exe_path = exe_path
        self.proxy_config = proxy_config or {}
        self.process = None
        self.sandbox_manager = None
        self.system_modifier = None
        self.status = "stopped"  # stopped, starting, running, stopping
        self.created_time = datetime.now()
        self.start_time = None
        self.pid = None
        self.work_dir = None
        self.isolated_exe_path = None  # 隔离的exe文件路径

        # 生成实例名称
        if custom_name:
            self.name = custom_name
        else:
            self.name = f"{os.path.basename(exe_path).replace('.exe', '')}_{instance_id[:8]}"

        # 简化的代理属性（用于界面显示和编辑）
        self.proxy_ip = proxy_config.get('ip', '') if proxy_config else ''
        self.proxy_port = proxy_config.get('port', '') if proxy_config else ''
        self.proxy_username = proxy_config.get('username', '') if proxy_config else ''
        self.proxy_password = proxy_config.get('password', '') if proxy_config else ''

        # 备注字段
        self.remark = ""

    def start(self):
        """启动实例"""
        try:
            self.status = "starting"
            app_name = get_app_display_name(self.exe_path)
            print(f"开始启动实例 {self.id[:8]} ({app_name})...")

            # 检查是否是QingTalk应用，使用优化方案
            if is_qingtalk_app(self.exe_path):
                print(f"检测到QingTalk应用，使用优化启动方案")
                self.process = launch_qingtalk_optimized(self.exe_path, self.id)

                if self.process:
                    self.pid = self.process.pid
                    self.start_time = datetime.now()
                    self.status = "running"
                    print(f"QingTalk优化启动成功 (PID: {self.pid})")

                    # 验证进程是否真的在运行
                    import time
                    time.sleep(1)  # 等待1秒让进程稳定

                    if self.process.poll() is None:
                        print(f"  ✅ 进程验证成功，PID {self.pid} 正在运行")
                        return True
                    else:
                        print(f"  ❌ 进程验证失败，PID {self.pid} 已退出，返回码: {self.process.returncode}")
                        self.status = "stopped"
                        return False
                else:
                    self.status = "stopped"
                    print(f"QingTalk优化启动失败")
                    return False

            # 检查是否需要完全隔离（针对其他严格的单实例应用）
            elif should_use_full_isolation(self.exe_path):
                print(f"使用完全隔离模式启动 {app_name}")
                self.process = launch_with_full_isolation(self.exe_path, self.id)

                if self.process:
                    self.pid = self.process.pid
                    self.start_time = datetime.now()
                    self.status = "running"
                    print(f"完全隔离启动成功 (PID: {self.pid})")
                    return True
                else:
                    self.status = "stopped"
                    print(f"完全隔离启动失败")
                    return False

            # 标准隔离模式
            # 根据应用类型决定是否使用exe隔离
            if should_use_isolated_exe(self.exe_path):
                self.isolated_exe_path = create_isolated_executable(self.exe_path, self.id)
                if not self.isolated_exe_path:
                    print(f"创建隔离exe失败，使用原始路径")
                    self.isolated_exe_path = self.exe_path
                else:
                    print(f"使用隔离exe: {os.path.basename(self.isolated_exe_path)}")
            else:
                self.isolated_exe_path = self.exe_path
                print(f"使用原始exe路径: {self.exe_path}")

            # 创建隔离的用户数据目录
            import tempfile
            self.user_data_dir = tempfile.mkdtemp(prefix=f"userdata_{self.id[:8]}_")

            # 设置环境变量
            env = os.environ.copy()

            # 添加基本的实例标识
            env['INSTANCE_ID'] = self.id
            env['SANDBOX_MODE'] = '1'

            # 重定向用户数据目录，避免配置文件冲突
            env['APPDATA'] = self.user_data_dir
            env['LOCALAPPDATA'] = self.user_data_dir
            env['USERPROFILE'] = self.user_data_dir
            env['HOMEPATH'] = self.user_data_dir
            env['HOME'] = self.user_data_dir

            # 为Electron应用设置特殊的用户数据目录
            env['ELECTRON_USER_DATA'] = os.path.join(self.user_data_dir, 'electron')
            env['CHROME_USER_DATA'] = os.path.join(self.user_data_dir, 'chrome')

            # 添加进程隔离环境变量
            isolation_env = get_process_isolation_env(self.id)
            env.update(isolation_env)

            # 添加应用特定的环境变量
            app_env = get_app_specific_env(self.exe_path, self.id, self.user_data_dir)
            env.update(app_env)

            # 设置代理（如果有）
            if self.proxy_config and self.proxy_config.get('http_proxy'):
                env['HTTP_PROXY'] = self.proxy_config['http_proxy']
                env['HTTPS_PROXY'] = self.proxy_config['http_proxy']
                print(f"设置代理: {self.proxy_config['http_proxy']}")

            print(f"用户数据目录: {self.user_data_dir}")

            # 获取应用特定的启动参数
            app_args = get_app_specific_args(self.exe_path, self.id, self.user_data_dir)
            if app_args:
                print(f"应用特定参数: {' '.join(app_args)}")

            # 构建完整的命令行
            command = [self.isolated_exe_path] + app_args

            # 改进的启动方式，确保窗口能正常显示
            print(f"启动命令: {' '.join(command)}")

            # 创建启动信息，确保窗口正常显示
            try:
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags = 0x00000001  # STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = 1       # SW_NORMAL

                # 启动进程，使用完整命令行
                self.process = subprocess.Popen(
                    command,
                    env=env,
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )

            except Exception as e:
                print(f"使用完整命令失败，尝试基本方式: {e}")
                # 如果完整命令失败，使用最基本的方式
                self.process = subprocess.Popen(
                    self.isolated_exe_path,
                    env=env,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )

            self.pid = self.process.pid
            self.start_time = datetime.now()
            self.status = "running"

            print(f"实例 {self.id[:8]} 启动成功 (PID: {self.pid})")
            return True

        except Exception as e:
            self.status = "stopped"
            print(f"启动实例 {self.id} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False


    
    def stop(self):
        """停止实例"""
        try:
            self.status = "stopping"
            print(f"开始停止实例 {self.id[:8]}... (PID: {self.pid})")

            if self.process and self.process.poll() is None:
                # 尝试优雅关闭
                print(f"尝试优雅关闭进程 {self.pid}")
                self.process.terminate()

                # 等待进程结束
                try:
                    self.process.wait(timeout=5)
                    print(f"进程 {self.pid} 已优雅关闭")
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    print(f"强制杀死进程 {self.pid}")
                    self.process.kill()
                    self.process.wait()
                    print(f"进程 {self.pid} 已强制关闭")
            else:
                print(f"进程 {self.pid} 已经停止")

            # 使用psutil确保进程真的被关闭
            try:
                import psutil
                if self.pid and psutil.pid_exists(self.pid):
                    proc = psutil.Process(self.pid)

                    # 获取所有子进程
                    children = proc.children(recursive=True)

                    # 先尝试优雅关闭主进程
                    proc.terminate()
                    try:
                        proc.wait(timeout=3)
                        print(f"使用psutil优雅关闭进程 {self.pid}")
                    except psutil.TimeoutExpired:
                        # 强制杀死主进程
                        proc.kill()
                        proc.wait()
                        print(f"使用psutil强制关闭进程 {self.pid}")

                    # 关闭所有子进程
                    for child in children:
                        try:
                            if child.is_running():
                                child.terminate()
                                child.wait(timeout=2)
                                print(f"关闭子进程 {child.pid}")
                        except:
                            try:
                                child.kill()
                                print(f"强制关闭子进程 {child.pid}")
                            except:
                                pass

            except Exception as e:
                print(f"psutil关闭进程失败: {e}")

            # 最后检查进程是否真的关闭了
            try:
                import psutil
                if self.pid and psutil.pid_exists(self.pid):
                    print(f"⚠️ 警告：进程 {self.pid} 仍在运行")
                else:
                    print(f"✅ 确认进程 {self.pid} 已完全关闭")
            except:
                pass

            # 清理用户数据目录
            if hasattr(self, 'user_data_dir') and self.user_data_dir:
                try:
                    import shutil
                    shutil.rmtree(self.user_data_dir, ignore_errors=True)
                    print(f"清理用户数据目录: {self.user_data_dir}")
                except:
                    pass

            # 清理工作目录
            if hasattr(self, 'work_dir') and self.work_dir:
                try:
                    import shutil
                    shutil.rmtree(self.work_dir, ignore_errors=True)
                    print(f"清理工作目录: {self.work_dir}")
                except:
                    pass

            # 清理隔离的exe文件
            if hasattr(self, 'isolated_exe_path') and self.isolated_exe_path != self.exe_path:
                cleanup_isolated_executable(self.isolated_exe_path)

            # 清理隔离环境
            if self.process and hasattr(self.process, 'isolation_info'):
                isolation_info = self.process.isolation_info
                method = isolation_info.get('method', 'unknown')

                if method == 'qingtalk_ultimate':
                    # 使用终极方案清理方法
                    from src.utils.qingtalk_ultimate import cleanup_qingtalk_ultimate
                    cleanup_qingtalk_ultimate(self.process)
                elif method in ['qingtalk_optimized', 'qingtalk_perfect', 'qingtalk_persistent_login', 'qingtalk_final']:
                    # 使用QingTalk专用清理方法（保持登录状态）
                    cleanup_qingtalk_optimized(self.process)
                else:
                    # 使用通用清理方法
                    cleanup_isolation(self.process)

            self.status = "stopped"
            self.process = None
            self.pid = None

            print(f"实例 {self.id[:8]} 停止完成")
            return True

        except Exception as e:
            print(f"停止实例 {self.id} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def is_running(self):
        """检查实例是否在运行"""
        if self.process is None:
            print(f"  实例 {self.id[:8]}: 进程对象为空")
            return False

        try:
            poll_result = self.process.poll()
            if poll_result is None:
                # 进程还在运行
                return True
            else:
                # 进程已退出
                print(f"  实例 {self.id[:8]}: 进程已退出，返回码: {poll_result}")
                self.status = "stopped"
                return False
        except Exception as e:
            print(f"  实例 {self.id[:8]}: 检查进程状态时出错: {e}")
            return False
    
    def get_info(self):
        """获取实例信息"""
        return {
            'id': self.id,
            'exe_path': self.exe_path,
            'status': self.status,
            'pid': self.pid,
            'created_time': self.created_time.isoformat(),
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'proxy_config': self.proxy_config
        }

class InstanceManager:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.instances: Dict[str, Instance] = {}
        self.proxy_manager = ProxyManager(config_manager)
        self.monitor_thread = None
        self.monitoring = False
        
        # 启动监控线程
        self.start_monitoring()
    
    def create_instance(self, exe_path: str, proxy_config: Optional[Dict] = None, custom_name: Optional[str] = None) -> str:
        """创建新实例"""
        # 如果没有指定代理，自动分配一个
        if proxy_config is None:
            proxy_config = self.proxy_manager.get_available_proxy()

        # 确保proxy_config不为None（如果代理管理器也没有可用代理）
        if proxy_config is None:
            proxy_config = {}

        # 生成基于内容的稳定实例ID（支持多个相同配置的实例）
        instance_id = self._generate_stable_instance_id(exe_path, proxy_config)

        instance = Instance(instance_id, exe_path, proxy_config)
        self.instances[instance_id] = instance

        # 保存实例配置
        instance_config = {
            'id': instance_id,
            'exe_path': exe_path,
            'proxy_config': proxy_config
        }
        self.config_manager.add_instance_config(instance_config)

        return instance_id

    def _generate_stable_instance_id(self, exe_path: str, proxy_config: Optional[Dict]) -> str:
        """生成基于内容的稳定实例ID，支持多个相同配置的实例"""
        import hashlib
        import time

        # 处理proxy_config为None的情况
        if proxy_config is None:
            proxy_config = {}

        # 创建基础内容
        base_content_parts = [
            os.path.basename(exe_path),  # 程序名
            proxy_config.get('ip', ''),  # 代理IP
            proxy_config.get('port', ''), # 代理端口
            proxy_config.get('username', ''), # 代理用户名
        ]

        base_content = '|'.join(str(part) for part in base_content_parts)

        # 检查是否已存在相同配置的实例
        base_hash = hashlib.md5(base_content.encode()).hexdigest()[:8]

        # 如果基础ID不存在，直接使用
        if base_hash not in self.instances:
            print(f"生成稳定实例ID: {base_hash} (基于: {base_content})")
            return base_hash

        # 如果基础ID已存在，添加时间戳和序号确保唯一性
        timestamp = str(int(time.time() * 1000))  # 毫秒时间戳
        sequence = 1

        while True:
            # 创建包含时间戳和序号的内容
            unique_content_parts = base_content_parts + [timestamp, str(sequence)]
            unique_content = '|'.join(str(part) for part in unique_content_parts)

            # 生成唯一ID
            unique_hash = hashlib.md5(unique_content.encode()).hexdigest()[:8]

            # 如果这个ID不存在，使用它
            if unique_hash not in self.instances:
                print(f"生成唯一实例ID: {unique_hash} (基于: {base_content} + 时间戳 + 序号{sequence})")
                return unique_hash

            # 如果还是冲突，增加序号
            sequence += 1

            # 防止无限循环（理论上不会发生）
            if sequence > 1000:
                import uuid
                fallback_id = str(uuid.uuid4())[:8]
                print(f"生成随机实例ID: {fallback_id} (序号超限，使用随机ID)")
                return fallback_id
    
    def start_instance(self, instance_id: str) -> bool:
        """启动指定实例"""
        if instance_id not in self.instances:
            return False
        
        instance = self.instances[instance_id]
        return instance.start()
    
    def stop_instance(self, instance_id: str) -> bool:
        """停止指定实例"""
        if instance_id not in self.instances:
            return False
        
        instance = self.instances[instance_id]
        return instance.stop()
    
    def remove_instance(self, instance_id: str) -> bool:
        """删除实例"""
        if instance_id not in self.instances:
            return False
        
        # 先停止实例
        self.stop_instance(instance_id)
        
        # 删除实例
        del self.instances[instance_id]
        
        # 删除配置
        self.config_manager.remove_instance_config(instance_id)
        
        return True
    
    def get_instance(self, instance_id: str) -> Optional[Instance]:
        """获取指定实例"""
        return self.instances.get(instance_id)
    
    def get_all_instances(self) -> List[Instance]:
        """获取所有实例"""
        return list(self.instances.values())
    
    def stop_all_instances(self):
        """停止所有实例"""
        for instance in self.instances.values():
            instance.stop()
    
    def start_monitoring(self):
        """启动监控线程"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_instances, daemon=True)
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控线程"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_instances(self):
        """监控实例状态"""
        while self.monitoring:
            try:
                for instance in self.instances.values():
                    if instance.status == "running":
                        # 检查实例是否刚启动（给新启动的实例一些时间稳定）
                        if hasattr(instance, 'start_time') and instance.start_time:
                            from datetime import datetime, timedelta
                            time_since_start = datetime.now() - instance.start_time
                            if time_since_start < timedelta(seconds=10):
                                # 新启动的实例，跳过检查
                                continue

                        # 检查进程是否还在运行
                        if not instance.is_running():
                            print(f"检测到实例 {instance.id[:8]} 已停止")
                            # 添加详细的调试信息
                            if instance.process:
                                print(f"  进程对象存在，PID: {instance.pid}")
                                print(f"  进程返回码: {instance.process.returncode}")
                            else:
                                print(f"  进程对象为空")

                time.sleep(5)  # 改为每5秒检查一次，减少频率

            except Exception as e:
                print(f"监控实例时出错: {e}")
                import traceback
                traceback.print_exc()
                time.sleep(10)
