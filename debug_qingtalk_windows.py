#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试QingTalk窗口信息
"""

import os
import sys

def debug_qingtalk_windows():
    """调试QingTalk窗口信息"""
    try:
        import psutil
        import win32gui
        
        print("🔍 查找所有QingTalk相关进程...")
        
        # 查找QingTalk进程
        qingtalk_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and 'qingtalk' in proc.info['name'].lower():
                    qingtalk_processes.append(proc.info)
                    print(f"  找到进程: {proc.info['pid']} - {proc.info['name']} - {proc.info['exe']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if not qingtalk_processes:
            print("❌ 没有找到QingTalk进程")
            return
        
        print(f"\n🔍 查找 {len(qingtalk_processes)} 个QingTalk进程的窗口...")
        
        # 获取所有QingTalk进程的PID
        qingtalk_pids = [proc['pid'] for proc in qingtalk_processes]
        
        # 查找所有窗口
        all_windows = []
        
        def enum_windows_callback(hwnd, data):
            try:
                _, window_pid = win32gui.GetWindowThreadProcessId(hwnd)
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                is_visible = win32gui.IsWindowVisible(hwnd)
                
                # 获取窗口大小
                try:
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                except:
                    width = height = 0
                
                window_info = {
                    'hwnd': hwnd,
                    'pid': window_pid,
                    'title': window_text,
                    'class': class_name,
                    'visible': is_visible,
                    'width': width,
                    'height': height
                }
                
                # 收集所有窗口，然后分类
                all_windows.append(window_info)
                
            except Exception as e:
                pass
            return True
        
        win32gui.EnumWindows(enum_windows_callback, None)

        # 过滤QingTalk相关窗口
        qingtalk_windows = []
        for window in all_windows:
            if (window['pid'] in qingtalk_pids or
                'qingtalk' in window['title'].lower() or
                'qingtalk' in window['class'].lower() or
                '钉钉' in window['title'] or
                'dingtalk' in window['title'].lower() or
                'dingtalk' in window['class'].lower()):
                qingtalk_windows.append(window)

        print(f"\n📋 所有窗口: {len(all_windows)} 个")
        print(f"📋 QingTalk相关窗口: {len(qingtalk_windows)} 个")
        print("-" * 80)

        # 显示QingTalk相关窗口
        for i, window in enumerate(qingtalk_windows):
            print(f"QingTalk窗口 {i+1}:")
            print(f"  PID: {window['pid']}")
            print(f"  标题: '{window['title']}'")
            print(f"  类名: '{window['class']}'")
            print(f"  可见: {window['visible']}")
            print(f"  大小: {window['width']}x{window['height']}")
            print(f"  句柄: {window['hwnd']}")
            print()

        # 如果没有QingTalk窗口，显示一些可能相关的窗口
        if not qingtalk_windows:
            print("🔍 没有找到明确的QingTalk窗口，显示一些可能相关的窗口:")

            # 查找可能的窗口
            possible_windows = []
            for window in all_windows:
                # 查找QingTalk进程的窗口
                if window['pid'] in qingtalk_pids:
                    possible_windows.append((window, f"QingTalk进程{window['pid']}的窗口"))
                # 查找包含常见关键词的窗口
                elif any(keyword in window['title'].lower() for keyword in ['qt', 'chrome', 'webkit']):
                    if window['visible'] and window['width'] > 100:
                        possible_windows.append((window, "可能的应用窗口"))

            for i, (window, reason) in enumerate(possible_windows[:10]):  # 只显示前10个
                print(f"可能窗口 {i+1} ({reason}):")
                print(f"  PID: {window['pid']}")
                print(f"  标题: '{window['title']}'")
                print(f"  类名: '{window['class']}'")
                print(f"  可见: {window['visible']}")
                print(f"  大小: {window['width']}x{window['height']}")
                print()

        print("-" * 80)
        
        for i, window in enumerate(all_windows):
            print(f"窗口 {i+1}:")
            print(f"  PID: {window['pid']}")
            print(f"  标题: '{window['title']}'")
            print(f"  类名: '{window['class']}'")
            print(f"  可见: {window['visible']}")
            print(f"  大小: {window['width']}x{window['height']}")
            print(f"  句柄: {window['hwnd']}")
            print()
        
        # 分析最佳窗口
        print("🎯 分析最佳激活窗口:")
        
        # 过滤可见窗口
        visible_windows = [w for w in all_windows if w['visible']]
        print(f"  可见窗口: {len(visible_windows)} 个")
        
        # 过滤有标题的窗口
        titled_windows = [w for w in visible_windows if w['title'].strip()]
        print(f"  有标题窗口: {len(titled_windows)} 个")
        
        # 过滤大窗口
        large_windows = [w for w in visible_windows if w['width'] > 300 and w['height'] > 200]
        print(f"  大窗口: {len(large_windows)} 个")
        
        # 推荐激活的窗口
        best_candidates = []
        
        # 优先级1: 标题包含QingTalk的大窗口
        for window in large_windows:
            if 'qingtalk' in window['title'].lower() or '钉钉' in window['title']:
                best_candidates.append((window, "QingTalk标题大窗口"))
        
        # 优先级2: 有标题的大窗口
        if not best_candidates:
            for window in large_windows:
                if window['title'].strip():
                    best_candidates.append((window, "有标题大窗口"))
        
        # 优先级3: 任何大窗口
        if not best_candidates:
            for window in large_windows:
                best_candidates.append((window, "大窗口"))
        
        # 优先级4: 任何可见窗口
        if not best_candidates:
            for window in visible_windows:
                best_candidates.append((window, "可见窗口"))
        
        if best_candidates:
            print(f"\n✅ 推荐激活窗口:")
            for i, (window, reason) in enumerate(best_candidates[:3]):  # 只显示前3个
                print(f"  {i+1}. {reason}")
                print(f"     PID: {window['pid']}, 标题: '{window['title']}', 类名: '{window['class']}'")
                print(f"     大小: {window['width']}x{window['height']}")
        else:
            print("❌ 没有找到合适的激活窗口")
        
        # 测试激活第一个推荐窗口
        if best_candidates:
            test_window = best_candidates[0][0]
            print(f"\n🚀 测试激活窗口: {test_window['title'] or '(无标题)'}")
            
            try:
                import win32con
                hwnd = test_window['hwnd']
                
                # 尝试激活
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                win32gui.SetForegroundWindow(hwnd)
                print("✅ 窗口激活测试成功")
                
            except Exception as e:
                print(f"❌ 窗口激活测试失败: {e}")
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请安装: pip install psutil pywin32")
    except Exception as e:
        print(f"❌ 调试失败: {e}")

def main():
    print("=" * 60)
    print("QingTalk窗口调试工具")
    print("=" * 60)
    print("💡 请确保QingTalk正在运行")
    print()
    
    debug_qingtalk_windows()
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
