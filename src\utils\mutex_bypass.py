#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互斥体绕过工具
用于绕过单实例限制
"""

import os
import subprocess
import tempfile
import shutil
from typing import Optional

def create_isolated_environment(exe_path: str, instance_id: str) -> dict:
    """
    创建完全隔离的运行环境
    """
    try:
        # 创建完整的应用程序副本
        app_dir = os.path.dirname(exe_path)
        app_name = os.path.basename(app_dir)
        
        # 创建隔离目录
        isolated_base = tempfile.mkdtemp(prefix=f"isolated_app_{instance_id[:8]}_")
        isolated_app_dir = os.path.join(isolated_base, app_name)
        
        print(f"创建应用程序副本: {app_dir} -> {isolated_app_dir}")
        
        # 复制整个应用程序目录（这可能需要一些时间）
        shutil.copytree(app_dir, isolated_app_dir, ignore=shutil.ignore_patterns('*.log', '*.tmp'))
        
        # 新的exe路径
        exe_name = os.path.basename(exe_path)
        new_exe_path = os.path.join(isolated_app_dir, exe_name)
        
        # 创建用户数据目录
        user_data_dir = os.path.join(isolated_base, 'userdata')
        os.makedirs(user_data_dir, exist_ok=True)
        
        return {
            'isolated_base': isolated_base,
            'isolated_app_dir': isolated_app_dir,
            'isolated_exe_path': new_exe_path,
            'user_data_dir': user_data_dir
        }
        
    except Exception as e:
        print(f"创建隔离环境失败: {e}")
        return {}

def create_registry_isolation(instance_id: str) -> dict:
    """
    创建注册表隔离
    """
    try:
        # 创建临时注册表文件
        temp_dir = tempfile.mkdtemp(prefix=f"registry_{instance_id[:8]}_")
        
        # 为实例创建唯一的注册表键
        reg_env = {
            'HKEY_CURRENT_USER_OVERRIDE': temp_dir,
            'REGISTRY_ISOLATION': '1',
            'INSTANCE_REGISTRY_ID': instance_id
        }
        
        return reg_env
        
    except Exception as e:
        print(f"创建注册表隔离失败: {e}")
        return {}

def create_mutex_isolation(instance_id: str) -> dict:
    """
    创建互斥体隔离环境变量
    """
    return {
        'MUTEX_INSTANCE_ID': instance_id,
        'SINGLE_INSTANCE_BYPASS': '1',
        'APP_INSTANCE_UNIQUE_ID': instance_id,
        'PROCESS_ISOLATION_MODE': '1'
    }

def launch_with_full_isolation(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    使用轻量级完全隔离启动应用程序
    """
    try:
        print(f"开始轻量级隔离启动: {os.path.basename(exe_path)}")

        # 创建用户数据目录（不复制整个应用）
        user_data_dir = tempfile.mkdtemp(prefix=f"qingtalk_isolated_{instance_id[:8]}_")

        # 设置环境变量
        env = os.environ.copy()

        # 为QingTalk创建专门的隔离目录结构
        appdata_dir = os.path.join(user_data_dir, 'AppData', 'Roaming')
        localappdata_dir = os.path.join(user_data_dir, 'AppData', 'Local')
        temp_dir = os.path.join(user_data_dir, 'Temp')

        # 创建目录结构
        os.makedirs(appdata_dir, exist_ok=True)
        os.makedirs(localappdata_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)

        # 只重定向关键的用户数据路径，保持系统路径不变
        env.update({
            'APPDATA': appdata_dir,
            'LOCALAPPDATA': localappdata_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir
        })

        # 不要重定向 USERPROFILE 和 HOME，这可能导致Electron初始化失败

        # 添加注册表隔离
        reg_env = create_registry_isolation(instance_id)
        env.update(reg_env)

        # 添加互斥体隔离
        mutex_env = create_mutex_isolation(instance_id)
        env.update(mutex_env)

        # 添加QingTalk特定的环境变量
        env.update({
            'INSTANCE_ID': instance_id,
            'SANDBOX_MODE': '1',
            'FULL_ISOLATION': '1',
            'QINGTALK_USER_DATA': user_data_dir,
            'ELECTRON_USER_DATA': localappdata_dir,
            'CHROME_USER_DATA_DIR': localappdata_dir,
            # 尝试绕过单实例检测
            'QINGTALK_MULTI_INSTANCE': '1',
            'DISABLE_SINGLE_INSTANCE': '1',
            'FORCE_MULTI_INSTANCE': '1',
            # 添加更多绕过变量
            'ELECTRON_ENABLE_LOGGING': '1',
            'ELECTRON_DISABLE_SECURITY_WARNINGS': '1',
            'ELECTRON_NO_ATTACH_CONSOLE': '1',
            'NODE_ENV': 'development',
            # 尝试修改进程名相关
            'PROCESS_TYPE': f'qingtalk_{instance_id[:8]}',
            'CHROME_DESKTOP': f'qingtalk_{instance_id[:8]}.desktop',
            # 网络相关
            'HTTP_PROXY': '',
            'HTTPS_PROXY': '',
            'NO_PROXY': 'localhost,127.0.0.1'
        })

        print(f"用户数据目录: {user_data_dir}")
        print(f"APPDATA: {appdata_dir}")
        print(f"LOCALAPPDATA: {localappdata_dir}")

        # 为QingTalk添加特定的启动参数
        command = [exe_path]

        # 添加更多Electron参数来绕过限制
        command.extend([
            f'--user-data-dir={localappdata_dir}',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu-sandbox',
            '--disable-software-rasterizer',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            f'--app-user-model-id=QingTalk_{instance_id[:8]}',
            '--allow-running-insecure-content',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ])

        print(f"启动命令: {' '.join(command)}")

        # 使用完整命令启动
        process = subprocess.Popen(
            command,
            env=env,
            cwd=os.path.dirname(exe_path),  # 使用原始工作目录
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )

        # 保存隔离信息到进程对象
        process.isolation_info = {'user_data_dir': user_data_dir}

        print(f"轻量级隔离启动成功 (PID: {process.pid})")
        return process

    except Exception as e:
        print(f"轻量级隔离启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_isolation(process: subprocess.Popen):
    """
    清理隔离环境
    """
    try:
        if hasattr(process, 'isolation_info'):
            isolation_info = process.isolation_info

            # 清理用户数据目录
            user_data_dir = isolation_info.get('user_data_dir')
            if user_data_dir and os.path.exists(user_data_dir):
                print(f"清理隔离环境: {user_data_dir}")
                shutil.rmtree(user_data_dir, ignore_errors=True)

            # 清理完整隔离环境（如果存在）
            isolated_base = isolation_info.get('isolated_base')
            if isolated_base and os.path.exists(isolated_base):
                print(f"清理完整隔离环境: {isolated_base}")
                shutil.rmtree(isolated_base, ignore_errors=True)

    except Exception as e:
        print(f"清理隔离环境失败: {e}")

def should_use_full_isolation(exe_path: str) -> bool:
    """
    判断是否应该使用完全隔离
    """
    exe_name = os.path.basename(exe_path).lower()
    
    # 这些应用需要完全隔离
    full_isolation_apps = [
        'qingtalk.exe',
        'dingtalk.exe'
    ]
    
    return exe_name in full_isolation_apps
