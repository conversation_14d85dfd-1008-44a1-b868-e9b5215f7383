#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk检测机制专业分析工具
基于错误信息和行为模式进行深度分析
"""

import os
import subprocess
import time
import json
import psutil
import tempfile
from datetime import datetime
from typing import Dict, List

class QingTalkDetective:
    def __init__(self):
        self.qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
        self.analysis_results = {}
        
    def analyze_error_messages(self):
        """分析QingTalk的错误信息"""
        print("🔍 分析QingTalk错误信息...")
        
        known_errors = [
            "只能启动一个app4",
            "只能启动一个app", 
            "常青藤域名获取失败",
            "Failed to get 'userData' path",
            "isComponentDamage = true/false"
        ]
        
        analysis = {
            "single_instance_errors": [
                "只能启动一个app4",
                "只能启动一个app"
            ],
            "network_errors": [
                "常青藤域名获取失败"
            ],
            "electron_errors": [
                "Failed to get 'userData' path"
            ],
            "integrity_check": [
                "isComponentDamage = true/false"
            ]
        }
        
        return analysis
    
    def test_file_locking_detection(self):
        """测试文件锁定检测机制"""
        print("🔍 测试文件锁定检测...")
        
        # QingTalk可能使用的锁文件位置
        potential_lock_files = [
            os.path.expanduser("~\\AppData\\Roaming\\QingTalk\\lock"),
            os.path.expanduser("~\\AppData\\Local\\QingTalk\\lock"),
            os.path.expanduser("~\\AppData\\Roaming\\QingTalk\\running.lock"),
            os.path.expanduser("~\\AppData\\Local\\QingTalk\\running.lock"),
            "C:\\ProgramData\\QingTalk\\lock",
            "C:\\temp\\qingtalk.lock"
        ]
        
        found_files = []
        for lock_file in potential_lock_files:
            if os.path.exists(lock_file):
                try:
                    stat = os.stat(lock_file)
                    found_files.append({
                        'path': lock_file,
                        'size': stat.st_size,
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'accessible': True
                    })
                except:
                    found_files.append({
                        'path': lock_file,
                        'accessible': False
                    })
        
        return {
            'potential_lock_files': potential_lock_files,
            'found_files': found_files
        }
    
    def test_named_objects_detection(self):
        """测试命名对象检测（互斥体、事件、信号量等）"""
        print("🔍 测试命名对象检测...")
        
        # 常见的QingTalk命名对象模式
        potential_objects = [
            "Global\\QingTalk",
            "Global\\QingTalk_SingleInstance",
            "Global\\QingTalk_Mutex",
            "Global\\DingTalk",
            "Global\\Taobao_QingTalk",
            "Local\\QingTalk",
            "QingTalk_Running",
            "QingTalk_Instance",
            "Session\\1\\QingTalk"
        ]
        
        # 尝试创建这些命名对象来测试冲突
        test_results = []
        
        for obj_name in potential_objects:
            try:
                # 使用Python的multiprocessing来测试命名对象
                import multiprocessing
                
                # 尝试创建命名信号量
                try:
                    sem = multiprocessing.Semaphore(name=obj_name)
                    test_results.append({
                        'name': obj_name,
                        'type': 'semaphore',
                        'created': True,
                        'conflict': False
                    })
                    sem.close()
                except:
                    test_results.append({
                        'name': obj_name,
                        'type': 'semaphore',
                        'created': False,
                        'conflict': True
                    })
            except:
                pass
        
        return {
            'potential_objects': potential_objects,
            'test_results': test_results
        }
    
    def test_process_communication(self):
        """测试进程间通信检测"""
        print("🔍 测试进程间通信检测...")
        
        # 检查QingTalk可能使用的端口
        potential_ports = [12345, 23456, 34567, 45678, 56789, 8080, 8888, 9999]
        
        port_status = []
        for port in potential_ports:
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                sock.close()
                
                port_status.append({
                    'port': port,
                    'status': 'open' if result == 0 else 'closed'
                })
            except:
                port_status.append({
                    'port': port,
                    'status': 'error'
                })
        
        return {
            'potential_ports': potential_ports,
            'port_status': port_status
        }
    
    def test_registry_detection_deep(self):
        """深度测试注册表检测"""
        print("🔍 深度测试注册表检测...")
        
        import winreg
        
        # 更具体的QingTalk注册表位置
        specific_keys = [
            (winreg.HKEY_CURRENT_USER, "Software\\QingTalk\\Instance"),
            (winreg.HKEY_CURRENT_USER, "Software\\QingTalk\\Running"),
            (winreg.HKEY_CURRENT_USER, "Software\\QingTalk\\Config"),
            (winreg.HKEY_CURRENT_USER, "Software\\Taobao\\QingTalk"),
            (winreg.HKEY_CURRENT_USER, "Software\\Alibaba\\QingTalk"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\QingTalk"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Taobao\\QingTalk"),
            (winreg.HKEY_CURRENT_USER, "Software\\Classes\\QingTalk"),
        ]
        
        found_keys = []
        for root_key, path in specific_keys:
            try:
                with winreg.OpenKey(root_key, path) as key:
                    # 获取所有值
                    values = []
                    i = 0
                    while True:
                        try:
                            name, data, reg_type = winreg.EnumValue(key, i)
                            values.append({
                                'name': name,
                                'data': str(data),
                                'type': reg_type
                            })
                            i += 1
                        except OSError:
                            break
                    
                    found_keys.append({
                        'path': path,
                        'values': values
                    })
            except FileNotFoundError:
                pass
            except PermissionError:
                found_keys.append({
                    'path': path,
                    'error': 'Access Denied'
                })
        
        return {
            'specific_keys': [path for _, path in specific_keys],
            'found_keys': found_keys
        }
    
    def test_startup_sequence(self):
        """测试QingTalk启动序列"""
        print("🔍 测试QingTalk启动序列...")
        
        if not os.path.exists(self.qingtalk_exe):
            return {'error': 'QingTalk executable not found'}
        
        # 启动QingTalk并监控前几秒的行为
        startup_log = []
        
        try:
            # 启动进程
            process = subprocess.Popen(
                self.qingtalk_exe,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            startup_log.append({
                'time': 0,
                'event': 'process_started',
                'pid': process.pid
            })
            
            # 监控前10秒
            for i in range(10):
                time.sleep(1)
                
                # 检查进程状态
                if process.poll() is not None:
                    startup_log.append({
                        'time': i + 1,
                        'event': 'process_terminated',
                        'return_code': process.returncode
                    })
                    break
                else:
                    startup_log.append({
                        'time': i + 1,
                        'event': 'process_running',
                        'pid': process.pid
                    })
            
            # 如果进程还在运行，终止它
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=5)
                startup_log.append({
                    'time': 10,
                    'event': 'process_terminated_by_test',
                    'return_code': process.returncode
                })
            
            # 获取输出
            stdout, stderr = process.communicate(timeout=5)
            
            return {
                'startup_log': startup_log,
                'stdout': stdout.decode('utf-8', errors='ignore') if stdout else '',
                'stderr': stderr.decode('utf-8', errors='ignore') if stderr else ''
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'startup_log': startup_log
            }
    
    def run_comprehensive_detection_analysis(self):
        """运行综合检测分析"""
        print("=" * 60)
        print("QingTalk检测机制专业分析")
        print("=" * 60)
        
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'error_analysis': self.analyze_error_messages(),
            'file_locking': self.test_file_locking_detection(),
            'named_objects': self.test_named_objects_detection(),
            'process_communication': self.test_process_communication(),
            'registry_deep': self.test_registry_detection_deep(),
            'startup_sequence': self.test_startup_sequence()
        }
        
        return self.analysis_results
    
    def generate_detection_report(self):
        """生成检测报告"""
        if not self.analysis_results:
            return
        
        print("\n" + "=" * 60)
        print("QingTalk检测机制分析报告")
        print("=" * 60)
        
        # 错误信息分析
        error_analysis = self.analysis_results.get('error_analysis', {})
        print(f"\n🚨 已知错误信息分析:")
        for category, errors in error_analysis.items():
            print(f"  {category}: {len(errors)} 个错误")
            for error in errors:
                print(f"    - {error}")
        
        # 文件锁定分析
        file_locking = self.analysis_results.get('file_locking', {})
        found_files = file_locking.get('found_files', [])
        if found_files:
            print(f"\n📁 发现的锁文件 ({len(found_files)} 个):")
            for file_info in found_files:
                print(f"  - {file_info['path']}")
                if file_info.get('accessible'):
                    print(f"    大小: {file_info.get('size', 'unknown')} 字节")
                    print(f"    修改时间: {file_info.get('modified', 'unknown')}")
        else:
            print(f"\n📁 未发现明显的锁文件")
        
        # 命名对象分析
        named_objects = self.analysis_results.get('named_objects', {})
        test_results = named_objects.get('test_results', [])
        conflicts = [r for r in test_results if r.get('conflict')]
        if conflicts:
            print(f"\n🔒 发现命名对象冲突 ({len(conflicts)} 个):")
            for conflict in conflicts:
                print(f"  - {conflict['name']} ({conflict['type']})")
        else:
            print(f"\n🔒 未发现命名对象冲突")
        
        # 进程通信分析
        process_comm = self.analysis_results.get('process_communication', {})
        open_ports = [p for p in process_comm.get('port_status', []) if p['status'] == 'open']
        if open_ports:
            print(f"\n🌐 发现开放端口 ({len(open_ports)} 个):")
            for port in open_ports:
                print(f"  - 端口 {port['port']}")
        else:
            print(f"\n🌐 未发现相关开放端口")
        
        # 注册表深度分析
        registry_deep = self.analysis_results.get('registry_deep', {})
        found_keys = registry_deep.get('found_keys', [])
        if found_keys:
            print(f"\n📋 发现的注册表键 ({len(found_keys)} 个):")
            for key_info in found_keys:
                if 'error' in key_info:
                    print(f"  - {key_info['path']}: {key_info['error']}")
                else:
                    print(f"  - {key_info['path']}: {len(key_info['values'])} 个值")
        else:
            print(f"\n📋 未发现特定的注册表键")
        
        # 启动序列分析
        startup = self.analysis_results.get('startup_sequence', {})
        if 'error' not in startup:
            startup_log = startup.get('startup_log', [])
            print(f"\n🚀 启动序列分析 ({len(startup_log)} 个事件):")
            for event in startup_log:
                print(f"  {event['time']}s: {event['event']}")
                if 'return_code' in event:
                    print(f"      返回码: {event['return_code']}")
            
            # 输出分析
            stderr = startup.get('stderr', '')
            if stderr:
                print(f"\n📝 错误输出:")
                for line in stderr.split('\n')[:5]:  # 显示前5行
                    if line.strip():
                        print(f"  {line}")
        else:
            print(f"\n🚀 启动序列测试失败: {startup['error']}")
    
    def save_results(self, filename=None):
        """保存分析结果"""
        if not filename:
            filename = f"qingtalk_detective_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细分析结果已保存到: {filename}")
        return filename

def run_qingtalk_detective():
    """运行QingTalk检测分析"""
    detective = QingTalkDetective()
    
    print("QingTalk检测机制专业分析工具")
    print("基于已知错误信息和行为模式进行深度分析")
    
    # 运行分析
    results = detective.run_comprehensive_detection_analysis()
    
    # 生成报告
    detective.generate_detection_report()
    
    # 保存结果
    filename = detective.save_results()
    
    return filename, results

if __name__ == "__main__":
    run_qingtalk_detective()
