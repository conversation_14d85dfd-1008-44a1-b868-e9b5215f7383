#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力刷新API和WMI缓存
专门解决GetComputerNameEx和WMI查询仍显示原始名称的问题
"""

import ctypes
from ctypes import wintypes
import os
import sys
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_current_status():
    """检查当前状态"""
    print("🔍 检查当前API和WMI状态...")
    
    # GetComputerNameEx
    try:
        size = wintypes.DWORD(0)
        ctypes.windll.kernel32.GetComputerNameExW(0, None, ctypes.byref(size))
        buffer = ctypes.create_unicode_buffer(size.value)
        success = ctypes.windll.kernel32.GetComputerNameExW(0, buffer, ctypes.byref(size))
        if success:
            api_name = buffer.value
        else:
            api_name = 'Failed'
    except:
        api_name = 'Error'
    
    # WMI查询
    try:
        import wmi
        c = wmi.WMI()
        for computer in c.Win32_ComputerSystem():
            wmi_name = computer.Name
            break
        else:
            wmi_name = 'Not Found'
    except:
        wmi_name = 'Error'
    
    # 环境变量
    env_name = os.environ.get('COMPUTERNAME', 'Unknown')
    
    print(f"  API GetComputerNameEx: {api_name}")
    print(f"  WMI ComputerSystem: {wmi_name}")
    print(f"  环境变量: {env_name}")
    
    return api_name, wmi_name, env_name

def force_restart_wmi_service():
    """强制重启WMI服务"""
    print("🔄 强制重启WMI服务...")
    
    try:
        # 停止WMI服务
        print("  停止WMI服务...")
        result = subprocess.run(['net', 'stop', 'Winmgmt', '/y'], 
                               capture_output=True, timeout=30, check=False)
        if result.returncode == 0:
            print("  ✅ WMI服务已停止")
        else:
            print(f"  ⚠️ WMI服务停止失败: {result.stderr.decode('gbk', errors='ignore')}")
        
        # 等待服务完全停止
        time.sleep(3)
        
        # 启动WMI服务
        print("  启动WMI服务...")
        result = subprocess.run(['net', 'start', 'Winmgmt'], 
                               capture_output=True, timeout=30, check=False)
        if result.returncode == 0:
            print("  ✅ WMI服务已启动")
        else:
            print(f"  ⚠️ WMI服务启动失败: {result.stderr.decode('gbk', errors='ignore')}")
        
        # 等待服务完全启动
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"  ❌ WMI服务重启失败: {e}")
        return False

def force_refresh_computer_name_cache():
    """强制刷新计算机名缓存"""
    print("🔄 强制刷新计算机名缓存...")
    
    try:
        # 获取当前环境变量中的计算机名
        new_computer_name = os.environ.get('COMPUTERNAME', 'Unknown')
        
        if new_computer_name == 'Unknown':
            print("  ❌ 无法获取新的计算机名")
            return False
        
        print(f"  🎯 目标计算机名: {new_computer_name}")
        
        # 方式1: 多次调用SetComputerNameExW
        name_types = [
            (0, "NetBIOS"),
            (1, "DnsHostname"), 
            (4, "PhysicalNetBIOS"),
            (5, "PhysicalDnsHostname")
        ]
        
        for name_type, type_name in name_types:
            try:
                success = ctypes.windll.kernel32.SetComputerNameExW(name_type, new_computer_name)
                if success:
                    print(f"    ✅ SetComputerNameExW({type_name}) 成功")
                else:
                    print(f"    ⚠️ SetComputerNameExW({type_name}) 失败")
            except Exception as e:
                print(f"    ❌ SetComputerNameExW({type_name}) 异常: {e}")
        
        # 方式2: 调用SetComputerNameW
        try:
            success = ctypes.windll.kernel32.SetComputerNameW(new_computer_name)
            if success:
                print(f"    ✅ SetComputerNameW 成功")
            else:
                print(f"    ⚠️ SetComputerNameW 失败")
        except Exception as e:
            print(f"    ❌ SetComputerNameW 异常: {e}")
        
        # 方式3: 强制刷新系统缓存
        try:
            # 发送系统更改通知
            HWND_BROADCAST = 0xFFFF
            WM_SETTINGCHANGE = 0x001A
            
            result = ctypes.windll.user32.SendMessageTimeoutW(
                HWND_BROADCAST,
                WM_SETTINGCHANGE,
                0,
                "Environment",
                0x0002,  # SMTO_ABORTIFHUNG
                10000,   # 10秒超时
                None
            )
            
            if result:
                print(f"    ✅ 系统更改通知已发送")
            else:
                print(f"    ⚠️ 系统更改通知发送失败")
                
        except Exception as e:
            print(f"    ❌ 系统通知失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 计算机名缓存刷新失败: {e}")
        return False

def force_refresh_all_caches():
    """强制刷新所有缓存"""
    print("🔄 强制刷新所有系统缓存...")
    
    try:
        # 1. 刷新DNS缓存
        subprocess.run(['ipconfig', '/flushdns'], capture_output=True, timeout=10, check=False)
        print("  ✅ DNS缓存已刷新")
        
        # 2. 刷新NetBIOS缓存
        subprocess.run(['nbtstat', '-R'], capture_output=True, timeout=10, check=False)
        print("  ✅ NetBIOS缓存已刷新")
        
        # 3. 刷新ARP缓存
        subprocess.run(['arp', '-d', '*'], capture_output=True, timeout=10, check=False)
        print("  ✅ ARP缓存已刷新")
        
        # 4. 刷新注册表缓存
        try:
            result = ctypes.windll.advapi32.RegFlushKey(0x80000002)  # HKEY_LOCAL_MACHINE
            if result == 0:
                print("  ✅ 注册表缓存已刷新")
        except:
            pass
        
    except Exception as e:
        print(f"  ❌ 缓存刷新失败: {e}")

def main():
    """主函数"""
    print("💪 强力API和WMI缓存刷新工具")
    print("=" * 60)
    print("专门解决GetComputerNameEx和WMI查询缓存问题")
    print("=" * 60)
    
    # 检查管理员权限
    if not ctypes.windll.shell32.IsUserAnAdmin():
        print("❌ 需要管理员权限来运行此工具")
        return
    
    # 检查当前状态
    print("\n📋 修改前状态:")
    api_before, wmi_before, env_before = check_current_status()
    
    # 执行设备修改
    try:
        from src.utils.device_modifier import DeviceModifier
        
        modifier = DeviceModifier()
        
        print(f"\n🔧 执行设备修改...")
        success = modifier.modify_computer_name_only()
        
        if not success:
            print("❌ 设备修改失败")
            return
        
        print(f"\n📋 修改后状态（刷新前）:")
        api_after, wmi_after, env_after = check_current_status()
        
        # 如果API和WMI仍未变化，执行强力刷新
        if api_after == api_before or wmi_after == wmi_before:
            print(f"\n💪 检测到API或WMI未变化，执行强力刷新...")
            
            # 1. 强制刷新计算机名缓存
            force_refresh_computer_name_cache()
            
            # 2. 强制重启WMI服务
            force_restart_wmi_service()
            
            # 3. 强制刷新所有缓存
            force_refresh_all_caches()
            
            # 4. 等待生效
            print(f"\n⏳ 等待15秒让强力刷新生效...")
            time.sleep(15)
            
            # 5. 再次检查状态
            print(f"\n📋 强力刷新后状态:")
            api_final, wmi_final, env_final = check_current_status()
            
            # 分析结果
            print(f"\n🔍 最终分析:")
            if api_final != api_before:
                print(f"  ✅ GetComputerNameEx 已更改: {api_before} -> {api_final}")
            else:
                print(f"  ❌ GetComputerNameEx 仍未变化: {api_final}")
            
            if wmi_final != wmi_before:
                print(f"  ✅ WMI查询 已更改: {wmi_before} -> {wmi_final}")
            else:
                print(f"  ❌ WMI查询 仍未变化: {wmi_final}")
            
            if api_final != api_before or wmi_final != wmi_before:
                print(f"\n🎉 强力刷新成功！现在QingTalk应该会显示新的设备信息")
            else:
                print(f"\n⚠️ 强力刷新后仍未完全生效")
                print(f"💡 可能需要重启系统才能完全生效")
        
        else:
            print(f"\n✅ API和WMI都已更改，无需强力刷新")
        
        # 等待用户测试
        input(f"\n请启动QingTalk测试设备信息，然后按回车键恢复...")
        
        # 恢复
        print(f"\n🔄 恢复原始设备信息...")
        modifier.restore_device_info()
        
        print(f"\n✅ 强力刷新测试完成！")
        
    except Exception as e:
        print(f"\n❌ 强力刷新过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
