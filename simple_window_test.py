#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的窗口测试
"""

def test_window_enumeration():
    """测试窗口枚举"""
    try:
        import win32gui
        
        print("🔍 测试窗口枚举功能...")
        
        window_count = 0
        
        def enum_callback(hwnd, data):
            nonlocal window_count
            try:
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                is_visible = win32gui.IsWindowVisible(hwnd)
                
                window_count += 1
                
                # 只显示前10个窗口
                if window_count <= 10:
                    print(f"  窗口 {window_count}: '{window_text}' [{class_name}] - 可见:{is_visible}")
                
            except Exception as e:
                print(f"  枚举窗口出错: {e}")
            
            return True
        
        win32gui.EnumWindows(enum_callback, None)
        
        print(f"\n✅ 总共找到 {window_count} 个窗口")
        
        if window_count > 0:
            print("✅ win32gui工作正常")
            return True
        else:
            print("❌ win32gui可能有问题")
            return False
            
    except ImportError:
        print("❌ win32gui未安装")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_process_enumeration():
    """测试进程枚举"""
    try:
        import psutil
        
        print("\n🔍 测试进程枚举功能...")
        
        process_count = 0
        qingtalk_count = 0
        
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                process_count += 1
                if proc.info['name'] and 'qingtalk' in proc.info['name'].lower():
                    qingtalk_count += 1
                    print(f"  QingTalk进程: {proc.info['pid']} - {proc.info['name']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        print(f"\n✅ 总共找到 {process_count} 个进程")
        print(f"✅ 其中QingTalk进程: {qingtalk_count} 个")
        
        return True
        
    except ImportError:
        print("❌ psutil未安装")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_specific_pid_windows():
    """测试特定PID的窗口"""
    try:
        import win32gui
        import psutil
        
        print("\n🔍 测试特定PID的窗口查找...")
        
        # 找一个QingTalk进程
        qingtalk_pid = None
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] and 'qingtalk' in proc.info['name'].lower():
                    qingtalk_pid = proc.info['pid']
                    print(f"  测试PID: {qingtalk_pid}")
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if not qingtalk_pid:
            print("❌ 没有找到QingTalk进程")
            return False
        
        # 查找该PID的窗口
        found_windows = []
        
        def enum_callback(hwnd, data):
            try:
                _, window_pid = win32gui.GetWindowThreadProcessId(hwnd)
                if window_pid == qingtalk_pid:
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    is_visible = win32gui.IsWindowVisible(hwnd)
                    
                    found_windows.append({
                        'title': window_text,
                        'class': class_name,
                        'visible': is_visible
                    })
                    
                    print(f"    找到窗口: '{window_text}' [{class_name}] - 可见:{is_visible}")
            except Exception as e:
                pass
            return True
        
        win32gui.EnumWindows(enum_callback, None)
        
        print(f"  PID {qingtalk_pid} 的窗口数: {len(found_windows)}")
        
        return len(found_windows) > 0
        
    except ImportError:
        print("❌ 缺少依赖")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_window_activation():
    """测试窗口激活"""
    try:
        import win32gui
        import win32con
        
        print("\n🔍 测试窗口激活功能...")
        
        # 找一个可见窗口进行测试
        test_hwnd = None
        
        def find_test_window(hwnd, data):
            nonlocal test_hwnd
            try:
                window_text = win32gui.GetWindowText(hwnd)
                if win32gui.IsWindowVisible(hwnd) and window_text.strip():
                    test_hwnd = hwnd
                    print(f"  找到测试窗口: '{window_text}'")
                    return False  # 停止枚举
            except:
                pass
            return True
        
        win32gui.EnumWindows(find_test_window, None)
        
        if test_hwnd:
            try:
                # 尝试激活
                win32gui.ShowWindow(test_hwnd, win32con.SW_RESTORE)
                win32gui.SetForegroundWindow(test_hwnd)
                print("  ✅ 窗口激活测试成功")
                return True
            except Exception as e:
                print(f"  ❌ 窗口激活失败: {e}")
                return False
        else:
            print("  ❌ 没有找到测试窗口")
            return False
            
    except ImportError:
        print("❌ win32gui未安装")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("简单窗口功能测试")
    print("=" * 60)
    
    tests = [
        ("窗口枚举", test_window_enumeration),
        ("进程枚举", test_process_enumeration),
        ("特定PID窗口", test_specific_pid_windows),
        ("窗口激活", test_window_activation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"\n通过率: {success_count}/{total_count}")
    
    if success_count >= 2:
        print("\n💡 基本功能正常，可能是QingTalk窗口的特殊性")
    else:
        print("\n⚠️ 基础功能有问题，需要检查环境")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
