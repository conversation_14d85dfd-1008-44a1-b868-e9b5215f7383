#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责程序配置的加载、保存和管理
"""

import json
import os
import configparser
from typing import Dict, List, Any
from datetime import datetime

class ConfigManager:
    def __init__(self):
        self.config_dir = "config"
        self.config_file = os.path.join(self.config_dir, "app_config.json")
        self.instances_file = os.path.join(self.config_dir, "instances.json")
        self.proxies_file = os.path.join(self.config_dir, "proxies.json")
        
        # 默认配置
        self.default_config = {
            "app": {
                "window_width": 1200,
                "window_height": 800,
                "theme": "default",
                "auto_save": True,
                "max_instances": 10
            },
            "sandbox": {
                "enable_isolation": True,
                "temp_dir": "temp",
                "log_level": "INFO"
            },
            "proxy": {
                "enable_proxy": True,
                "proxy_type": "http",
                "timeout": 30
            }
        }
        
        self.config = self.default_config.copy()
        self.instances_config = []
        self.proxies_config = []
        
        # 确保配置目录存在
        self._ensure_config_dir()
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def load_config(self):
        """加载配置文件"""
        try:
            # 加载主配置
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self._merge_config(self.config, loaded_config)
            
            # 加载实例配置
            if os.path.exists(self.instances_file):
                with open(self.instances_file, 'r', encoding='utf-8') as f:
                    self.instances_config = json.load(f)
            
            # 加载代理配置
            if os.path.exists(self.proxies_file):
                with open(self.proxies_file, 'r', encoding='utf-8') as f:
                    self.proxies_config = json.load(f)
                    
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 保存主配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            # 保存实例配置
            with open(self.instances_file, 'w', encoding='utf-8') as f:
                json.dump(self.instances_config, f, indent=4, ensure_ascii=False)
            
            # 保存代理配置
            with open(self.proxies_file, 'w', encoding='utf-8') as f:
                json.dump(self.proxies_config, f, indent=4, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _merge_config(self, default: Dict, loaded: Dict):
        """合并配置，保持默认值"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
    
    def get_config(self, section: str = None, key: str = None):
        """获取配置值"""
        if section is None:
            return self.config
        
        if section not in self.config:
            return None
            
        if key is None:
            return self.config[section]
            
        return self.config[section].get(key)
    
    def set_config(self, section: str, key: str, value: Any):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
    
    def add_instance_config(self, instance_config: Dict):
        """添加实例配置"""
        instance_config['created_time'] = datetime.now().isoformat()
        self.instances_config.append(instance_config)
    
    def remove_instance_config(self, instance_id: str):
        """删除实例配置"""
        self.instances_config = [
            config for config in self.instances_config 
            if config.get('id') != instance_id
        ]
    
    def get_instance_configs(self) -> List[Dict]:
        """获取所有实例配置"""
        return self.instances_config
    
    def add_proxy_config(self, proxy_config: Dict):
        """添加代理配置"""
        proxy_config['created_time'] = datetime.now().isoformat()
        self.proxies_config.append(proxy_config)
    
    def remove_proxy_config(self, proxy_id: str):
        """删除代理配置"""
        self.proxies_config = [
            config for config in self.proxies_config 
            if config.get('id') != proxy_id
        ]
    
    def get_proxy_configs(self) -> List[Dict]:
        """获取所有代理配置"""
        return self.proxies_config
